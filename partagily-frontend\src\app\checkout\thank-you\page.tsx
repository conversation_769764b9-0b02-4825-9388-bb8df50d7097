'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { CheckCircle, ArrowRight, ShoppingBag } from 'lucide-react';
import '@/styles/checkout.css';

export default function ThankYouPage() {
  const router = useRouter();

  useEffect(() => {
    // Check if the user came from a checkout process
    // This is a simple check - in a real app, you might want to use a more robust solution
    const hasOrderId = sessionStorage.getItem('lastOrderId');

    if (!hasOrderId) {
      // If no order ID is found, redirect to dashboard
      // This prevents users from accessing the thank you page directly
      // router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
      <div className="checkout-header pt-8 mb-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white text-center mb-4">
          Order Confirmation
        </h1>
      </div>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl mx-auto bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden checkout-content"
      >
        <div className="p-12 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="w-24 h-24 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-8"
          >
            <CheckCircle size={56} className="text-green-600 dark:text-green-400" />
          </motion.div>

          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            Thank You for Your Purchase!
          </h2>

          <p className="text-gray-600 dark:text-gray-300 mb-10 max-w-md mx-auto text-lg">
            Your payment has been successfully processed. You now have access to the tools and plans you purchased.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/dashboard"
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg shadow-md hover:shadow-lg"
            >
              Go to Dashboard <ArrowRight size={18} className="ml-3" />
            </Link>

            <Link
              href="/dashboard/orders"
              className="bg-white dark:bg-slate-700 hover:bg-gray-100 dark:hover:bg-slate-600 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg"
            >
              View Order History <ShoppingBag size={18} className="ml-3" />
            </Link>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-slate-700/50 p-8 text-center">
          <p className="text-base text-gray-600 dark:text-gray-400">
            If you have any questions about your purchase, please contact our support team.
          </p>
        </div>
      </motion.div>
      <div className="checkout-footer"></div>
    </div>
  );
}
