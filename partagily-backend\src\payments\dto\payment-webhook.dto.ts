import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsEnum, IsObject, IsOptional } from 'class-validator';

export enum WebhookEventType {
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_PENDING = 'payment.pending',
  PAYMENT_REFUNDED = 'payment.refunded',
}

export class PaymentWebhookDto {
  @ApiProperty({
    description: 'The payment ID from the payment gateway',
    example: 'pay_123456789',
  })
  @IsString()
  paymentId: string;

  @ApiProperty({
    description: 'The event type',
    enum: WebhookEventType,
    example: WebhookEventType.PAYMENT_COMPLETED,
  })
  @IsEnum(WebhookEventType)
  event: WebhookEventType;

  @ApiProperty({
    description: 'The payment status',
    example: 'completed',
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'The payment amount',
    example: 19.99,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'The payment currency',
    example: 'TND',
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Additional metadata',
    example: { userId: '123', toolId: '456', planId: '789' },
    required: false,
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'The timestamp of the event',
    example: '2023-01-01T12:00:00Z',
  })
  @IsString()
  timestamp: string;
}
