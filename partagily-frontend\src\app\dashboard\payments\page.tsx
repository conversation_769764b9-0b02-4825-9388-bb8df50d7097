'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import OrderHistory from '@/components/dashboard/OrderHistory';
import PaymentMethods from '@/components/dashboard/PaymentMethods';
import PaymentSettings from '@/components/dashboard/PaymentSettings';

export default function PaymentsPage() {
  const [activeTab, setActiveTab] = useState<'methods' | 'history' | 'settings'>('methods');

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Payments 💳</h1>
        <p className="text-gray-600">
          Manage your payment methods, view transaction history, and update payment settings.
        </p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm mb-6">
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('methods')}
            className={`flex-1 py-4 px-6 text-center font-medium transition-colors duration-200 ${
              activeTab === 'methods'
                ? 'text-yellow-500 border-b-2 border-yellow-500'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Payment Methods
          </button>
          <button
            onClick={() => setActiveTab('history')}
            className={`flex-1 py-4 px-6 text-center font-medium transition-colors duration-200 ${
              activeTab === 'history'
                ? 'text-yellow-500 border-b-2 border-yellow-500'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Payment History
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`flex-1 py-4 px-6 text-center font-medium transition-colors duration-200 ${
              activeTab === 'settings'
                ? 'text-yellow-500 border-b-2 border-yellow-500'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Payment Settings
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.2 }}
      >
        {activeTab === 'methods' && <PaymentMethods />}
        {activeTab === 'history' && <OrderHistory />}
        {activeTab === 'settings' && <PaymentSettings />}
      </motion.div>
    </div>
  );
}
