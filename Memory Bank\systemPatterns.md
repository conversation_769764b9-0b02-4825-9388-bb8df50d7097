# Partagily System Patterns

## Architecture
- Multi-component system with frontend, backend, and browser extension
- RESTful API communication between components
- JWT-based authentication
- Role-based access control
- Microservices architecture for backend modules
- Event-driven communication for real-time updates
- Secure cookie management and encryption

## Design Patterns
- Component-based UI architecture (React)
- Repository pattern for data access
- Dependency injection in NestJS
- Observer pattern for extension events
- Factory pattern for creating tool instances
- Strategy pattern for subscription tiers
- Singleton pattern for extension service workers
- Adapter pattern for third-party integrations
