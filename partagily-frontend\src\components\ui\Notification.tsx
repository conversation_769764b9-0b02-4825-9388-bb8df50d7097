import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface NotificationProps {
  type: NotificationType;
  message: string;
  isVisible: boolean;
  onClose: () => void;
  autoClose?: boolean;
  autoCloseTime?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const Notification: React.FC<NotificationProps> = ({
  type,
  message,
  isVisible,
  onClose,
  autoClose = true,
  autoCloseTime,
  action,
}) => {
  // Set different auto-close times based on notification type
  const getAutoCloseTime = () => {
    if (autoCloseTime) return autoCloseTime;

    switch (type) {
      case 'error':
        return 6000; // 6 seconds for errors
      case 'warning':
        return 6000; // 6 seconds for warnings
      case 'success':
        return 6000;  // 6 seconds for success
      default:
        return 6000;  // 6 seconds default
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isVisible && autoClose && !action) { // Don't auto-close if there's an action button
      const closeTime = getAutoCloseTime();
      console.log(`Setting notification to close in ${closeTime}ms`);

      timer = setTimeout(() => {
        onClose();
      }, closeTime);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isVisible, autoClose, onClose, action]);

  const getEmoji = () => {
    switch (type) {
      case 'success':
        return '🎉';
      case 'error':
        return '😕';
      case 'warning':
        return '⚠️';
      case 'info':
        return '💡';
      default:
        return '📢';
    }
  };

  const getStyles = () => {
    const baseStyles = 'rounded-xl shadow-2xl max-w-md backdrop-blur-sm';

    switch (type) {
      case 'success':
        return `${baseStyles} bg-gradient-to-r from-green-400 to-teal-500 text-white`;
      case 'error':
        return `${baseStyles} bg-gradient-to-r from-red-400 to-pink-500 text-white`;
      case 'warning':
        return `${baseStyles} bg-gradient-to-r from-yellow-400 to-orange-500 text-white`;
      case 'info':
        return `${baseStyles} bg-gradient-to-r from-blue-400 to-indigo-500 text-white`;
      default:
        return `${baseStyles} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, x: 100, scale: 0.9 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 100, scale: 0.9 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 20
          }}
          className={`${getStyles()} z-50 shadow-xl`}
        >
          <div className="p-4 flex flex-col">
            <div className="flex items-center mb-2">
              <span className="text-2xl mr-3">{getEmoji()}</span>
              <h3 className="font-bold text-lg flex-1">
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </h3>
              <button
                onClick={onClose}
                className="text-white/80 hover:text-white focus:outline-none transition-colors"
                aria-label="Close notification"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <p className="text-white/90 mb-3">{message}</p>

            {action && (
              <div className="flex justify-end mt-1">
                <button
                  onClick={() => {
                    action.onClick();
                    onClose();
                  }}
                  className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-white font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
                >
                  {action.label}
                </button>
              </div>
            )}
          </div>

          {/* Animated progress bar for auto-close */}
          {autoClose && !action && (
            <motion.div
              className="h-1 bg-white/30 rounded-b-xl"
              initial={{ width: "100%" }}
              animate={{ width: "0%" }}
              transition={{ duration: getAutoCloseTime() / 1000, ease: "linear" }}
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Notification;
