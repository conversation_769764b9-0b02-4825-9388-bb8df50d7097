'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

const HeroSection = () => {
  return (
    <section className="py-20 overflow-hidden">
      <div className="container mx-auto px-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-6 terminal-text">
            The shared account platform <img src="/tunisia-flag.svg" alt="Tunisia" className="w-12 h-12 inline-block mx-2" /> with superpowers
          </h1>

          <motion.p
            className="text-xl mb-8 max-w-3xl mx-auto text-gray-700 dark:text-gray-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            A Tunisian-friendly SaaS that works around international payment blocks.
            Get access to premium tools without the payment hassle.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link
              href="/signup"
              className="btn btn-primary px-8 py-3 text-lg flex items-center justify-center gap-2 hover-glow"
            >
              Try it now ⚡️
            </Link>

            <Link
              href="#how-it-works"
              className="btn btn-outline px-8 py-3 text-lg flex items-center justify-center gap-2 hover-scale"
            >
              How it works 🧩
            </Link>
          </motion.div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="mt-12 relative"
        >
          <div className="terminal-container bg-white dark:bg-card-bg p-4 rounded-xl border border-transparent dark:border-gray-700/30 shadow-xl mx-auto max-w-4xl overflow-hidden">
            <div className="terminal-header bg-gray-100 dark:bg-gray-800 rounded-lg p-2 flex gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-red-400"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
              <div className="w-3 h-3 rounded-full bg-green-400"></div>
              <div className="flex-1 text-center mono-text text-xs text-gray-500 dark:text-gray-400">partagily.com</div>
            </div>
            <div className="terminal-content aspect-video bg-gray-900 dark:bg-gray-900 rounded-lg flex items-center justify-center">
              <p className="terminal-text text-green-400 text-xl">
                <span className="text-[#FFAD00]">$</span> Access premium tools with local payment methods...
                <span className="animate-pulse">_</span>
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
