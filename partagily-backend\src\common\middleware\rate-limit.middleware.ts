import { Injectable, NestMiddleware, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

/**
 * Rate Limit Configuration Interface
 */
interface RateLimitConfig {
  windowMs: number;      // Time window in milliseconds
  max: number;           // Maximum number of requests in the time window
  message: string;       // Error message
  statusCode: number;    // HTTP status code for rate limit exceeded
  skipSuccessfulRequests: boolean; // Whether to skip counting successful requests
  keyGenerator: (req: Request) => string; // Function to generate a unique key for each client
}

/**
 * Rate Limit Store Interface
 */
interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
    blocked: boolean;
    blockExpires?: number;
  };
}

/**
 * Rate Limiting Middleware
 * 
 * This middleware implements rate limiting to protect the API from abuse.
 */
@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  private store: RateLimitStore = {};
  private config: RateLimitConfig;
  private blockList: Set<string> = new Set();
  private readonly cleanupInterval = 60 * 60 * 1000; // 1 hour
  
  constructor(private configService: ConfigService) {
    // Default configuration
    this.config = {
      windowMs: this.configService.get<number>('RATE_LIMIT_WINDOW_MS') || 15 * 60 * 1000, // 15 minutes
      max: this.configService.get<number>('RATE_LIMIT_MAX') || 100, // 100 requests per windowMs
      message: 'Too many requests, please try again later.',
      statusCode: HttpStatus.TOO_MANY_REQUESTS,
      skipSuccessfulRequests: false,
      keyGenerator: (req: Request) => {
        // Use X-Forwarded-For header if available (for proxies), otherwise use IP
        return (
          (req.headers['x-forwarded-for'] as string) || 
          req.ip || 
          req.connection.remoteAddress || 
          'unknown'
        ).split(',')[0].trim();
      },
    };
    
    // Start cleanup interval
    setInterval(() => this.cleanup(), this.cleanupInterval);
  }
  
  /**
   * Process the request
   */
  async use(req: Request, res: Response, next: NextFunction) {
    const key = this.config.keyGenerator(req);
    
    // Check if the client is in the block list
    if (this.blockList.has(key)) {
      return this.rejectRequest(res, 'Your IP has been blocked due to excessive requests. Please contact support.');
    }
    
    // Initialize or get client data
    if (!this.store[key]) {
      this.store[key] = {
        count: 0,
        resetTime: Date.now() + this.config.windowMs,
        blocked: false,
      };
    }
    
    const client = this.store[key];
    
    // Reset count if the time window has passed
    if (Date.now() > client.resetTime) {
      client.count = 0;
      client.resetTime = Date.now() + this.config.windowMs;
      client.blocked = false;
    }
    
    // Check if client has exceeded the rate limit
    if (client.count >= this.config.max) {
      // Increment consecutive blocks
      this.handleExcessiveRequests(key, client);
      
      // Set rate limit headers
      this.setRateLimitHeaders(res, client);
      
      // Reject the request
      return this.rejectRequest(res, this.config.message);
    }
    
    // Increment request count
    client.count++;
    
    // Set rate limit headers
    this.setRateLimitHeaders(res, client);
    
    // Handle response to potentially skip counting successful requests
    if (this.config.skipSuccessfulRequests) {
      const originalEnd = res.end;
      res.end = (...args: any[]) => {
        // If response is successful (2xx), decrement the counter
        if (res.statusCode >= 200 && res.statusCode < 300) {
          client.count = Math.max(0, client.count - 1);
        }
        return originalEnd.apply(res, args);
      };
    }
    
    next();
  }
  
  /**
   * Set rate limit headers on the response
   */
  private setRateLimitHeaders(res: Response, client: any) {
    res.setHeader('X-RateLimit-Limit', this.config.max);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, this.config.max - client.count));
    res.setHeader('X-RateLimit-Reset', Math.ceil(client.resetTime / 1000));
    
    if (client.count >= this.config.max) {
      const retryAfter = Math.ceil((client.resetTime - Date.now()) / 1000);
      res.setHeader('Retry-After', retryAfter);
    }
  }
  
  /**
   * Reject the request with an error message
   */
  private rejectRequest(res: Response, message: string) {
    res.status(this.config.statusCode).json({
      statusCode: this.config.statusCode,
      message,
      error: 'Too Many Requests',
    });
  }
  
  /**
   * Handle excessive requests by potentially blocking the client
   */
  private handleExcessiveRequests(key: string, client: any) {
    // Track consecutive blocks
    if (!client.consecutiveBlocks) {
      client.consecutiveBlocks = 1;
    } else {
      client.consecutiveBlocks++;
    }
    
    // If client has been blocked multiple times, add to block list
    if (client.consecutiveBlocks >= 5) {
      this.blockList.add(key);
      
      // Set block expiration (24 hours)
      const blockDuration = 24 * 60 * 60 * 1000;
      client.blockExpires = Date.now() + blockDuration;
      
      // Log the block
      console.warn(`IP ${key} has been blocked for excessive requests for ${blockDuration / (60 * 60 * 1000)} hours`);
      
      // Schedule unblock
      setTimeout(() => {
        this.blockList.delete(key);
        console.info(`IP ${key} has been unblocked`);
      }, blockDuration);
    }
  }
  
  /**
   * Clean up expired entries
   */
  private cleanup() {
    const now = Date.now();
    
    // Clean up store
    for (const key in this.store) {
      const client = this.store[key];
      
      // Remove expired entries
      if (client.resetTime < now && !client.blocked) {
        delete this.store[key];
      }
      
      // Remove expired blocks
      if (client.blockExpires && client.blockExpires < now) {
        this.blockList.delete(key);
        delete this.store[key];
      }
    }
  }
  
  /**
   * Configure the rate limiter
   */
  configure(options: Partial<RateLimitConfig>) {
    this.config = { ...this.config, ...options };
  }
  
  /**
   * Add an IP to the block list
   */
  blockIp(ip: string, duration: number = 24 * 60 * 60 * 1000) {
    this.blockList.add(ip);
    
    // Set block expiration
    if (!this.store[ip]) {
      this.store[ip] = {
        count: this.config.max,
        resetTime: Date.now() + this.config.windowMs,
        blocked: true,
        blockExpires: Date.now() + duration,
      };
    } else {
      this.store[ip].blocked = true;
      this.store[ip].blockExpires = Date.now() + duration;
    }
    
    // Schedule unblock
    setTimeout(() => {
      this.blockList.delete(ip);
    }, duration);
    
    return true;
  }
  
  /**
   * Remove an IP from the block list
   */
  unblockIp(ip: string) {
    if (this.blockList.has(ip)) {
      this.blockList.delete(ip);
      
      if (this.store[ip]) {
        this.store[ip].blocked = false;
        delete this.store[ip].blockExpires;
      }
      
      return true;
    }
    
    return false;
  }
  
  /**
   * Get the current block list
   */
  getBlockList() {
    return Array.from(this.blockList);
  }
}
