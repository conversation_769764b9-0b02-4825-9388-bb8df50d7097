import { Controller, Get, Post, Put, Patch, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';

@ApiTags('admin-payments')
@Controller('admin/payments')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminPaymentsController {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get all payments with filtering and pagination
   */
  @Get()
  @ApiOperation({ summary: 'Get all payments' })
  @ApiResponse({ status: 200, description: 'List of payments' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'toolId', required: false })
  @ApiQuery({ name: 'planId', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async getAllPayments(
    @Query('userId') userId?: string,
    @Query('toolId') toolId?: string,
    @Query('planId') planId?: string,
    @Query('status') status?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
  ) {
    try {
      // Build filter conditions
      const where: any = {};

      if (userId) {
        where.userId = userId;
      }

      if (toolId) {
        where.toolId = toolId;
      }

      if (planId) {
        where.planId = planId;
      }

      if (status) {
        where.paymentStatus = status;
      }

      if (startDate && endDate) {
        where.createdAt = {
          gte: new Date(startDate),
          lte: new Date(endDate),
        };
      } else if (startDate) {
        where.createdAt = {
          gte: new Date(startDate),
        };
      } else if (endDate) {
        where.createdAt = {
          lte: new Date(endDate),
        };
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build sort options
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      // Get payments with pagination
      const payments = await this.prisma.order.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tool: true,
          plan: true,
        },
      });

      // Get total count for pagination
      const total = await this.prisma.order.count({ where });

      return {
        data: payments,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  }

  /**
   * Get a payment by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a payment by ID' })
  @ApiResponse({ status: 200, description: 'Payment details' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  async getPaymentById(@Param('id') id: string) {
    try {
      const payment = await this.prisma.order.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tool: true,
          plan: true,
        },
      });

      if (!payment) {
        return {
          success: false,
          message: 'Payment not found',
        };
      }

      return {
        success: true,
        data: payment,
      };
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }
  }

  /**
   * Update a payment
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a payment' })
  @ApiResponse({ status: 200, description: 'Payment updated successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  async updatePayment(@Param('id') id: string, @Body() paymentData: any) {
    try {
      // Check if payment exists
      const existingPayment = await this.prisma.order.findUnique({
        where: { id },
      });

      if (!existingPayment) {
        return {
          success: false,
          message: 'Payment not found',
        };
      }

      // Prepare update data
      const updateData: any = {};

      if (paymentData.paymentStatus !== undefined) {
        updateData.paymentStatus = paymentData.paymentStatus;
      }

      if (paymentData.amount !== undefined) {
        updateData.amount = paymentData.amount;
      }

      if (paymentData.paymentMethod !== undefined) {
        updateData.paymentMethod = paymentData.paymentMethod;
      }

      // Update payment
      const updatedPayment = await this.prisma.order.update({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tool: true,
          plan: true,
        },
      });

      return {
        success: true,
        message: 'Payment updated successfully',
        data: updatedPayment,
      };
    } catch (error) {
      console.error('Error updating payment:', error);
      throw error;
    }
  }

  /**
   * Process a refund
   */
  @Post('refund')
  @ApiOperation({ summary: 'Process a refund' })
  @ApiResponse({ status: 200, description: 'Refund processed successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  async processRefund(@Body() refundData: any) {
    try {
      const { orderId, amount, reason } = refundData;

      // Check if payment exists
      const existingPayment = await this.prisma.order.findUnique({
        where: { id: orderId },
      });

      if (!existingPayment) {
        return {
          success: false,
          message: 'Payment not found',
        };
      }

      // Check if payment is already refunded
      if (existingPayment.paymentStatus === 'REFUNDED') {
        return {
          success: false,
          message: 'Payment is already refunded',
        };
      }

      // Check if payment is completed
      if (existingPayment.paymentStatus !== 'COMPLETED') {
        return {
          success: false,
          message: 'Only completed payments can be refunded',
        };
      }

      // Process refund (in a real app, this would call a payment gateway API)

      // Update payment status to refunded
      const updatedPayment = await this.prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus: 'REFUNDED',
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tool: true,
          plan: true,
        },
      });

      // Create an audit log for the refund
      await this.prisma.auditLog.create({
        data: {
          eventType: 'PAYMENT_REFUND',
          userId: updatedPayment.userId,
          severity: 'INFO',
          details: JSON.stringify({
            orderId,
            amount: amount || updatedPayment.amount,
            reason,
            refundedAt: new Date(),
          }),
        },
      });

      return {
        success: true,
        message: 'Refund processed successfully',
        data: updatedPayment,
      };
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }

  /**
   * Get payment statistics
   */
  @Get('stats/overview')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({ status: 200, description: 'Payment statistics' })
  async getPaymentStats() {
    try {
      // Get total payments
      const totalPayments = await this.prisma.order.count();

      // Get total revenue
      const totalRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true,
        },
        where: {
          paymentStatus: 'COMPLETED',
        },
      });

      // Get payments by status
      const statuses = ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'];

      const paymentsByStatus = await Promise.all(
        statuses.map(async (status) => {
          const count = await this.prisma.order.count({
            where: {
              paymentStatus: status as any,
            },
          });

          const sum = await this.prisma.order.aggregate({
            _sum: {
              amount: true,
            },
            where: {
              paymentStatus: status as any,
            },
          });

          return {
            status,
            count,
            amount: sum._sum.amount || 0,
          };
        })
      );

      // Get payments by payment method
      const paymentMethods = await this.prisma.order.groupBy({
        by: ['paymentMethod'],
        _count: {
          id: true,
        },
        _sum: {
          amount: true,
        },
      });

      const paymentsByMethod = paymentMethods.map((method) => ({
        method: method.paymentMethod,
        count: method._count.id,
        amount: method._sum.amount || 0,
      }));

      // Get recent payments (last 30 days)
      const recentPayments = await this.prisma.order.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      // Get recent revenue (last 30 days)
      const recentRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true,
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      // Calculate growth rate
      const previousMonthPayments = await this.prisma.order.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      const previousMonthRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true,
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });

      const paymentGrowthRate = previousMonthPayments > 0
        ? ((recentPayments - previousMonthPayments) / previousMonthPayments) * 100
        : 0;

      const revenueGrowthRate = previousMonthRevenue._sum.amount > 0
        ? ((recentRevenue._sum.amount - previousMonthRevenue._sum.amount) / previousMonthRevenue._sum.amount) * 100
        : 0;

      return {
        totalPayments,
        totalRevenue: totalRevenue._sum.amount || 0,
        paymentsByStatus,
        paymentsByMethod,
        recentPayments,
        recentRevenue: recentRevenue._sum.amount || 0,
        paymentGrowthRate,
        revenueGrowthRate,
      };
    } catch (error) {
      console.error('Error fetching payment stats:', error);
      throw error;
    }
  }
}
