'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

const PricingSection = () => {
  const plans = [
    {
      name: "Standard",
      price: "29.99",
      currency: "TND",
      period: "per month",
      description: "Basic access to shared accounts",
      features: [
        "Access to 10+ premium tools",
        "Basic support",
        "1 concurrent login",
        "Standard availability"
      ],
      buttonText: "Get Started",
      highlighted: false,
      emoji: "🚀"
    },
    {
      name: "Premium",
      price: "59.99",
      currency: "TND",
      period: "per month",
      description: "Enhanced access with priority support",
      features: [
        "Access to 50+ premium tools",
        "Priority support",
        "2 concurrent logins",
        "High availability",
        "Premium tool selection"
      ],
      buttonText: "Get Premium",
      highlighted: true,
      emoji: "⭐"
    },
    {
      name: "Gold",
      price: "99.99",
      currency: "TND",
      period: "per month",
      description: "Full access to all premium tools",
      features: [
        "Access to 120+ premium tools",
        "24/7 support",
        "3 concurrent logins",
        "Highest availability",
        "All premium tools included",
        "Early access to new tools"
      ],
      buttonText: "Get Gold",
      highlighted: false,
      emoji: "👑"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-pink-50 to-white" id="pricing">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            Simple <span className="text-yellow-400">Pricing</span> 💰
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Choose the plan that works best for you. All plans include local payment options.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 * index, duration: 0.5 }}
              className={`rounded-xl overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ${
                plan.highlighted
                  ? 'shadow-xl ring-2 ring-yellow-400'
                  : 'shadow-lg'
              }`}
            >
              <div
                className={`p-6 ${
                  plan.highlighted ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-900' : 'bg-white'
                }`}
              >
                <div className="flex justify-between items-center">
                  <h3 className="text-2xl font-bold terminal-text">{plan.name}</h3>
                  <span className="text-3xl">{plan.emoji}</span>
                </div>
                <div className="flex items-end mt-4">
                  <span className="text-3xl font-bold">{plan.currency} {plan.price}</span>
                  <span className="ml-1 text-sm opacity-80">{plan.period}</span>
                </div>
                <p className={`mt-2 ${plan.highlighted ? 'text-gray-800' : 'text-gray-600'}`}>
                  {plan.description}
                </p>
              </div>

              <div className="bg-white p-6">
                <ul className="mb-6 space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-center">
                      <svg
                        className="w-5 h-5 text-yellow-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Link
                  href="/signup"
                  className={`w-full block text-center py-3 rounded-full font-medium transition-all duration-300 hover:shadow-lg ${
                    plan.highlighted
                      ? 'bg-yellow-400 text-gray-900 hover:bg-yellow-500'
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                  }`}
                >
                  {plan.buttonText}
                </Link>
                
                <p className="text-center mt-4 text-sm text-gray-500">
                  Pay with local Tunisian methods
                </p>
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="mt-12 text-center"
        >
          <p className="text-gray-700">
            Need a custom plan for your team or business?
          </p>
          <Link 
            href="/contact" 
            className="text-yellow-500 font-medium hover:underline"
          >
            Contact us for special pricing →
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
