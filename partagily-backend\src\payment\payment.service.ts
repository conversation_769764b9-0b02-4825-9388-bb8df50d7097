import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { PrismaService } from "../prisma/prisma.service";
import { InitiatePaymentDto } from "./dto/initiate-payment.dto";
import { PaymentResponseDto } from "./dto/payment-response.dto";
import axios from "axios";

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly konnectApiUrl = "https://app.konnect.network/api/payment";
  private readonly processedPayments = new Set<string>(); // For idempotency

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService
  ) {}

  async initiatePayment(
    userId: string,
    initiatePaymentDto: InitiatePaymentDto
  ): Promise<PaymentResponseDto> {
    try {
      // Find the user's cart
      const cart = await this.prisma.cart.findFirst({
        where: {
          userId,
          status: "OPEN",
        },
        include: {
          items: true,
        },
      });

      if (!cart || cart.items.length === 0) {
        throw new NotFoundException("Cart is empty or not found");
      }

      // Get user details for the payment
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException("User not found");
      }

      // Calculate total amount
      const totalAmount = cart.items.reduce((sum, item) => sum + item.price, 0);

      // Generate a unique client reference
      const clientReference = `partagily-${userId}-${Date.now()}`;

      // Store the client reference in the database for later verification
      await this.prisma.paymentReference.create({
        data: {
          userId,
          cartId: cart.id,
          clientReference,
          amount: totalAmount,
          status: "PENDING",
        },
      });

      // Prepare the payment request to Konnect
      const paymentRequest = {
        amount: totalAmount,
        currency: "TND", // Tunisian Dinar
        customer_email: user.email,
        success_url: `${this.configService.get("FRONTEND_URL")}/payment/success`,
        cancel_url: `${this.configService.get("FRONTEND_URL")}/payment/cancel`,
        webhook_url: `${this.configService.get("BACKEND_URL")}/payment/webhook`,
        client_reference: clientReference,
      };

      this.logger.log(
        `Initiating payment for user ${userId} with amount ${totalAmount}`
      );

      // Call Konnect API to initiate payment
      const response = await axios.post(
        `${this.konnectApiUrl}/initiate`,
        paymentRequest,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.data || !response.data.payment_url) {
        throw new BadRequestException(
          "Failed to initiate payment with Konnect"
        );
      }

      // Return the payment URL to redirect the user
      return {
        success: true,
        message: "Payment initiated successfully",
        data: {
          paymentUrl: response.data.payment_url,
          clientReference,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error initiating payment: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || "Failed to initiate payment",
      };
    }
  }

  async verifyPayment(
    paymentRef: string
  ): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      // Check if we've already processed this payment (idempotency)
      if (this.processedPayments.has(paymentRef)) {
        this.logger.log(`Payment ${paymentRef} already processed, skipping`);
        return { success: true, message: "Payment already processed" };
      }

      this.logger.log(`Verifying payment with reference: ${paymentRef}`);

      // Call Konnect API to verify payment status
      const response = await axios.get(
        `${this.konnectApiUrl}/details?payment_ref=${paymentRef}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.data || response.data.status !== "completed") {
        this.logger.warn(
          `Payment ${paymentRef} verification failed: ${JSON.stringify(response.data)}`
        );
        return { success: false, message: "Payment verification failed" };
      }

      // Extract client reference from the response
      const clientReference = response.data.client_reference;
      if (!clientReference) {
        this.logger.warn(`Payment ${paymentRef} has no client reference`);
        return { success: false, message: "Payment has no client reference" };
      }

      // Find the payment reference in our database
      const paymentReference = await this.prisma.paymentReference.findFirst({
        where: {
          clientReference,
        },
      });

      if (!paymentReference) {
        this.logger.warn(
          `Payment reference ${clientReference} not found in database`
        );
        return {
          success: false,
          message: "Payment reference not found in database",
        };
      }

      // Mark the payment as processed for idempotency
      this.processedPayments.add(paymentRef);

      // Process the payment
      const orderHistory =
        await this.processSuccessfulPayment(paymentReference);

      // Return the order history data
      return {
        success: true,
        message: "Payment processed successfully",
        data: orderHistory,
      };
    } catch (error) {
      this.logger.error(
        `Error verifying payment: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: `Error verifying payment: ${error.message}`,
      };
    }
  }

  async handleCancelledPayment(
    paymentRef: string
  ): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      this.logger.log(
        `Handling cancelled payment with reference: ${paymentRef}`
      );

      // Call Konnect API to get payment details
      const response = await axios.get(
        `${this.konnectApiUrl}/details?payment_ref=${paymentRef}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Extract client reference from the response
      const clientReference = response.data?.client_reference;
      if (!clientReference) {
        this.logger.warn(
          `Cancelled payment ${paymentRef} has no client reference`
        );
        return { success: false, message: "Payment has no client reference" };
      }

      // Find the payment reference in our database
      const paymentReference = await this.prisma.paymentReference.findFirst({
        where: {
          clientReference,
        },
      });

      if (!paymentReference) {
        this.logger.warn(
          `Payment reference ${clientReference} not found in database`
        );
        return {
          success: false,
          message: "Payment reference not found in database",
        };
      }

      // Update payment reference status to CANCELLED
      await this.prisma.paymentReference.update({
        where: { id: paymentReference.id },
        data: {
          status: "CANCELLED",
          paymentRef: paymentRef,
        },
      });

      // Get the cart details
      const cart = await this.prisma.cart.findUnique({
        where: { id: paymentReference.cartId },
        include: { items: true },
      });

      if (!cart) {
        return { success: false, message: "Cart not found" };
      }

      // Return the cart data
      return {
        success: false,
        message: "Payment was cancelled",
        data: {
          id: cart.id,
          userId: cart.userId,
          status: cart.status,
          items: cart.items,
          totalAmount: cart.items.reduce((sum, item) => sum + item.price, 0),
        },
      };
    } catch (error) {
      this.logger.error(
        `Error handling cancelled payment: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: `Error handling cancelled payment: ${error.message}`,
      };
    }
  }

  private async processSuccessfulPayment(paymentReference: any): Promise<any> {
    try {
      let orderHistory;

      // Start a transaction to ensure all operations succeed or fail together
      await this.prisma.$transaction(async (prisma) => {
        // Update payment reference status
        await prisma.paymentReference.update({
          where: { id: paymentReference.id },
          data: {
            status: "COMPLETED",
            paymentRef: paymentReference.paymentRef || null,
          },
        });

        // Find the cart
        const cart = await prisma.cart.findUnique({
          where: { id: paymentReference.cartId },
          include: { items: true },
        });

        if (!cart) {
          throw new NotFoundException("Cart not found");
        }

        // Generate order number
        const orderNumber = `ORD-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Create order history
        orderHistory = await prisma.orderHistory.create({
          data: {
            userId: paymentReference.userId,
            orderNumber,
            totalAmount: paymentReference.amount,
            status: "COMPLETED",
            paymentMethod: "KONNECT",
            items: {
              create: cart.items.map((item) => ({
                type: item.type,
                itemId: item.itemId,
                name: item.name,
                price: item.price,
                icon: item.icon || null,
              })),
            },
          },
          include: {
            items: true,
          },
        });

        // Process each item in the cart
        for (const item of cart.items) {
          if (item.type === "PLAN") {
            // Create subscription for plans
            await prisma.subscription.create({
              data: {
                userId: paymentReference.userId,
                planId: item.itemId,
                startDate: new Date(),
                endDate: new Date(
                  new Date().setFullYear(new Date().getFullYear() + 1)
                ), // 1 year subscription
                status: "ACTIVE",
                autoRenew: true,
              },
            });
          } else if (item.type === "TOOL") {
            // Grant access to tools
            // This will be implemented in the next phase
          }
        }

        // Update cart status
        await prisma.cart.update({
          where: { id: cart.id },
          data: { status: "CHECKED_OUT" },
        });
      });

      this.logger.log(
        `Payment ${paymentReference.clientReference} processed successfully`
      );

      // Return the order history
      return orderHistory;
    } catch (error) {
      this.logger.error(
        `Error processing payment: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }
}
