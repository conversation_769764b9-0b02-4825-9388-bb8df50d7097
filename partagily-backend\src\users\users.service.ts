import { Injectable, ConflictException, NotFoundException, InternalServerErrorException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { Prisma } from '@prisma/client';
import { User } from './entities/user.entity';
import { UserRole } from './entities/user.entity';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findByEmail(email: string): Promise<User | null> {
    console.log('Finding user by email:', email);

    try {
      // Try to find the user directly in the database
      console.log('Searching for user in database with email:', email);
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (user) {
        console.log('User found in database with email:', email);
        // Convert Prisma User to our User entity
        return this.mapPrismaUserToEntity(user);
      }

      console.log('User not found in database with email:', email);
      return null;
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw new InternalServerErrorException('Database error while finding user');
    }
  }

  async findById(id: string): Promise<User | null> {
    console.log('Finding user by id:', id);

    try {
      // Try to find the user directly in the database
      console.log('Searching for user in database with id:', id);
      const user = await this.prisma.user.findUnique({
        where: { id },
      });

      if (user) {
        console.log('User found in database with id:', id);
        // Convert Prisma User to our User entity
        return this.mapPrismaUserToEntity(user);
      }

      console.log('User not found in database with id:', id);
      return null;
    } catch (error) {
      console.error('Error finding user by id:', error);
      throw new InternalServerErrorException('Database error while finding user by ID');
    }
  }

  async create(data: any): Promise<User> {
    console.log('UsersService.create called with:', {
      email: data.email,
      name: data.name,
      password: '***REDACTED***',
    });

    try {
      // Verify database connection before attempting to create user
      if (!this.prisma.isDatabaseConnected()) {
        console.error('Database is not connected. Cannot create user.');
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      // Check if user with this email already exists directly in the database
      console.log('Checking if email already exists in database...');
      const existingUserInDb = await this.prisma.user.findUnique({
        where: { email: data.email },
      });

      if (existingUserInDb) {
        console.log('Email already in use in database:', data.email);
        throw new ConflictException('Email already in use');
      }
      console.log('Email is available in database');

      // Hash the password
      console.log('Hashing password...');
      const hashedPassword = await bcrypt.hash(data.password, 10);
      console.log('Password hashed successfully');

      // Prepare user data with only the fields that are in the schema
      const userData = {
        email: data.email,
        name: data.name,
        password: hashedPassword,
        role: data.role || UserRole.USER,
        isActive: true,
        emailVerified: false
      };

      console.log('User data prepared:', {
        ...userData,
        password: '***REDACTED***',
      });

      // Create the user in the database
      console.log('About to create user in database with data:', {
        ...userData,
        password: '***REDACTED***',
      });

      // Define the database operation
      const dbOperation = async () => {
        try {
          const user = await this.prisma.user.create({
            data: userData,
          });
          console.log('User created successfully with ID:', user.id);
          return user;
        } catch (error) {
          console.error('Error in database operation:', error);

          // Handle Prisma unique constraint violation
          if (error.code === 'P2002') {
            throw new ConflictException('Email already in use');
          }

          throw error; // Re-throw other errors
        }
      };

      // Execute the operation directly - no more fallbacks
      const createdUser = await this.prisma.safeExecute(
        dbOperation,
        () => { throw new Error('Database operation failed and no fallback is available'); }
      );

      // Verify that we have a valid user
      if (!createdUser || !createdUser.id) {
        console.error('Failed to create user: No valid user returned');
        throw new InternalServerErrorException('Failed to create user. Please try again later.');
      }

      console.log('User created successfully:', {
        id: createdUser.id,
        email: createdUser.email,
        name: createdUser.name,
      });

      // Convert Prisma User to our User entity
      const userEntity = this.mapPrismaUserToEntity(createdUser);
      console.log('User entity created:', {
        id: userEntity.id,
        email: userEntity.email,
        name: userEntity.name,
      });

      return userEntity;
    } catch (error) {
      console.error('Error creating user:', error);

      // Handle specific error types
      if (error instanceof ConflictException) {
        throw error;
      }

      if (error instanceof InternalServerErrorException) {
        throw error;
      }

      // Handle Prisma unique constraint violation
      if (error.code === 'P2002') {
        throw new ConflictException('Email already in use');
      }

      // Always throw a properly formatted error
      throw new InternalServerErrorException(`Failed to create user: ${error.message}`);
    }
  }

  async update(id: string, data: any): Promise<User> {
    console.log('Updating user with ID:', id, 'Data:', data);

    try {
      // Check if user exists
      const user = await this.findById(id);
      if (!user) {
        console.log('User not found with ID:', id);
        throw new NotFoundException('User not found');
      }

      // Verify database connection before attempting to update user
      if (!this.prisma.isDatabaseConnected()) {
        console.error('Database is not connected. Cannot update user.');
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      // If email is being changed, check if it's already in use
      if (data.email && data.email !== user.email) {
        console.log('Email change detected. Checking if new email is available:', data.email);
        const existingUser = await this.findByEmail(data.email);
        if (existingUser && existingUser.id !== id) {
          console.log('Email already in use:', data.email);
          throw new ConflictException('Email already in use');
        }
        console.log('New email is available:', data.email);
      }

      // Define the database operation
      const dbOperation = async () => {
        try {
          // Update the user
          const updatedUser = await this.prisma.user.update({
            where: { id },
            data,
          });
          console.log('User updated successfully:', updatedUser.id);
          return updatedUser;
        } catch (error) {
          console.error('Error in database operation:', error);

          // Handle Prisma unique constraint violation
          if (error.code === 'P2002') {
            throw new ConflictException('Email already in use');
          }

          throw error; // Re-throw other errors
        }
      };

      // Execute the operation
      const updatedUser = await this.prisma.safeExecute(
        dbOperation,
        () => { throw new Error('Database operation failed and no fallback is available'); }
      );

      // Verify that we have a valid user
      if (!updatedUser || !updatedUser.id) {
        console.error('Failed to update user: No valid user returned');
        throw new InternalServerErrorException('Failed to update user. Please try again later.');
      }

      console.log('User updated successfully:', {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
      });

      // Convert Prisma User to our User entity
      return this.mapPrismaUserToEntity(updatedUser);
    } catch (error) {
      console.error('Error updating user:', error);

      // Handle specific error types
      if (error instanceof NotFoundException ||
          error instanceof ConflictException ||
          error instanceof InternalServerErrorException) {
        throw error;
      }

      // Handle Prisma unique constraint violation
      if (error.code === 'P2002') {
        throw new ConflictException('Email already in use');
      }

      // Always throw a properly formatted error
      throw new InternalServerErrorException(`Failed to update user: ${error.message}`);
    }
  }

  async updatePassword(id: string, password: string): Promise<User> {
    console.log('Updating password for user with ID:', id);

    try {
      // Check if user exists
      const user = await this.findById(id);
      if (!user) {
        console.log('User not found with ID:', id);
        throw new NotFoundException('User not found');
      }

      // Verify database connection before attempting to update password
      if (!this.prisma.isDatabaseConnected()) {
        console.error('Database is not connected. Cannot update password.');
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      // Hash the new password
      console.log('Hashing new password...');
      const hashedPassword = await bcrypt.hash(password, 10);
      console.log('Password hashed successfully');

      // Define the database operation
      const dbOperation = async () => {
        try {
          // Update the user's password
          const updatedUser = await this.prisma.user.update({
            where: { id },
            data: { password: hashedPassword },
          });
          console.log('Password updated successfully for user:', updatedUser.id);
          return updatedUser;
        } catch (error) {
          console.error('Error in database operation:', error);
          throw error;
        }
      };

      // Execute the operation
      const updatedUser = await this.prisma.safeExecute(
        dbOperation,
        () => { throw new Error('Database operation failed and no fallback is available'); }
      );

      // Verify that we have a valid user
      if (!updatedUser || !updatedUser.id) {
        console.error('Failed to update password: No valid user returned');
        throw new InternalServerErrorException('Failed to update password. Please try again later.');
      }

      console.log('Password updated successfully for user:', updatedUser.id);

      // Convert Prisma User to our User entity
      return this.mapPrismaUserToEntity(updatedUser);
    } catch (error) {
      console.error('Error updating password:', error);

      // Handle specific error types
      if (error instanceof NotFoundException ||
          error instanceof InternalServerErrorException) {
        throw error;
      }

      // Always throw a properly formatted error
      throw new InternalServerErrorException(`Failed to update password: ${error.message}`);
    }
  }

  /**
   * Helper method to convert Prisma User to our User entity
   */
  private mapPrismaUserToEntity(prismaUser: any): User {
    const user = new User();
    user.id = prismaUser.id;
    user.email = prismaUser.email;
    user.password = prismaUser.password;
    user.name = prismaUser.name;
    user.avatar = prismaUser.avatar;
    user.role = prismaUser.role;
    user.isActive = prismaUser.isActive;
    user.emailVerified = prismaUser.emailVerified;
    user.createdAt = prismaUser.createdAt;
    user.updatedAt = prismaUser.updatedAt;
    user.subscriptions = [];
    user.orders = [];
    user.sessions = [];

    return user;
  }
}
