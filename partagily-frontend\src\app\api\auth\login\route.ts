import { NextRequest, NextResponse } from "next/server";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Forward the request to the backend
    console.log("Forwarding login request to:", `${API_URL}/auth/login`);
    console.log(
      "Request body:",
      JSON.stringify({ ...body, password: "***REDACTED***" })
    );

    const response = await fetch(`${API_URL}/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    console.log("Login response status:", response.status);
    console.log("Login response data:", {
      ...data,
      accessToken: data.accessToken ? "***REDACTED***" : undefined,
      refreshToken: data.refreshToken ? "***REDACTED***" : undefined,
    });

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error("Error proxying to backend:", error);

    // Provide more detailed error information
    let errorMessage = "Failed to login";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = `Login error: ${error.message}`;
      console.error("Error details:", error.stack);
    }

    if (error instanceof TypeError && error.message.includes("fetch")) {
      errorMessage =
        "Unable to connect to the backend server. Please check if the server is running.";
      statusCode = 503; // Service Unavailable
    }

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
