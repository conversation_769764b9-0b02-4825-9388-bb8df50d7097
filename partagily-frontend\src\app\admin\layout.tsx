'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';

// Icons
import {
  Users,
  LayoutDashboard,
  Package,
  CreditCard,
  BarChart3,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  // Admin check code
  useEffect(() => {
    // First, check if we have admin user in localStorage or sessionStorage
    const adminUserJson = localStorage.getItem('adminUser') || sessionStorage.getItem('adminUser') ||
                          localStorage.getItem('user') || sessionStorage.getItem('user');

    if (adminUserJson) {
      try {
        const adminUser = JSON.parse(adminUserJson);
        if (adminUser && (adminUser.email === '<EMAIL>' || adminUser.role === 'admin')) {
          console.log('Admin user found in storage, allowing access');
          return; // Allow access to admin layout
        }
      } catch (error) {
        console.error('Error parsing admin user from storage:', error);
      }
    }

    // If no admin user in storage, check if we have tokens
    const accessToken = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken') || sessionStorage.getItem('refreshToken');

    if (accessToken && refreshToken) {
      console.log('Auth tokens found, allowing access');
      return; // Allow access based on tokens
    }

    // If no admin user in localStorage, check through normal auth flow
    if (!isLoading) {
      console.log('Admin layout - checking user:', user);
      console.log('User authenticated?', isAuthenticated);

      if (!isAuthenticated) {
        console.log('User not authenticated, redirecting to admin-login');
        router.push('/admin-login');
        return;
      }

      if (!user) {
        console.log('User object is null or undefined, redirecting to admin-login');
        router.push('/admin-login');
        return;
      }

      console.log('User role:', user.role);
      console.log('Role type:', typeof user.role);

      // Check if <NAME_EMAIL>
      if (user.email === '<EMAIL>') {
        console.log('Admin email detected, allowing access');
        return; // Allow access to admin layout
      }

      // Check if role is exactly 'admin'
      const isAdmin = user.role === 'admin';

      // Fallback check if role contains 'admin' (case insensitive)
      const containsAdmin = typeof user.role === 'string' &&
                          user.role.toLowerCase().includes('admin');

      console.log('Is user admin (exact match)?', isAdmin);
      console.log('Does role contain admin?', containsAdmin);

      if (!isAdmin && !containsAdmin) {
        console.log('User not admin, redirecting to admin-login');
        router.push('/admin-login');
      } else {
        console.log('User is admin, allowing access to admin layout');
      }
    }
  }, [isAuthenticated, isLoading, router, user]);

  // Remove any existing Navbar and Footer elements that might be rendered from the parent layout
  useEffect(() => {
    const navbar = document.querySelector('nav:not(.admin-nav)');
    const footer = document.querySelector('footer');

    if (navbar) navbar.style.display = 'none';
    if (footer) footer.style.display = 'none';

    return () => {
      if (navbar) navbar.style.display = '';
      if (footer) footer.style.display = '';
    };
  }, []);

  const handleLogout = async () => {
    // Clear all storage
    localStorage.removeItem('adminUser');
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');

    sessionStorage.removeItem('adminUser');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('accessToken');
    sessionStorage.removeItem('refreshToken');

    // Also call the normal logout function
    await logout();

    // Redirect to admin login page
    router.push('/admin-login');
  };

  const toggleSubmenu = (submenu: string) => {
    if (activeSubmenu === submenu) {
      setActiveSubmenu(null);
    } else {
      setActiveSubmenu(submenu);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-r from-gray-900 to-gray-800">
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: { duration: 1, repeat: Infinity, ease: 'linear' },
            scale: { duration: 1, repeat: Infinity, ease: 'easeInOut' },
          }}
          className="w-16 h-16 rounded-full border-4 border-t-yellow-400 border-r-pink-400 border-b-blue-400 border-l-green-400"
        />
      </div>
    );
  }

  const menuItems = [
    {
      title: 'Dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />,
      href: '/admin',
      submenu: null,
    },
    {
      title: 'Users',
      icon: <Users className="w-5 h-5" />,
      href: '/admin/users',
      submenu: [
        { title: 'All Users', href: '/admin/users' },
        { title: 'Add User', href: '/admin/users/create' },
      ],
    },
    {
      title: 'Tools',
      icon: <Package className="w-5 h-5" />,
      href: '/admin/tools',
      submenu: [
        { title: 'All Tools', href: '/admin/tools' },
        { title: 'Add Tool', href: '/admin/tools/create' },
      ],
    },
    {
      title: 'Subscriptions',
      icon: <CreditCard className="w-5 h-5" />,
      href: '/admin/subscriptions',
      submenu: [
        { title: 'All Subscriptions', href: '/admin/subscriptions' },
        { title: 'Expiring Soon', href: '/admin/subscriptions/expiring' },
        { title: 'Add Subscription', href: '/admin/subscriptions/create' },
      ],
    },
    {
      title: 'Payments',
      icon: <CreditCard className="w-5 h-5" />,
      href: '/admin/payments',
      submenu: [
        { title: 'All Payments', href: '/admin/payments' },
        { title: 'Refunds', href: '/admin/payments/refunds' },
      ],
    },
    {
      title: 'Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      href: '/admin/analytics',
      submenu: [
        { title: 'Overview', href: '/admin/analytics' },
        { title: 'Dashboard', href: '/admin/analytics/dashboard' },
        { title: 'User Growth', href: '/admin/analytics/user-growth' },
        { title: 'Revenue', href: '/admin/analytics/revenue' },
        { title: 'Subscriptions', href: '/admin/analytics/subscriptions' },
        { title: 'Tool Usage', href: '/admin/analytics/tool-usage' },
      ],
    },
    {
      title: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      href: '/admin/settings',
      submenu: null,
    },
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <motion.aside
        initial={{ width: isSidebarOpen ? 240 : 80 }}
        animate={{ width: isSidebarOpen ? 240 : 80 }}
        transition={{ duration: 0.3 }}
        className="hidden md:block bg-gray-900 text-white h-full overflow-y-auto"
      >
        <div className="p-4">
          <div className="flex items-center justify-between">
            {isSidebarOpen && (
              <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="text-xl font-bold text-yellow-400"
              >
                Partagily Admin
              </motion.h1>
            )}
            <button
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
              className="p-2 rounded-md hover:bg-gray-800"
            >
              <Menu className="w-5 h-5" />
            </button>
          </div>
        </div>

        <nav className="mt-6 admin-nav">
          <ul className="space-y-2 px-2">
            {menuItems.map((item) => (
              <li key={item.title}>
                {item.submenu ? (
                  <div>
                    <button
                      onClick={() => toggleSubmenu(item.title)}
                      className={`flex items-center justify-between w-full p-3 rounded-md hover:bg-gray-800 transition-colors ${
                        activeSubmenu === item.title ? 'bg-gray-800' : ''
                      }`}
                    >
                      <div className="flex items-center">
                        {item.icon}
                        {isSidebarOpen && (
                          <motion.span
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.3 }}
                            className="ml-3"
                          >
                            {item.title}
                          </motion.span>
                        )}
                      </div>
                      {isSidebarOpen && (
                        <motion.span
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {activeSubmenu === item.title ? (
                            <ChevronUp className="w-4 h-4" />
                          ) : (
                            <ChevronDown className="w-4 h-4" />
                          )}
                        </motion.span>
                      )}
                    </button>
                    {isSidebarOpen && activeSubmenu === item.title && (
                      <motion.ul
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        transition={{ duration: 0.3 }}
                        className="pl-10 mt-1 space-y-1"
                      >
                        {item.submenu.map((subItem) => (
                          <li key={subItem.title}>
                            <Link
                              href={subItem.href}
                              className="block p-2 rounded-md hover:bg-gray-800 transition-colors"
                            >
                              {subItem.title}
                            </Link>
                          </li>
                        ))}
                      </motion.ul>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className="flex items-center p-3 rounded-md hover:bg-gray-800 transition-colors"
                  >
                    {item.icon}
                    {isSidebarOpen && (
                      <motion.span
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                        className="ml-3"
                      >
                        {item.title}
                      </motion.span>
                    )}
                  </Link>
                )}
              </li>
            ))}
            <li>
              <button
                onClick={handleLogout}
                className="flex items-center w-full p-3 rounded-md hover:bg-gray-800 transition-colors text-red-400"
              >
                <LogOut className="w-5 h-5" />
                {isSidebarOpen && (
                  <motion.span
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="ml-3"
                  >
                    Logout
                  </motion.span>
                )}
              </button>
            </li>
          </ul>
        </nav>
      </motion.aside>

      {/* Mobile sidebar */}
      <div className="md:hidden">
        <button
          onClick={() => setIsMobileSidebarOpen(true)}
          className="fixed top-4 left-4 z-20 p-2 rounded-md bg-gray-900 text-white"
        >
          <Menu className="w-5 h-5" />
        </button>

        {isMobileSidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-30"
            onClick={() => setIsMobileSidebarOpen(false)}
          />
        )}

        <motion.aside
          initial={{ x: '-100%' }}
          animate={{ x: isMobileSidebarOpen ? 0 : '-100%' }}
          transition={{ duration: 0.3 }}
          className="fixed top-0 left-0 z-40 w-64 h-full bg-gray-900 text-white overflow-y-auto"
        >
          <div className="p-4 flex items-center justify-between">
            <h1 className="text-xl font-bold text-yellow-400">Partagily Admin</h1>
            <button
              onClick={() => setIsMobileSidebarOpen(false)}
              className="p-2 rounded-md hover:bg-gray-800"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <nav className="mt-6 admin-nav">
            <ul className="space-y-2 px-2">
              {menuItems.map((item) => (
                <li key={item.title}>
                  {item.submenu ? (
                    <div>
                      <button
                        onClick={() => toggleSubmenu(item.title)}
                        className={`flex items-center justify-between w-full p-3 rounded-md hover:bg-gray-800 transition-colors ${
                          activeSubmenu === item.title ? 'bg-gray-800' : ''
                        }`}
                      >
                        <div className="flex items-center">
                          {item.icon}
                          <span className="ml-3">{item.title}</span>
                        </div>
                        {activeSubmenu === item.title ? (
                          <ChevronUp className="w-4 h-4" />
                        ) : (
                          <ChevronDown className="w-4 h-4" />
                        )}
                      </button>
                      {activeSubmenu === item.title && (
                        <motion.ul
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          transition={{ duration: 0.3 }}
                          className="pl-10 mt-1 space-y-1"
                        >
                          {item.submenu.map((subItem) => (
                            <li key={subItem.title}>
                              <Link
                                href={subItem.href}
                                className="block p-2 rounded-md hover:bg-gray-800 transition-colors"
                                onClick={() => setIsMobileSidebarOpen(false)}
                              >
                                {subItem.title}
                              </Link>
                            </li>
                          ))}
                        </motion.ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="flex items-center p-3 rounded-md hover:bg-gray-800 transition-colors"
                      onClick={() => setIsMobileSidebarOpen(false)}
                    >
                      {item.icon}
                      <span className="ml-3">{item.title}</span>
                    </Link>
                  )}
                </li>
              ))}
              <li>
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full p-3 rounded-md hover:bg-gray-800 transition-colors text-red-400"
                >
                  <LogOut className="w-5 h-5" />
                  <span className="ml-3">Logout</span>
                </button>
              </li>
            </ul>
          </nav>
        </motion.aside>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">Admin Dashboard</h1>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-700">
                {user?.name} <span className="text-gray-400">(Admin)</span>
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 p-4 md:p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
