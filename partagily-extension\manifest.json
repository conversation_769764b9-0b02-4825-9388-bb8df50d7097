{"manifest_version": 3, "name": "Partagily", "version": "1.0.0", "description": "Access premium tools through shared accounts", "permissions": ["storage", "cookies", "declarativeNetRequest", "tabs", "alarms", "scripting"], "host_permissions": ["<all_urls>", "http://localhost:*/*", "http://127.0.0.1:*/*", "https://partagily.com/*", "https://*.netflix.com/*"], "background": {"service_worker": "background.js"}, "action": {"default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start"}, {"matches": ["*://*.netflix.com/*"], "js": ["src/content-scripts/netflix.js"], "run_at": "document_end"}, {"matches": ["http://localhost:*/*", "http://127.0.0.1:*/*", "https://partagily.com/*"], "js": ["src/content-scripts/platform.js"], "run_at": "document_end"}], "web_accessible_resources": [{"resources": ["icons/*", "utils/*", "src/inject.js", "src/utils/anti-debug.js"], "matches": ["<all_urls>"]}], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'", "sandbox": "sandbox allow-scripts allow-forms allow-popups allow-modals; script-src 'self' 'unsafe-inline' 'unsafe-eval'; child-src 'self'"}}