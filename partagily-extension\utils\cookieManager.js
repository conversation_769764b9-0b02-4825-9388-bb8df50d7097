// Secure cookie management utility
import { initializeEncryptionKey, encryptData, decryptData } from './cryptoUtils.js';

/**
 * Cookie Manager for secure cookie handling
 */
class CookieManager {
  constructor() {
    this.encryptionKey = null;
    this.initialized = false;
  }

  /**
   * Initialize the cookie manager
   * @returns {Promise<void>}
   */
  async initialize() {
    if (!this.initialized) {
      this.encryptionKey = await initializeEncryptionKey();
      this.initialized = true;
    }
  }

  /**
   * Store encrypted cookies in secure storage
   * @param {string} toolId - ID of the tool
   * @param {Array} cookies - Array of cookie objects
   * @returns {Promise<void>}
   */
  async storeCookies(toolId, cookies) {
    await this.initialize();
    
    // Add metadata to cookies
    const cookiesWithMetadata = cookies.map(cookie => ({
      ...cookie,
      createdAt: Date.now(),
      expiresAt: Date.now() + (cookie.maxAge || 86400000) // Default 1 day if not specified
    }));
    
    // Encrypt the cookie data
    const encryptedData = await encryptData(
      JSON.stringify(cookiesWithMetadata),
      this.encryptionKey
    );
    
    // Store in Chrome's secure storage
    await chrome.storage.local.set({
      [`cookies_${toolId}`]: encryptedData
    });
    
    console.log(`Stored encrypted cookies for tool ${toolId}`);
  }

  /**
   * Retrieve and decrypt cookies for a tool
   * @param {string} toolId - ID of the tool
   * @returns {Promise<Array|null>} Array of cookie objects or null if not found
   */
  async getCookies(toolId) {
    await this.initialize();
    
    // Get encrypted data from storage
    const data = await chrome.storage.local.get(`cookies_${toolId}`);
    const encryptedData = data[`cookies_${toolId}`];
    
    if (!encryptedData) {
      console.log(`No cookies found for tool ${toolId}`);
      return null;
    }
    
    try {
      // Decrypt the data
      const decryptedData = await decryptData(encryptedData, this.encryptionKey);
      const cookies = JSON.parse(decryptedData);
      
      // Filter out expired cookies
      const validCookies = cookies.filter(cookie => {
        return cookie.expiresAt > Date.now();
      });
      
      // If some cookies expired, update the storage with only valid ones
      if (validCookies.length < cookies.length) {
        await this.storeCookies(toolId, validCookies);
      }
      
      return validCookies;
    } catch (error) {
      console.error('Error decrypting cookies:', error);
      return null;
    }
  }

  /**
   * Inject cookies for a specific domain
   * @param {string} domain - Domain to inject cookies for
   * @param {Array} cookies - Array of cookie objects
   * @returns {Promise<boolean>} Success status
   */
  async injectCookies(domain, cookies) {
    try {
      if (!cookies || cookies.length === 0) {
        console.error('No cookies to inject');
        return false;
      }
      
      for (const cookie of cookies) {
        // Validate cookie before injection
        if (!this.validateCookie(cookie)) {
          console.error('Invalid cookie:', cookie);
          continue;
        }
        
        // Set secure cookie attributes
        await chrome.cookies.set({
          url: `https://${domain}`,
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain || `.${domain}`,
          path: cookie.path || '/',
          secure: true,
          httpOnly: cookie.httpOnly !== false, // Default to true
          sameSite: cookie.sameSite || 'lax', // Default to lax
          expirationDate: Math.floor(cookie.expiresAt / 1000)
        });
      }
      
      console.log(`Successfully injected ${cookies.length} cookies for ${domain}`);
      return true;
    } catch (error) {
      console.error('Error injecting cookies:', error);
      return false;
    }
  }

  /**
   * Validate a cookie object
   * @param {Object} cookie - Cookie object to validate
   * @returns {boolean} Is cookie valid
   */
  validateCookie(cookie) {
    // Basic validation
    if (!cookie || typeof cookie !== 'object') return false;
    if (!cookie.name || !cookie.value) return false;
    
    // Check for suspicious values or injection attempts
    if (typeof cookie.name !== 'string' || typeof cookie.value !== 'string') return false;
    if (cookie.name.includes(';') || cookie.value.includes(';')) return false;
    
    return true;
  }

  /**
   * Rotate cookies for a tool
   * @param {string} toolId - ID of the tool
   * @param {Function} refreshCallback - Async function to get fresh cookies
   * @returns {Promise<boolean>} Success status
   */
  async rotateCookies(toolId, refreshCallback) {
    try {
      const cookies = await this.getCookies(toolId);
      
      // Check if cookies need rotation (e.g., 80% of lifetime passed)
      const needsRotation = cookies && cookies.some(cookie => {
        const lifetime = cookie.expiresAt - cookie.createdAt;
        const elapsed = Date.now() - cookie.createdAt;
        return elapsed > (lifetime * 0.8);
      });
      
      if (needsRotation) {
        console.log(`Rotating cookies for tool ${toolId}`);
        const freshCookies = await refreshCallback();
        if (freshCookies) {
          await this.storeCookies(toolId, freshCookies);
          return true;
        }
      }
      
      return false;
    } catch (error) {
      console.error('Error rotating cookies:', error);
      return false;
    }
  }

  /**
   * Clear all cookies for a tool
   * @param {string} toolId - ID of the tool
   * @returns {Promise<void>}
   */
  async clearCookies(toolId) {
    await chrome.storage.local.remove(`cookies_${toolId}`);
    console.log(`Cleared cookies for tool ${toolId}`);
  }

  /**
   * Clear all stored cookies
   * @returns {Promise<void>}
   */
  async clearAllCookies() {
    const data = await chrome.storage.local.get(null);
    const cookieKeys = Object.keys(data).filter(key => key.startsWith('cookies_'));
    
    if (cookieKeys.length > 0) {
      await chrome.storage.local.remove(cookieKeys);
      console.log(`Cleared all cookies (${cookieKeys.length} entries)`);
    }
  }
}

// Export singleton instance
const cookieManager = new CookieManager();
export default cookieManager;
