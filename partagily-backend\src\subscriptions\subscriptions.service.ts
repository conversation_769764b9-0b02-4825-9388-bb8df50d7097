import { Injectable } from '@nestjs/common';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class SubscriptionsService {
  constructor(private readonly prisma: PrismaService) {}

  async findAllPlans() {
    try {
      // Get plans from the database using Prisma client
      const dbPlans = await this.prisma.plan.findMany({
        include: {
          includedTools: {
            include: {
              tool: true
            }
          }
        }
      });

      // Format and return the plans
      return dbPlans.map(plan => ({
        id: plan.id,
        name: plan.name,
        tier: plan.tier,
        price: plan.price,
        description: plan.description,
        features: plan.features,
        includedTools: plan.includedTools.map(planTool => ({
          id: planTool.tool.id,
          name: planTool.tool.name
        }))
      }));
    } catch (error) {
      console.error('Error fetching plans from database:', error);
      throw new Error(`Failed to fetch plans: ${error.message}`);
    }
  }

  async findPlanByName(name: string) {
    try {
      // Get plan by name from the database using Prisma client
      const dbPlan = await this.prisma.plan.findFirst({
        where: {
          name: name
        },
        include: {
          includedTools: {
            include: {
              tool: true
            }
          }
        }
      });

      // If we found the plan, format and return it
      if (dbPlan) {
        return {
          id: dbPlan.id,
          name: dbPlan.name,
          tier: dbPlan.tier,
          price: dbPlan.price,
          description: dbPlan.description,
          features: dbPlan.features,
          includedTools: dbPlan.includedTools.map(planTool => ({
            id: planTool.tool.id,
            name: planTool.tool.name
          }))
        };
      }

      console.log(`Plan with name ${name} not found in database`);
      return null;
    } catch (error) {
      console.error(`Error fetching plan ${name} from database:`, error);
      throw new Error(`Failed to fetch plan by name: ${error.message}`);
    }
  }

  async findUserSubscriptions(userId: string) {
    try {
      // Get user subscriptions from the database using Prisma client
      const dbSubscriptions = await this.prisma.subscription.findMany({
        where: {
          userId: userId
        },
        include: {
          plan: true
        }
      });

      // Format and return the subscriptions
      return dbSubscriptions.map(subscription => ({
        id: subscription.id,
        userId: subscription.userId,
        planId: subscription.planId,
        plan: {
          id: subscription.plan.id,
          name: subscription.plan.name,
          tier: subscription.plan.tier,
          price: subscription.plan.price,
          description: subscription.plan.description,
          features: subscription.plan.features
        },
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        status: subscription.status,
        autoRenew: subscription.autoRenew,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt
      }));
    } catch (error) {
      console.error(`Error fetching subscriptions for user ${userId}:`, error);
      throw new Error(`Failed to fetch user subscriptions: ${error.message}`);
    }
  }

  async findActiveUserSubscription(userId: string) {
    try {
      // Get active user subscription from the database using Prisma client
      const dbSubscription = await this.prisma.subscription.findFirst({
        where: {
          userId: userId,
          status: 'ACTIVE'
        },
        include: {
          plan: true
        }
      });

      // If we found an active subscription, format and return it
      if (dbSubscription) {
        return {
          id: dbSubscription.id,
          userId: dbSubscription.userId,
          planId: dbSubscription.planId,
          plan: {
            id: dbSubscription.plan.id,
            name: dbSubscription.plan.name,
            tier: dbSubscription.plan.tier,
            price: dbSubscription.plan.price,
            description: dbSubscription.plan.description,
            features: dbSubscription.plan.features
          },
          startDate: dbSubscription.startDate,
          endDate: dbSubscription.endDate,
          status: dbSubscription.status,
          autoRenew: dbSubscription.autoRenew,
          createdAt: dbSubscription.createdAt,
          updatedAt: dbSubscription.updatedAt
        };
      }

      console.log(`No active subscription found for user ${userId}`);
      return null;
    } catch (error) {
      console.error(`Error fetching active subscription for user ${userId}:`, error);
      throw new Error(`Failed to fetch active user subscription: ${error.message}`);
    }
  }

  async create(createSubscriptionDto: CreateSubscriptionDto) {
    try {
      // Create a new subscription in the database using Prisma client
      const newSubscription = await this.prisma.subscription.create({
        data: {
          userId: createSubscriptionDto.userId,
          planId: createSubscriptionDto.planId,
          startDate: new Date(),
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
          status: 'ACTIVE',
          autoRenew: createSubscriptionDto.autoRenew ?? true
        },
        include: {
          plan: true
        }
      });

      // Format and return the new subscription
      return {
        id: newSubscription.id,
        userId: newSubscription.userId,
        planId: newSubscription.planId,
        plan: newSubscription.plan ? {
          id: newSubscription.plan.id,
          name: newSubscription.plan.name,
          tier: newSubscription.plan.tier,
          price: newSubscription.plan.price,
          description: newSubscription.plan.description,
          features: newSubscription.plan.features
        } : null,
        startDate: newSubscription.startDate,
        endDate: newSubscription.endDate,
        status: newSubscription.status,
        autoRenew: newSubscription.autoRenew,
        createdAt: newSubscription.createdAt,
        updatedAt: newSubscription.updatedAt
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error(`Failed to create subscription: ${error.message}`);
    }
  }
}
