import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as crypto from 'crypto';

/**
 * CSRF Protection Middleware
 *
 * This middleware handles CSRF token generation and validation
 */
@Injectable()
export class CsrfMiddleware implements NestMiddleware {
  // Store tokens with expiration times
  private readonly tokens: Map<string, { value: string, expires: number }> = new Map();

  // Token expiration time (1 hour)
  private readonly TOKEN_EXPIRATION = 60 * 60 * 1000;

  // Clean up expired tokens every 10 minutes
  constructor() {
    setInterval(() => this.cleanupExpiredTokens(), 10 * 60 * 1000);
  }

  /**
   * Process the request
   */
  async use(req: Request, res: Response, next: NextFunction) {
    // Skip CSRF check for authentication routes
    if (req.path === '/auth/login' || req.path === '/auth/register') {
      return next();
    }

    // Skip CSRF check for GET, HEAD, OPTIONS requests
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      // For GET requests to the CSRF token endpoint, generate a new token
      if (req.method === 'GET' && req.path === '/auth/csrf-token') {
        const token = this.generateToken();
        res.cookie('XSRF-TOKEN', token, {
          httpOnly: false, // Client-side JS needs to read this
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: this.TOKEN_EXPIRATION
        });

        // Store the token with expiration
        this.storeToken(req, token);

        // Set response
        res.status(200).json({ token });
        return;
      }

      // For other GET requests, add CSRF token if user is authenticated
      if (req['user']) {
        const token = this.generateToken();
        res.cookie('XSRF-TOKEN', token, {
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: this.TOKEN_EXPIRATION
        });

        // Store the token with expiration
        this.storeToken(req, token);
      }

      return next();
    }

    // For state-changing requests (POST, PUT, DELETE, etc.), validate the token
    const token = req.headers['x-csrf-token'] || req.headers['x-xsrf-token'];

    if (!token || typeof token !== 'string') {
      throw new UnauthorizedException('CSRF token missing');
    }

    // Validate the token
    if (!this.validateToken(req, token)) {
      throw new UnauthorizedException('Invalid CSRF token');
    }

    // Token is valid, proceed
    next();
  }

  /**
   * Generate a secure random token
   */
  private generateToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Store a token with expiration
   */
  private storeToken(req: Request, token: string): void {
    // Use session ID or IP address as key
    const key = req['sessionID'] || req.ip;

    // Store token with expiration
    this.tokens.set(key, {
      value: token,
      expires: Date.now() + this.TOKEN_EXPIRATION
    });
  }

  /**
   * Validate a token
   */
  private validateToken(req: Request, token: string): boolean {
    // Use session ID or IP address as key
    const key = req['sessionID'] || req.ip;

    // Get stored token
    const storedToken = this.tokens.get(key);

    // Check if token exists and is not expired
    if (!storedToken || storedToken.expires < Date.now()) {
      return false;
    }

    // Check if token matches
    return crypto.timingSafeEqual(
      Buffer.from(token),
      Buffer.from(storedToken.value)
    );
  }

  /**
   * Clean up expired tokens
   */
  private cleanupExpiredTokens(): void {
    const now = Date.now();

    for (const [key, token] of this.tokens.entries()) {
      if (token.expires < now) {
        this.tokens.delete(key);
      }
    }
  }
}
