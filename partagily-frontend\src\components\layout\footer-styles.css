/* Modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600;700&display=swap');

.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.mono-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

.footer {
  background-color: #F9FAFB;
  color: white;
  padding-top: 4rem;
  padding-bottom: 2rem;
  border-top-left-radius: 2rem;
  border-top-right-radius: 2rem;
  margin-top: -2rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.dark .footer {
  background-color: #111827;
  border-top: 1px solid rgba(55, 65, 81, 0.5);
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

/* Add subtle wave decoration to the footer */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(to right, #FFAD00, #e94a9c, #4f8eff);
  opacity: 0.8;
}

.social-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  border: 1px solid rgba(229, 231, 235, 0.8);
}

.dark .social-icon {
  background-color: #1f2937;
  border: none;
}

.social-icon:hover {
  background-color: #FFAD00;
  color: #111827;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 173, 0, 0.15);
}

.dark .social-icon:hover {
  background-color: #FFAD00;
  color: #111827;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 173, 0, 0.2);
}

.footer-link {
  color: #9ca3af;
  transition: color 0.3s;
}

.footer-link:hover {
  color: #facc15;
}
