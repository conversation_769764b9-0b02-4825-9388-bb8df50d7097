import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<string[]>('roles', context.getHandler());
    if (!roles) {
      console.log('RolesGuard: No roles required, allowing access');
      return true;
    }

    console.log('RolesGuard: Required roles:', roles);

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      console.log('RolesGuard: No user in request, denying access');
      return false;
    }

    console.log('RolesGuard: User role:', user.role);

    // Normalize role to lowercase for case-insensitive comparison
    const userRole = typeof user.role === 'string' ? user.role.toLowerCase() : user.role;

    // Convert required roles to lowercase for comparison
    const normalizedRoles = roles.map(role => role.toLowerCase());

    const hasRole = normalizedRoles.includes(userRole);
    console.log(`RolesGuard: User ${hasRole ? 'has' : 'does not have'} required role, ${hasRole ? 'allowing' : 'denying'} access`);

    return hasRole;
  }
}
