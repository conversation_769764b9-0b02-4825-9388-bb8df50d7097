'use client';

import { motion } from 'framer-motion';
import { CreditCard, Building, Mail } from 'lucide-react';
import Link from 'next/link';
import CurvedShape from '../ui/CurvedShape';

const ModernPaymentSection = () => {
  const paymentMethods = [
    {
      name: "Konnect",
      description: "Notre partenaire principal de paiement",
      icon: <CreditCard size={32} className="text-[#e94a9c]" />,
      color: "bg-gradient-to-br from-[rgba(233,74,156,0.2)] to-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.4)]",
      featured: true,
      logo: "🔗", // Placeholder for Konnect logo
      shadowColor: "shadow-[0_8px_30px_rgba(233,74,156,0.3)]"
    },
    {
      name: "E-Dinar",
      description: "Payez avec votre carte E-Dinar",
      icon: <CreditCard size={28} className="text-[#4f8eff]" />,
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]",
      featured: false
    },
    {
      name: "La Poste",
      description: "Paiement via La Poste Tunisienne",
      icon: <Mail size={28} className="text-[#FFAD00]" />,
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]",
      featured: false
    },
    {
      name: "Virement Bancaire",
      description: "Virement direct depuis votre banque",
      icon: <Building size={28} className="text-[#30d158]" />,
      color: "bg-[rgba(48,209,88,0.1)]",
      borderColor: "border-[rgba(48,209,88,0.2)]",
      featured: false
    }
  ];

  return (
    <section className="py-24 relative" id="payment-methods">
      {/* Blob decorations */}
      <div className="blob-decoration blob-blue" style={{ left: 'auto', right: '-100px', top: '20%' }}></div>

      {/* Curved shapes */}
      <CurvedShape position="top" color="#FFAD00" />
      <CurvedShape position="bottom" color="#4f8eff" />

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Méthodes de <span className="text-[#FFAD00]">Paiement Locales</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Aucune méthode de paiement internationale requise. Utilisez les options de paiement que vous avez déjà en Tunisie.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {paymentMethods.map((method, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className={`glass-card p-8 hover-card relative ${
                method.featured ? 'ring-2 ring-[#e94a9c] ring-opacity-60 scale-105 shadow-[0_8px_30px_rgba(233,74,156,0.3)]' : ''
              }`}
            >
              {method.featured && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-[#e94a9c] to-[#ff6b9d] text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                    ⭐ PARTENAIRE PRINCIPAL ⭐
                  </span>
                </div>
              )}
              <div className="flex flex-col items-center text-center">
                <div className={`w-16 h-16 rounded-full ${method.color} flex items-center justify-center mb-6 ${
                  method.featured ? 'ring-2 ring-[#e94a9c] ring-opacity-30' : ''
                }`}>
                  {method.featured && method.logo && (
                    <span className="text-2xl mr-1">{method.logo}</span>
                  )}
                  {method.icon}
                </div>
                <h3 className={`text-2xl font-bold mb-2 ${method.featured ? 'text-[#e94a9c]' : ''}`}>
                  {method.name}
                </h3>
                <p className="text-gray-300">{method.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="bg-gradient-to-r from-[rgba(255,173,0,0.1)] to-[rgba(233,74,156,0.1)] p-8 rounded-2xl border border-[rgba(255,255,255,0.05)]"
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-2">Prêt à commencer ?</h3>
              <p className="text-gray-300">
                Choisissez un plan qui vous convient et commencez à accéder aux outils premium dès aujourd'hui.
              </p>
            </div>
            <Link
              href="#pricing"
              className="btn btn-primary btn-lg hover-glow whitespace-nowrap"
            >
              Voir nos plans
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernPaymentSection;
