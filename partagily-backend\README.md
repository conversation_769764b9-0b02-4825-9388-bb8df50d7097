# Partagily Backend

This is the backend API for the Partagily shared account platform.

## Technologies
Version Node app
- v20.17.0
- Npm 10.8.2
- NestJS 10.2.0
- PostgreSQL 16.1
- Prisma ORM
- TypeScript
- JWT Authentication

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- PostgreSQL (v16.1 or higher)

### Database Setup

1. Make sure PostgreSQL is installed and running
2. Create a new database:

```bash
createdb partagily
```

3. Update the `.env` file with your database connection string:

```
DATABASE_URL="postgresql://username:password@localhost:5432/partagily?schema=public"
```

### Installation

1. Install dependencies:

```bash
npm install
```

2. Generate Prisma client:

```bash
npm run prisma:generate
```

3. Run database migrations:

```bash
npm run prisma:migrate
```

4. Seed the database with initial data:

```bash
npm run prisma:seed

cd partagily-backend; npx prisma studio```

cd partagily-backend; npx prisma migrate status

5. npx prisma migrate dev --name <nom_de_votre_migration_initiale> (opt)

### Running the API

```bash
# Development mode
npm run start:dev

# Production mode
npm run build
npm run start:prod
```

The API will be available at http://localhost:3001

### API Documentation

Swagger documentation is available at http://localhost:3001/api

## Database Schema

The database schema includes the following models:

- **User**: For user authentication and profile information
- **Tool**: For available tools in the marketplace
- **Subscription**: For user subscription status
- **Plan**: For subscription tiers and pricing
- **Order**: For payment tracking
- **Session**: For tracking active user sessions
- **AuditLog**: For security and activity tracking

## Admin Dashboard

The admin dashboard provides the following features:

- **User Management**: View, create, update, and delete users
- **Tool Management**: Manage tools available in the marketplace
- **Subscription Management**: Track and manage user subscriptions
- **Payment Tracking**: Monitor payments and process refunds
- **Analytics**: View user growth, revenue, and tool usage statistics
- **Audit Logs**: Track security-related events and user activities

## Security Features

- **CORS Configuration**: Whitelisted origins including Chrome extension support
- **CSRF Protection**: Token-based protection for forms and API requests
- **Rate Limiting**: Prevents abuse through request throttling
- **Secure Cookies**: HTTP-only, secure cookies with proper attributes
- **Helmet Security Headers**: Protection against common web vulnerabilities
- **Input Validation**: Comprehensive validation for all inputs
- **Audit Logging**: Tracking of security-sensitive actions

## Chrome Extension Support

The backend is configured to support the Partagily Chrome extension through:

- CORS configuration allowing chrome-extension:// origins
- Cookie handling for extension authentication
- API endpoints for cookie injection

## Default Credentials

After seeding the database, you can use the following credentials:

- Admin User:
  - Email: <EMAIL>
  - Password: admin1234 /admin123 

- Regular User:
  - Email: <EMAIL>
  - Password: user123
