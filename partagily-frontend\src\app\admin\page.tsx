'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Package,
  CreditCard,
  TrendingUp,
  AlertTriangle,
  DollarSign,
} from 'lucide-react';
import Link from 'next/link';
import adminService from '@/services/adminService';

// Chart components
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

export default function AdminDashboard() {
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // Use real backend service
        console.log('Fetching dashboard data from backend API');
        const response = await adminService.getDashboardOverview();

        // Fetch user growth data
        let userGrowthData = [];
        try {
          const userGrowthResponse = await adminService.getUserGrowthAnalytics({ timeframe: 'year' });
          userGrowthData = userGrowthResponse.data || [];
        } catch (userGrowthError) {
          console.error('Error fetching user growth data:', userGrowthError);
        }

        // Set dashboard data with additional user growth data
        setDashboardData({
          ...response,
          userGrowthData
        });

        setError(null);
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">⚠️</span>
          <div>
            <p className="font-medium">Backend Connection Error</p>
            <p>{error}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6 text-center">
          <svg className="mx-auto h-24 w-24 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M5 12h14M12 5v14" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900">No Dashboard Data Available</h3>
          <p className="mt-2 text-sm text-gray-500">The backend server is not running or is unreachable.</p>
          <div className="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4 max-w-lg mx-auto">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-gray-800">Developer Information</h3>
                <div className="mt-2 text-sm text-gray-700">
                  <p>To see actual data, please start the backend server. If you're a developer, run:</p>
                  <pre className="mt-2 bg-gray-100 p-2 rounded text-xs overflow-x-auto">cd partagily-backend<br/>npm run start:dev</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Use real data from the API
  const userGrowthData = dashboardData?.userGrowthData || [];
  const revenueData = dashboardData?.revenueOverTime || [];

  // Transform tool usage data for the pie chart
  const toolUsageData = dashboardData?.popularTools?.map(tool => ({
    name: tool.name,
    value: tool.subscriptions
  })) || [];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Admin Dashboard</h1>
        <div className="text-sm text-gray-500">Last updated: {new Date().toLocaleString()}</div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <motion.div
          whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
          className="bg-white rounded-xl shadow-md overflow-hidden"
        >
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Users className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.userStats?.totalUsers || 0}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <p className="text-sm text-green-500">
                  +{dashboardData?.userStats?.newUsers || 0} new users this month
                </p>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-3">
            <Link
              href="/admin/users"
              className="text-sm font-medium text-blue-600 hover:text-blue-800"
            >
              View all users →
            </Link>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
          className="bg-white rounded-xl shadow-md overflow-hidden"
        >
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <DollarSign className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  ${dashboardData?.revenueStats?.totalRevenue?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                <p className="text-sm text-green-500">
                  ${dashboardData?.revenueStats?.periodRevenue?.toFixed(2) || '0.00'} this month
                </p>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-3">
            <Link
              href="/admin/payments"
              className="text-sm font-medium text-blue-600 hover:text-blue-800"
            >
              View all payments →
            </Link>
          </div>
        </motion.div>

        <motion.div
          whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
          className="bg-white rounded-xl shadow-md overflow-hidden"
        >
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <CreditCard className="w-6 h-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData?.subscriptionStats?.totalActiveSubscriptions || 0}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <AlertTriangle className="w-4 h-4 text-orange-500 mr-1" />
                <p className="text-sm text-orange-500">
                  {dashboardData?.subscriptionStats?.expiringSubscriptions || 0} expiring soon
                </p>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-3">
            <Link
              href="/admin/subscriptions"
              className="text-sm font-medium text-blue-600 hover:text-blue-800"
            >
              View all subscriptions →
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-md p-6"
        >
          <h2 className="text-lg font-semibold mb-4">User Growth</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={userGrowthData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(date) => {
                  // Format date to show only month
                  return new Date(date).toLocaleString('default', { month: 'short' });
                }} />
                <YAxis />
                <Tooltip labelFormatter={(date) => {
                  // Format date for tooltip
                  return new Date(date).toLocaleDateString();
                }} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="users"
                  stroke="#8884d8"
                  activeDot={{ r: 8 }}
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-md p-6"
        >
          <h2 className="text-lg font-semibold mb-4">Revenue</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={revenueData}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(date) => {
                  // Format date to show only month
                  return new Date(date).toLocaleString('default', { month: 'short' });
                }} />
                <YAxis />
                <Tooltip
                  labelFormatter={(date) => {
                    // Format date for tooltip
                    return new Date(date).toLocaleDateString();
                  }}
                  formatter={(value) => [`$${value}`, 'Revenue']}
                />
                <Legend />
                <Bar dataKey="revenue" fill="#fbbf24" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-md p-6"
        >
          <h2 className="text-lg font-semibold mb-4">Tool Usage</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={toolUsageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {toolUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-md p-6"
        >
          <h2 className="text-lg font-semibold mb-4">Recent Transactions</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dashboardData?.recentTransactions?.map((transaction: any) => (
                  <tr key={transaction.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {transaction.orderNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {transaction.user?.name || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${transaction.amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          transaction.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : transaction.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : transaction.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {transaction.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(transaction.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
