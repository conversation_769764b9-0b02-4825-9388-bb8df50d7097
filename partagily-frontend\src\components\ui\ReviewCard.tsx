'use client';

import React from 'react';
import { motion } from 'framer-motion';
import './review-styles.css';

export interface ReviewProps {
  rating: number;
  name: string;
  text: string;
  maxRating?: number;
}

const ReviewCard: React.FC<ReviewProps> = ({
  rating,
  name,
  text,
  maxRating = 5
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="review-card bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 dark:border-gray-700"
    >
      <div className="p-8">
        {/* Stars at the top */}
        <div className="flex justify-center mb-4">
          <div className="flex items-center">
            {Array.from({ length: maxRating }).map((_, i) => (
              <span key={i} className={`text-2xl ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
                ★
              </span>
            ))}
          </div>
        </div>

        {/* Review text */}
        <p className="text-lg text-gray-700 dark:text-gray-300 mb-6 leading-relaxed italic text-center">
          "{text}"
        </p>

        {/* User info with avatar */}
        <div className="flex items-center justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-[#e94a9c] to-[#FFAD00] rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
            {name.charAt(0)}
          </div>
          <div className="text-center">
            <div className="font-bold text-lg text-gray-900 dark:text-white">{name}</div>
            <div className="text-sm text-[#e94a9c] font-medium">🇹🇳 Utilisateur Tunisien</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ReviewCard;
