'use client';

import React from 'react';
import { motion } from 'framer-motion';
import './review-styles.css';

export interface ReviewProps {
  rating: number;
  name: string;
  text: string;
  maxRating?: number;
}

const ReviewCard: React.FC<ReviewProps> = ({
  rating,
  name,
  text,
  maxRating = 5
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="review-card"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-3">
          <div className="review-rating">
            {Array.from({ length: maxRating }).map((_, i) => (
              <span key={i} className={i < rating ? 'review-star' : 'review-star-empty'}>
                ★
              </span>
            ))}
            {rating > 0 && (
              <span className="ml-2 text-gray-500 font-medium">{rating.toFixed(1)}</span>
            )}
          </div>
          <div className="review-name">{name}</div>
        </div>
        <p className="review-text">{text}</p>
      </div>
    </motion.div>
  );
};

export default ReviewCard;
