# Partagily Backend Scripts

This directory contains utility scripts for the Partagily backend.

## Tool Import Script

The `import-tools.ts` script allows you to import tools into the database from a JSON file.

### Usage

```bash
npm run import:tools <path-to-json-file>
```

For example:

```bash
npm run import:tools data/sample-tools.json
```

### JSON Format

The JSON file should contain an array of tool objects with the following structure:

```json
[
  {
    "name": "Tool Name",
    "description": "Tool description",
    "icon": "/path/to/icon.png",
    "price": 9.99,
    "category": "Category",
    "status": "AVAILABLE",
    "requiredPlan": "STANDARD",
    "domain": "example.com",
    "originalPrice": 12.99
  }
]
```

### Field Descriptions

- `name` (required): The name of the tool
- `description` (required): A description of the tool
- `icon` (optional): Path to the tool's icon
- `price` (required): The price of the tool
- `category` (required): Must be one of the valid ToolCategory enum values:
  - Streaming
  - Stock
  - Publishing
  - Design
  - Video
  - AI
  - Music
  - Ecommerce
  - Writing
  - Networking
- `status` (optional, defaults to "AVAILABLE"): Must be one of the valid ToolStatus enum values:
  - AVAILABLE
  - UNAVAILABLE
  - MAINTENANCE
  - COMING_SOON
- `requiredPlan` (required): Must be one of the valid PlanTier enum values:
  - STANDARD
  - PREMIUM
  - GOLD
- `domain` (optional): The domain of the tool's website
- `originalPrice` (optional): The original price of the tool before any discount

### Behavior

- If a tool with the same name already exists in the database, it will be updated with the new values
- If a tool doesn't exist, a new one will be created
- Any errors during processing will be logged, but the script will continue processing other tools

### Example

See `data/sample-tools.json` for an example of a valid tools JSON file.
