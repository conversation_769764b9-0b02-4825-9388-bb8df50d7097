'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';

const CountUp = ({ end, duration = 2, suffix = '' }) => {
  const [count, setCount] = useState(0);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView) {
      let startTime;
      let animationFrame;

      const animate = (timestamp) => {
        if (!startTime) startTime = timestamp;
        const progress = timestamp - startTime;
        const percentage = Math.min(progress / (duration * 1000), 1);

        setCount(Math.floor(percentage * end));

        if (percentage < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);

      return () => cancelAnimationFrame(animationFrame);
    }
  }, [isInView, end, duration]);

  return <span ref={ref}>{count}{suffix}</span>;
};

const StatsSection = () => {
  const stats = [
    {
      value: 10000,
      suffix: "+",
      label: "Happy Users",
      icon: "👥"
    },
    {
      value: 50000,
      suffix: "+",
      label: "Monthly Visitors",
      icon: "📊"
    },
    {
      value: 120,
      suffix: "+",
      label: "Tools Shared",
      icon: "🛠️"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-r from-yellow-400 to-pink-400 dark:from-[#FFAD00]/20 dark:to-[#e94a9c]/20 text-gray-900 dark:text-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            Our <span className="text-white">Numbers</span> 📈
          </h2>
          <p className="text-xl max-w-3xl mx-auto">
            Join thousands of Tunisians who are already enjoying premium tools without the payment hassle.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 * index, duration: 0.5 }}
              className="bg-white/20 dark:bg-card-bg-elevated backdrop-blur-sm p-8 rounded-xl border border-white/10 dark:border-gray-700/30 shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col items-center text-center hover-card"
            >
              <div className="text-5xl mb-4">{stat.icon}</div>
              <div className="text-5xl font-bold mb-2 terminal-text">
                <CountUp end={stat.value} suffix={stat.suffix} />
              </div>
              <div className="text-xl font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
