import { NextRequest, NextResponse } from 'next/server';

// This would be a database in a real application
let tools = [];

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  
  // Find the tool by ID
  const tool = tools.find(t => t.id === id);
  
  if (!tool) {
    return NextResponse.json({ message: 'Tool not found' }, { status: 404 });
  }
  
  return NextResponse.json(tool);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  const data = await request.json();
  
  // Find the tool index
  const index = tools.findIndex(t => t.id === id);
  
  if (index === -1) {
    return NextResponse.json({ message: 'Tool not found' }, { status: 404 });
  }
  
  // Update the tool
  tools[index] = {
    ...tools[index],
    ...data,
    updatedAt: new Date().toISOString(),
  };
  
  return NextResponse.json({ 
    message: 'Tool updated successfully',
    tool: tools[index]
  });
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  
  // Find the tool index
  const index = tools.findIndex(t => t.id === id);
  
  if (index === -1) {
    return NextResponse.json({ message: 'Tool not found' }, { status: 404 });
  }
  
  // Remove the tool
  tools.splice(index, 1);
  
  return NextResponse.json({ 
    message: 'Tool deleted successfully' 
  });
}
