'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { Moon, Sun, Monitor } from 'lucide-react';
import { useState } from 'react';

export default function ThemeToggle() {
  const { theme, toggleTheme, isAutoTheme, setAutoTheme } = useTheme();
  const setTheme = (newTheme: 'light' | 'dark') => {
    if (newTheme === 'light') {
      toggleTheme();
    } else if (newTheme === 'dark' && theme === 'light') {
      toggleTheme();
    }
  };
  const [showMenu, setShowMenu] = useState(false);

  // If auto theme is enabled, we should show the system icon
  const getIcon = () => {
    if (isAutoTheme) {
      return <Monitor className="h-5 w-5" />;
    } else {
      return theme === 'light' ? <Moon className="h-5 w-5" /> : <Sun className="h-5 w-5" />;
    }
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        aria-label="Theme settings"
      >
        {getIcon()}
      </button>

      {showMenu && (
        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-900 rounded-xl shadow-xl py-2 z-10">
          <div className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700">
            Theme Settings
          </div>

          <button
            onClick={() => {
              setAutoTheme(true);
              setShowMenu(false);
            }}
            className={`flex items-center w-full px-4 py-2 text-sm text-left ${
              isAutoTheme ? 'text-yellow-500 dark:text-yellow-400 bg-yellow-50 dark:bg-gray-800' : 'text-gray-700 dark:text-gray-300'
            } hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors`}
          >
            <Monitor className="h-4 w-4 mr-2" />
            System preference
          </button>

          <button
            onClick={() => {
              setAutoTheme(false);
              setTheme('light');
              setShowMenu(false);
            }}
            className={`flex items-center w-full px-4 py-2 text-sm text-left ${
              !isAutoTheme && theme === 'light' ? 'text-yellow-500 dark:text-yellow-400 bg-yellow-50 dark:bg-gray-800' : 'text-gray-700 dark:text-gray-300'
            } hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors`}
          >
            <Sun className="h-4 w-4 mr-2" />
            Light mode
          </button>

          <button
            onClick={() => {
              setAutoTheme(false);
              setTheme('dark');
              setShowMenu(false);
            }}
            className={`flex items-center w-full px-4 py-2 text-sm text-left ${
              !isAutoTheme && theme === 'dark' ? 'text-yellow-500 dark:text-yellow-400 bg-yellow-50 dark:bg-gray-800' : 'text-gray-700 dark:text-gray-300'
            } hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors`}
          >
            <Moon className="h-4 w-4 mr-2" />
            Dark mode
          </button>
        </div>
      )}
    </div>
  );
}
