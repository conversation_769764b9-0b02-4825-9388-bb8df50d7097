import { Controller, Get, Put, Body, UseGuards, HttpStatus, HttpException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';
import { AuditService } from '../../audit/audit.service';

@ApiTags('Admin Settings')
@Controller('admin/settings')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminSettingsController {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly auditService: AuditService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get platform settings' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Settings retrieved successfully' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden' })
  async getSettings() {
    try {
      // Get settings from database
      const settings = await this.prismaService.setting.findMany();

      // Transform settings into a structured object
      const settingsObject = {
        general: {},
        security: {},
        notifications: {},
        payment: {},
      };

      // Group settings by category
      for (const setting of settings) {
        const [category, key] = setting.key.split('.');
        if (!settingsObject[category]) {
          settingsObject[category] = {};
        }

        // Convert string values to appropriate types
        let value: any = setting.value;
        if (value === 'true') value = true;
        else if (value === 'false') value = false;
        else if (!isNaN(Number(value)) && value !== '') value = Number(value);

        settingsObject[category][key] = value;
      }

      // If no settings found, return default settings
      if (settings.length === 0) {
        return this.getDefaultSettings();
      }

      return settingsObject;
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve settings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put()
  @ApiOperation({ summary: 'Update platform settings' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Settings updated successfully' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid settings data' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Forbidden' })
  async updateSettings(@Body() settingsData: any) {
    try {
      // Flatten the settings object for storage
      const flattenedSettings = [];

      // Process each category
      for (const category in settingsData) {
        for (const key in settingsData[category]) {
          flattenedSettings.push({
            key: `${category}.${key}`,
            value: String(settingsData[category][key]),
          });
        }
      }

      // Update settings in database
      for (const setting of flattenedSettings) {
        await this.prismaService.setting.upsert({
          where: { key: setting.key },
          update: { value: setting.value },
          create: { key: setting.key, value: setting.value },
        });
      }

      // Log the audit event
      await this.auditService.createAuditLog({
        action: 'UPDATE_SETTINGS',
        entityType: 'SETTINGS',
        entityId: 'global',
        description: 'Updated platform settings',
        metadata: { categories: Object.keys(settingsData) },
      });

      return { message: 'Settings updated successfully' };
    } catch (error) {
      throw new HttpException(
        'Failed to update settings',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Helper method to get default settings
  private getDefaultSettings() {
    return {
      general: {
        siteName: 'Partagily',
        siteDescription: 'Access premium tools through subscription plans',
        contactEmail: '<EMAIL>',
        enableMaintenance: false,
      },
      security: {
        sessionTimeout: 60,
        maxLoginAttempts: 5,
        passwordMinLength: 8,
        requirePasswordReset: 90,
      },
      notifications: {
        enableEmailNotifications: true,
        enableAdminAlerts: true,
        notifyOnNewUser: true,
        notifyOnNewSubscription: true,
        notifyOnPaymentFailure: true,
      },
      payment: {
        currency: 'TND',
        enableKonnect: true,
        testMode: true,
        konnectApiKey: '',
        konnectMerchantId: '',
      },
    };
  }
}
