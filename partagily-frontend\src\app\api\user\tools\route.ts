import { NextRequest, NextResponse } from 'next/server';

// Helper function to convert admin tools format to user tools format
const convertAdminToolToUserTool = (adminTool: any) => {
  // Extract the first plan for pricing info
  const firstPlan = adminTool.plans && adminTool.plans.length > 0 
    ? adminTool.plans[0] 
    : { price: 0, name: 'Basic' };
  
  // Find the popular plan if any
  const popularPlan = adminTool.plans?.find((plan: any) => plan.isPopular) || firstPlan;
  
  return {
    id: adminTool.id,
    name: adminTool.name,
    description: adminTool.description,
    logo: adminTool.logoUrl ? '🌐' : '📦', // Use emoji as fallback if no logo
    logoUrl: adminTool.logoUrl,
    category: adminTool.category || 'Other',
    price: popularPlan.price,
    originalPrice: popularPlan.price * 1.2, // Just for display purposes
    popular: adminTool.plans?.some((plan: any) => plan.isPopular) || false,
    isActive: adminTool.isActive,
    plans: adminTool.plans,
    websiteUrl: adminTool.websiteUrl,
  };
};

export async function GET(request: NextRequest) {
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const search = url.searchParams.get('search') || '';
    const category = url.searchParams.get('category') || '';
    
    // This is a server component, so we can't access localStorage directly
    // We'll return a script that will fetch from localStorage on the client side
    const script = `
      <script>
        (function() {
          try {
            // Get tools from localStorage
            const storedTools = localStorage.getItem('partagily-tools');
            let adminTools = storedTools ? JSON.parse(storedTools) : [];
            
            // Filter active tools only
            adminTools = adminTools.filter(tool => tool.isActive);
            
            // Convert admin tools to user tools format
            const userTools = adminTools.map(adminTool => {
              // Extract the first plan for pricing info
              const firstPlan = adminTool.plans && adminTool.plans.length > 0 
                ? adminTool.plans[0] 
                : { price: 0, name: 'Basic' };
              
              // Find the popular plan if any
              const popularPlan = adminTool.plans?.find(plan => plan.isPopular) || firstPlan;
              
              return {
                id: adminTool.id,
                name: adminTool.name,
                description: adminTool.description,
                logo: adminTool.logoUrl ? '🌐' : '📦', // Use emoji as fallback if no logo
                logoUrl: adminTool.logoUrl,
                category: adminTool.category || 'Other',
                price: popularPlan.price,
                originalPrice: popularPlan.price * 1.2, // Just for display purposes
                popular: adminTool.plans?.some(plan => plan.isPopular) || false,
                isActive: adminTool.isActive,
                plans: adminTool.plans,
                websiteUrl: adminTool.websiteUrl,
              };
            });
            
            // Send the tools to the parent window
            window.parent.postMessage({
              type: 'USER_TOOLS_DATA',
              tools: userTools
            }, '*');
          } catch (error) {
            console.error('Error fetching tools from localStorage:', error);
            window.parent.postMessage({
              type: 'USER_TOOLS_ERROR',
              error: error.message
            }, '*');
          }
        })();
      </script>
    `;
    
    // Return HTML with the script
    return new NextResponse(
      `<!DOCTYPE html><html><head><title>Tools Data</title></head><body>${script}</body></html>`,
      {
        headers: {
          'Content-Type': 'text/html',
        },
      }
    );
  } catch (error: any) {
    console.error('Error in user tools API route:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch tools',
      message: error.message 
    }, { status: 500 });
  }
}
