import { Controller, Get, Query, Logger, HttpStatus, HttpCode } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';

@ApiTags('payment')
@Controller('payment/cancel')
export class PaymentCancelController {
  private readonly logger = new Logger(PaymentCancelController.name);

  constructor(private readonly paymentService: PaymentService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Konnect payment cancellation' })
  @ApiResponse({ status: 200, description: 'Cancellation processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiQuery({ name: 'payment_ref', required: true, type: String })
  async handleCancellation(@Query('payment_ref') paymentRef: string): Promise<{ success: boolean; message: string; data?: any }> {
    this.logger.log(`Received cancellation for payment reference: ${paymentRef}`);

    if (!paymentRef) {
      this.logger.warn('Cancellation received without payment_ref');
      return { success: false, message: 'Missing payment reference' };
    }

    try {
      const paymentData = await this.paymentService.handleCancelledPayment(paymentRef);
      return paymentData;
    } catch (error) {
      this.logger.error(`Error processing cancellation: ${error.message}`, error.stack);
      return { success: false, message: 'Error processing cancellation' };
    }
  }
}
