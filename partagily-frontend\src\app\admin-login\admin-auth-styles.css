/* Modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600;700&display=swap');

.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.mono-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #1e293b, #0f172a);
  padding: 2rem 1rem;
}

@media (min-width: 640px) {
  .auth-container {
    padding: 3rem 2rem;
  }
}

@media (min-width: 768px) {
  .auth-container {
    padding: 5rem 2rem;
  }
}

.auth-card {
  max-width: 28rem;
  width: 100%;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.auth-header {
  text-align: center;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 1.875rem;
  font-weight: 700;
  font-family: var(--font-geist-sans), 'Inter', sans-serif;
  letter-spacing: -0.02em;
}

.auth-highlight {
  color: #facc15;
}

.auth-input {
  appearance: none;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
}

.auth-input:focus {
  outline: none;
  border-color: #facc15;
  box-shadow: 0 0 0 3px rgba(250, 204, 21, 0.3);
}

.auth-button {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 9999px;
  font-weight: 500;
  color: #111111;
  background-color: #facc15;
  transition: all 0.3s;
  cursor: pointer;
}

.auth-button:hover {
  background-color: #eab308;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.auth-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.auth-link {
  color: #facc15;
  font-weight: 500;
  transition: color 0.3s;
}

.auth-link:hover {
  color: #eab308;
}

.auth-divider {
  position: relative;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.auth-divider-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  border-top: 1px solid #e5e7eb;
}

.auth-divider-text {
  position: relative;
  display: flex;
  justify-content: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.auth-divider-text span {
  padding: 0 0.5rem;
  background-color: white;
}

.auth-footer {
  height: 0.5rem;
  background: linear-gradient(to right, #facc15, #f472b6, #3b82f6);
}
