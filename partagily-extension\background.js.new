// Background Service Worker

// Initialize CSRF token storage
let csrfToken = null;

// API configuration
const API_URL = 'http://localhost:3001';
const API_PROD_URL = 'https://api.partagily.com';

// Utility functions
function sanitizeString(str) {
  if (typeof str !== 'string') return '';
  return str.replace(/[^\w\s.-]/gi, '');
}

function sanitizeObject(obj) {
  if (!obj || typeof obj !== 'object') return {};
  const sanitized = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      if (typeof obj[key] === 'string') {
        sanitized[key] = obj[key]; // We'll trust the strings for now
      } else if (typeof obj[key] === 'number' || typeof obj[key] === 'boolean') {
        sanitized[key] = obj[key];
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitized[key] = sanitizeObject(obj[key]);
      }
    }
  }
  return sanitized;
}

function validateAndSanitizeDomain(domain) {
  if (typeof domain !== 'string') return '';
  // Simple domain validation regex
  const domainRegex = /^([a-z0-9]+(-[a-z0-9]+)*\.)+[a-z]{2,}$/i;
  const sanitized = domain.trim().toLowerCase();
  return domainRegex.test(sanitized) ? sanitized : '';
}

// Cookie manager
const cookieManager = {
  async initialize() {
    console.log('Cookie manager initialized');
    return true;
  },
  
  async getCookies(toolId) {
    console.log(`Getting cookies for tool ${toolId}`);
    return [];
  },
  
  async storeCookies(toolId, cookies) {
    console.log(`Storing ${cookies.length} cookies for tool ${toolId}`);
    return true;
  },
  
  async injectCookies(domain, cookies) {
    console.log(`Injecting ${cookies.length} cookies for domain ${domain}`);
    
    try {
      for (const cookie of cookies) {
        await chrome.cookies.set({
          url: `https://${domain}`,
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain || domain,
          path: cookie.path || '/',
          secure: cookie.secure !== false,
          httpOnly: cookie.httpOnly || false,
          sameSite: cookie.sameSite || 'lax',
          expirationDate: new Date(cookie.expiresAt || Date.now() + 86400000).getTime() / 1000
        });
      }
      return true;
    } catch (error) {
      console.error('Error injecting cookies:', error);
      return false;
    }
  },
  
  async clearCookies(toolId) {
    console.log(`Clearing cookies for tool ${toolId}`);
    return true;
  },
  
  async clearAllCookies() {
    console.log('Clearing all cookies');
    return true;
  },
  
  async rotateCookies(toolId, fetchCallback) {
    console.log(`Rotating cookies for tool ${toolId}`);
    return true;
  }
};

chrome.runtime.onInstalled.addListener(async () => {
  console.log('Partagily extension installed');

  // Initialize storage with default values
  await chrome.storage.local.set({
    isLoggedIn: false,
    user: null,
    activeTools: [],
    lastTokenRefresh: null
  });

  // Initialize cookie manager
  await cookieManager.initialize();
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    // Validate and sanitize the message
    if (!message || typeof message !== 'object') {
      console.error('Invalid message received');
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Sanitize the action
    const action = sanitizeString(message.action);
    console.log('Background received message:', action);

    // Process based on action type
    switch (action) {
      case 'openToolTab':
        if (message.data && typeof message.data === 'object') {
          handleOpenToolTab(sanitizeObject(message.data), sendResponse);
        } else {
          sendResponse({ success: false, error: 'Invalid tool data' });
        }
        return true;
        
      case 'checkAccess':
        if (message.data && typeof message.data === 'object') {
          checkToolAccess(sanitizeObject(message.data), sendResponse);
        } else {
          sendResponse({ success: false, error: 'Invalid access check data' });
        }
        return true;

      default:
        console.warn(`Unknown action: ${action}`);
        sendResponse({ success: false, error: 'Unknown action' });
        return true;
    }
  } catch (error) {
    console.error('Error processing message:', error);
    sendResponse({ success: false, error: 'Internal error processing message' });
    return true;
  }
});

/**
 * Handle opening a tool in a new tab and injecting cookies
 * @param {Object} data - Tool data
 * @param {Function} sendResponse - Function to send response back to caller
 */
async function handleOpenToolTab(data, sendResponse) {
  try {
    if (!data || !data.toolId || !data.url) {
      throw new Error('Invalid tool data');
    }
    
    // Create a new tab with the tool URL
    const tab = await chrome.tabs.create({ url: data.url });
    
    // Check if the user has access to the tool
    const accessResult = await checkToolAccess(data);
    
    if (!accessResult.hasAccess) {
      // User doesn't have access, but we've already opened the tab
      // The content script will handle showing an error message
      sendResponse({ 
        success: true, 
        tabId: tab.id,
        hasAccess: false,
        message: accessResult.message || 'You do not have access to this tool'
      });
      return;
    }
    
    // User has access, inject cookies
    const domain = new URL(data.url).hostname;
    
    // Mock cookies for Netflix
    const mockCookies = [
      {
        name: 'NetflixId',
        value: 'v=2&ct=BQAOAAEBEFJxjYwnsi9-D_ciY-kO4RuB8MHw9Vd4Ow4yvHzPnJLvHkgKdA',
        domain: domain,
        path: '/',
        secure: true,
        httpOnly: false,
        sameSite: 'lax',
        expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
      },
      {
        name: 'SecureNetflixId',
        value: 'v=2&mac=AQEAEQABABTuVJ_GBTfgj9bIWLBj2Lh87HEqLvtcvRM',
        domain: domain,
        path: '/',
        secure: true,
        httpOnly: true,
        sameSite: 'lax',
        expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
      }
    ];
    
    // Inject the cookies
    const success = await cookieManager.injectCookies(domain, mockCookies);
    
    sendResponse({ 
      success: true, 
      tabId: tab.id,
      hasAccess: true,
      injected: success,
      message: success ? 'Cookies injected successfully' : 'Failed to inject cookies'
    });
  } catch (error) {
    console.error('Error opening tool tab:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Check if the user has access to a tool
 * @param {Object} data - Tool data
 * @param {Function} sendResponse - Function to send response back to caller (optional)
 * @returns {Promise<Object>} - Result of the check
 */
async function checkToolAccess(data, sendResponse = null) {
  try {
    if (!data || !data.toolId) {
      throw new Error('Invalid tool data');
    }
    
    // For development, allow access to Netflix tool
    if (data.toolId === '520e0f4b-1273-475e-b2f8-2de932eebfd8' || 
        data.url?.includes('netflix.com')) {
      const result = { 
        success: true, 
        hasAccess: true,
        message: 'You have access to this tool'
      };
      
      if (sendResponse) sendResponse(result);
      return result;
    }
    
    // For other tools, check with the backend (mock for now)
    const result = { 
      success: true, 
      hasAccess: false,
      message: 'You do not have access to this tool. Please subscribe to a plan that includes this tool.'
    };
    
    if (sendResponse) sendResponse(result);
    return result;
  } catch (error) {
    console.error('Error checking tool access:', error);
    const result = { 
      success: false, 
      hasAccess: false,
      error: error.message
    };
    
    if (sendResponse) sendResponse(result);
    return result;
  }
}

// Log cookie event to API (simplified)
async function logCookieEventToAPI(eventData) {
  console.log('Logging cookie event:', eventData);
  return true;
}
