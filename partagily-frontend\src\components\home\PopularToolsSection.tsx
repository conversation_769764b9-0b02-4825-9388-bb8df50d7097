'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

const PopularToolsSection = () => {
  const popularTools = [
    {
      name: "Canva Pro",
      description: "Créez des designs professionnels facilement",
      icon: "🎨",
      logo: (
        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
          C
        </div>
      ),
      category: "Design",
      users: "3,500+",
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]"
    },
    {
      name: "ChatGPT Plus",
      description: "Assistant IA avancé pour tous vos besoins",
      icon: "🤖",
      logo: (
        <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
          AI
        </div>
      ),
      category: "Intelligence Artificielle",
      users: "2,800+",
      color: "bg-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.2)]"
    },
    {
      name: "Grammarly Premium",
      description: "Améliorez votre écriture en anglais et français",
      icon: "✍️",
      logo: (
        <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
          G
        </div>
      ),
      category: "Écriture",
      users: "2,200+",
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]"
    }
  ];

  return (
    <section className="py-24 relative">
      {/* Background decorations */}
      <div className="blob-decoration blob-blue" style={{ left: '-100px', top: '10%' }}></div>
      <div className="blob-decoration blob-yellow" style={{ right: '-100px', bottom: '10%' }}></div>

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Outils <span className="text-[#FFAD00]">Les Plus Populaires</span> en Tunisie
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Découvrez les outils premium les plus utilisés par les Tunisiens sur notre plateforme.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {popularTools.map((tool, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className="glass-card p-8 hover-card relative overflow-hidden"
            >
              {/* Background gradient */}
              <div className={`absolute inset-0 ${tool.color} opacity-50`}></div>
              
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    {tool.logo}
                    <div className="text-3xl">{tool.icon}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-400 uppercase tracking-wide">{tool.category}</div>
                    <div className="text-lg font-bold text-[#FFAD00]">{tool.users} utilisateurs</div>
                  </div>
                </div>

                <h3 className="text-2xl font-bold mb-3">{tool.name}</h3>
                <p className="text-gray-300 mb-6">{tool.description}</p>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-400">Disponible maintenant</span>
                  <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-center"
        >
          <div className="glass-card p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">
              Plus de <span className="text-[#e94a9c]">120+ outils premium</span> disponibles
            </h3>
            <p className="text-gray-300 mb-6">
              Netflix, Spotify, Adobe Creative Suite, Microsoft 365, et bien plus encore...
            </p>
            <Link
              href="/tools"
              className="btn btn-primary btn-lg btn-icon hover-glow"
            >
              Voir tous les outils <ArrowRight size={20} />
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PopularToolsSection;
