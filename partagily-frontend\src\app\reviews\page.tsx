'use client';

import React from 'react';
import { motion } from 'framer-motion';
import ReviewCard from '@/components/ui/ReviewCard';

export default function ReviewsPage() {
  const reviews = [
    {
      name: "<PERSON>",
      rating: 3,
      text: "I've gone on more dates in a month than I have in a year, thanks to this app!"
    },
    {
      name: "<PERSON>",
      rating: 5,
      text: "This platform helped me achieve my learning goals; they really care about helping users to grow! 📝 👍"
    }
  ];

  return (
    <div className="min-h-screen pt-20 pb-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            User <span className="text-yellow-400">Reviews</span>
          </h1>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            See what our users are saying about Partagily.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {reviews.map((review, index) => (
            <ReviewCard
              key={index}
              name={review.name}
              rating={review.rating}
              text={review.text}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
