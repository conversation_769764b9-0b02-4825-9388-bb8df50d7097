import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Tool } from '../../tools/entities/tool.entity';
import { Plan } from '../../subscriptions/entities/plan.entity';

export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
  CANCELLED = 'CANCELLED',
}

export enum PaymentMethod {
  KONNECT = 'KONNECT',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  orderNumber: string;

  @Column()
  userId: string;

  @Column({ nullable: true })
  toolId: string;

  @Column({ nullable: true })
  toolName: string;

  @Column({ nullable: true })
  planId: string;

  @Column({ nullable: true })
  planName: string;

  @Column('decimal', { precision: 10, scale: 2 })
  amount: number;

  @Column({ default: 'TND' })
  currency: string;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status: OrderStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.KONNECT,
  })
  paymentMethod: PaymentMethod;

  @Column({ nullable: true })
  paymentId: string;

  @Column({ nullable: true })
  paymentUrl: string;

  @Column({ nullable: true })
  transactionId: string;

  @Column({ nullable: true })
  receiptUrl: string;

  @Column({ nullable: true, type: 'decimal', precision: 10, scale: 2 })
  refundAmount: number;

  @Column({ nullable: true })
  refundReason: string;

  @Column({ nullable: true, type: 'timestamp' })
  refundedAt: Date;

  @Column({ nullable: true, type: 'timestamp' })
  expiryDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, user => user.orders)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Tool, { nullable: true })
  @JoinColumn({ name: 'toolId' })
  tool: Tool;

  @ManyToOne(() => Plan, { nullable: true })
  @JoinColumn({ name: 'planId' })
  plan: Plan;

  // For audit logging
  previousStatus?: OrderStatus;
}
