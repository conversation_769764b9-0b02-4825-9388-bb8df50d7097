/* Modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600;700&display=swap');

.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.mono-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  transition: all 0.3s;
}

.navbar-transparent {
  background-color: transparent;
  color: #111111;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  height: 90px;
}

.dark .navbar-transparent {
  color: #F3F4F6;
}

.navbar-solid {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: #111111;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  height: 90px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.dark .navbar-solid {
  background-color: rgba(17, 24, 39, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: #F3F4F6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}

.hover-bounce {
  transition: all 0.3s;
  position: relative;
  font-weight: 500;
}

.hover-bounce:hover {
  transform: translateY(-2px);
  color: var(--highlight-yellow);
}

.hover-bounce::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--highlight-yellow);
  transition: width 0.3s ease;
}

.hover-bounce:hover::after {
  width: 100%;
}

.dark .hover-bounce:hover {
  color: var(--highlight-yellow-dark);
}

.dark .hover-bounce::after {
  background-color: var(--highlight-yellow-dark);
}
