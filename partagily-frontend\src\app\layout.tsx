import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { Inter } from "next/font/google";
import "./globals.css";
import { Metadata } from "next";
import '@fontsource/fira-code/400.css';
import '@fontsource/fira-code/500.css';
import '@fontsource/fira-code/600.css';
import '@fontsource/fira-code/700.css';

import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { CartProvider } from "@/contexts/CartContext";
import LayoutWrapper from "./layout-wrapper";
import Script from "next/script";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Partagily - Plateforme de Comptes Partagés",
  description: "Accédez aux outils premium avec des méthodes de paiement locales tunisiennes",
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', sizes: '32x32' }
    ],
    apple: '/favicon.svg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script dangerouslySetInnerHTML={{
          __html: `
            (function() {
              try {
                // Check if auto theme is enabled
                const autoTheme = localStorage.getItem('autoTheme');
                const isAuto = autoTheme !== null ? autoTheme === 'true' : true;

                if (isAuto) {
                  // Use system preference
                  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                  if (prefersDark) {
                    document.documentElement.classList.add('dark');
                    document.body.classList.add('dark-mode');
                  }
                } else {
                  // Use saved preference
                  const savedTheme = localStorage.getItem('theme');
                  if (savedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                    document.body.classList.add('dark-mode');
                  }
                }
              } catch (e) {
                // Fail silently if localStorage is not available
                console.warn('Theme initialization failed:', e);
              }
            })();
          `
        }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased`}
        suppressHydrationWarning
      >
        <AuthProvider>
          <NotificationProvider>
            <ThemeProvider>
              <CartProvider>
                <LayoutWrapper>
                  {children}
                </LayoutWrapper>
              </CartProvider>
            </ThemeProvider>
          </NotificationProvider>
        </AuthProvider>

        {/* Extension Bridge Script */}
        <Script
          src="/extension-bridge.js"
          strategy="afterInteractive"
        />

        {/* Auth Token Script for Middleware */}
        <Script id="auth-token-script" strategy="beforeInteractive">
          {`
            (function() {
              try {
                // Check for tokens in localStorage or sessionStorage
                const accessToken = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');

                // If token exists, set it as a custom header for middleware to detect
                if (accessToken) {
                  // For fetch requests
                  const originalFetch = window.fetch;
                  window.fetch = function(url, options = {}) {
                    if (!options.headers) {
                      options.headers = {};
                    }

                    // Add the auth token header
                    options.headers['x-auth-token'] = accessToken;

                    return originalFetch(url, options);
                  };

                  console.log('Auth token script: Token found and fetch interceptor set up');
                } else {
                  console.log('Auth token script: No token found in storage');
                }
              } catch (e) {
                console.error('Auth token script error:', e);
              }
            })();
          `}
        </Script>
      </body>
    </html>
  );
}
