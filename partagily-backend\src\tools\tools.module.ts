import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ToolsService } from './tools.service';
import { ToolsController } from './tools.controller';
import { CookieService } from './services/cookie.service';
import { <PERSON>ieController } from './controllers/cookie.controller';
import { AccessController } from './controllers/access.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [PrismaModule, CommonModule],
  controllers: [ToolsController, CookieController, AccessController],
  providers: [ToolsService, CookieService],
  exports: [ToolsService, CookieService],
})
export class ToolsModule {}
