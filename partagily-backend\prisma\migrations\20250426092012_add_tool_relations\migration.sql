/*
  Warnings:

  - The values [DESIG<PERSON>,PRODUCTIVITY,WRITING,AI,DEVELOPMENT,MARKETING,EDUCATION,ENTERTAINMENT,OTHER] on the enum `ToolCategory` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ToolCategory_new" AS ENUM ('design', 'productivity', 'writing', 'ai', 'development', 'marketing', 'education', 'entertainment', 'streaming', 'other');
ALTER TABLE "tools" ALTER COLUMN "category" TYPE "ToolCategory_new" USING ("category"::text::"ToolCategory_new");
ALTER TYPE "ToolCategory" RENAME TO "ToolCategory_old";
ALTER TYPE "ToolCategory_new" RENAME TO "ToolCategory";
DROP TYPE "ToolCategory_old";
COMMIT;

-- AlterTable
ALTER TABLE "plans" ADD COLUMN     "duration" TEXT,
ADD COLUMN     "isPopular" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "subscriptions" ADD COLUMN     "expiresAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "toolId" TEXT;

-- AlterTable
ALTER TABLE "tools" ADD COLUMN     "logoUrl" TEXT,
ADD COLUMN     "websiteUrl" TEXT;

-- CreateTable
CREATE TABLE "_ToolPlans" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_ToolPlans_AB_unique" ON "_ToolPlans"("A", "B");

-- CreateIndex
CREATE INDEX "_ToolPlans_B_index" ON "_ToolPlans"("B");

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tools"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ToolPlans" ADD CONSTRAINT "_ToolPlans_A_fkey" FOREIGN KEY ("A") REFERENCES "plans"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ToolPlans" ADD CONSTRAINT "_ToolPlans_B_fkey" FOREIGN KEY ("B") REFERENCES "tools"("id") ON DELETE CASCADE ON UPDATE CASCADE;
