'use client';

import { motion } from 'framer-motion';
import { Download, UserPlus, CreditCard, Package, Zap, Key, Globe, HeadphonesIcon } from 'lucide-react';
import Link from 'next/link';
import CurvedShape from '../ui/CurvedShape';

const ModernHowItWorksSection = () => {
  const steps = [
    {
      title: "Payer avec Konnect",
      description: "Utilisez Konnect, E-Dinar ou La Poste - aucune carte internationale nécessaire !",
      icon: <CreditCard size={28} className="text-[#e94a9c]" />,
      color: "bg-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.2)]"
    },
    {
      title: "Créer un compte",
      description: "Inscrivez-vous et choisissez votre plan d'abonnement parfait.",
      icon: <UserPlus size={28} className="text-[#4f8eff]" />,
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]"
    },
    {
      title: "Accès instantané",
      description: "Accédez instantanément aux outils premium mondiaux et services.",
      icon: <Zap size={28} className="text-[#FFAD00]" />,
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]"
    }
  ];

  const features = [
    {
      title: "120+ Outils Premium",
      description: "Du design à la productivité aux outils IA.",
      icon: <Globe size={24} className="text-[#4f8eff]" />,
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]"
    },
    {
      title: "Accès partagé aux comptes pros",
      description: "Accédez aux outils premium sans abonnements individuels.",
      icon: <Key size={24} className="text-[#e94a9c]" />,
      color: "bg-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.2)]"
    },
    {
      title: "Accès instantané",
      description: "Pas d'attente - accès immédiat après paiement.",
      icon: <Zap size={24} className="text-[#FFAD00]" />,
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]"
    },
    {
      title: "Support Tunisien",
      description: "Équipe de support locale qui comprend vos besoins.",
      icon: <HeadphonesIcon size={24} className="text-[#30d158]" />,
      color: "bg-[rgba(48,209,88,0.1)]",
      borderColor: "border-[rgba(48,209,88,0.2)]"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-24 relative" id="how-it-works">
      {/* Blob decorations */}
      <div className="blob-decoration blob-yellow"></div>
      <div className="blob-decoration blob-pink" style={{ left: 'auto', right: '-50px', top: '50%' }}></div>

      {/* Curved shapes */}
      <CurvedShape position="top" color="#e94a9c" />
      <CurvedShape position="bottom" color="#FFAD00" />

      <div className="container mx-auto px-4">
        {/* How It Works Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Comment ça <span className="text-[#FFAD00]">marche</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Commencer avec Partagily est simple et direct.
            Suivez ces étapes pour accéder aux outils premium dès aujourd'hui !
          </p>
        </motion.div>

        {/* Steps with connecting lines */}
        <div className="relative mb-24">
          {/* Connecting line */}
          <div className="hidden md:block absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-[#FFAD00]/20 via-[#e94a9c]/20 to-[#30d158]/20 transform -translate-y-1/2 z-0"></div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10"
          >
            {steps.map((step, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className={`glass-card p-8 flex flex-col items-center text-center hover-card relative ${index % 2 === 0 ? 'md:-mt-8' : 'md:mt-8'}`}
              >
                <div className={`w-16 h-16 rounded-full ${step.color} flex items-center justify-center mb-6`}>
                  {step.icon}
                </div>
                <h3 className="text-xl font-bold mb-3">{step.title}</h3>
                <p className="text-gray-300">{step.description}</p>
                <div className="mt-6 bg-[#FFAD00] text-gray-900 w-8 h-8 rounded-full flex items-center justify-center font-bold shadow-md">
                  {index + 1}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Key Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Fonctionnalités <span className="text-[#FFAD00]">Clés</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Partagily est rempli de fonctionnalités conçues spécifiquement pour les utilisateurs tunisiens.
          </p>
        </motion.div>

        {/* Features with horizontal scroll on mobile */}
        <div className="md:grid md:grid-cols-2 lg:grid-cols-4 gap-8 horizontal-scroll mb-16">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className={`gradient-card p-6 flex flex-col items-center text-center hover-card`}
            >
              <div className={`w-14 h-14 rounded-full ${feature.color} flex items-center justify-center mb-4`}>
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
              <p className="text-gray-300">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="text-center"
        >
          <Link
            href="#payment-methods"
            className="btn btn-primary btn-lg hover-glow inline-flex items-center gap-2"
          >
            Voir les méthodes de paiement
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernHowItWorksSection;
