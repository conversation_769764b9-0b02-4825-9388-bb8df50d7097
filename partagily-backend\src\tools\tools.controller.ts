import { <PERSON>, Get, Param, Query, UseGuards, Request, NotFoundException, ForbiddenException } from '@nestjs/common';
import { ToolsService } from './tools.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { ToolCookiesResponseDto } from './dto/tool-cookies.dto';

@ApiTags('tools')
@Controller('tools')
export class ToolsController {
  constructor(private readonly toolsService: ToolsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all tools' })
  @ApiResponse({ status: 200, description: 'Return all tools' })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({ name: 'plan', required: false })
  findAll(@Query('category') category?: string, @Query('plan') plan?: string) {
    if (category) {
      return this.toolsService.findByCategory(category);
    }
    if (plan) {
      return this.toolsService.findByPlan(plan);
    }
    return this.toolsService.findAll();
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool found' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  findOne(@Param('id') id: string) {
    return this.toolsService.findOne(id);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id/cookies')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get cookies for a tool' })
  @ApiResponse({
    status: 200,
    description: 'Cookies for the tool',
    type: ToolCookiesResponseDto
  })
  @ApiResponse({ status: 403, description: 'User does not have access to this tool' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async getToolCookies(@Param('id') id: string, @Request() req) {
    // Get the user ID from the request
    const userId = req.user.id;

    // Check if the tool exists
    const tool = await this.toolsService.findOne(id);
    if (!tool) {
      throw new NotFoundException('Tool not found');
    }

    // Check if the user has access to this tool
    const hasAccess = await this.toolsService.userHasAccessToTool(userId, id);
    if (!hasAccess) {
      throw new ForbiddenException('You do not have access to this tool');
    }

    // Get the cookies for the tool
    return this.toolsService.getToolCookies(id);
  }
}
