/**
 * This script is injected into the page to apply anti-debugging measures
 * It runs in the context of the page, not the extension
 */

(function() {
  // Obfuscation key for cookie encryption
  const OBFUSCATION_KEY = 'p4rt4g1ly-s3cur3-k3y';
  
  /**
   * Encrypts cookie data using a simple XOR cipher
   * @param {string} data - The data to encrypt
   * @returns {string} - The encrypted data
   */
  function encryptData(data) {
    if (!data) return '';
    
    let result = '';
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
      result += String.fromCharCode(charCode);
    }
    
    // Convert to base64 to make it harder to read
    return btoa(result);
  }
  
  /**
   * Decrypts cookie data
   * @param {string} encryptedData - The encrypted data
   * @returns {string} - The decrypted data
   */
  function decryptData(encryptedData) {
    if (!encryptedData) return '';
    
    try {
      // Decode from base64
      const data = atob(encryptedData);
      
      let result = '';
      for (let i = 0; i < data.length; i++) {
        const charCode = data.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
        result += String.fromCharCode(charCode);
      }
      
      return result;
    } catch (error) {
      console.error('Error decrypting data:', error);
      return '';
    }
  }
  
  /**
   * Detects if DevTools is open
   * @returns {boolean} - True if DevTools is likely open
   */
  function detectDevTools() {
    // Method 1: Check window size difference
    const widthThreshold = window.outerWidth - window.innerWidth > 160;
    const heightThreshold = window.outerHeight - window.innerHeight > 160;
    
    // Method 2: Check if debugger statements are being caught
    let debuggerTriggered = false;
    try {
      function debuggerCheck() {
        debuggerTriggered = true;
        return true;
      }
      
      // Override toString to hide the function's purpose
      debuggerCheck.toString = function() {
        return 'function debuggerCheck() { [native code] }';
      };
      
      // Set a breakpoint
      debugger;
      
      // If debugger is active, this won't be called immediately
      const startTime = Date.now();
      debuggerCheck();
      const debuggerTime = Date.now() - startTime > 100;
    } catch (e) {
      // Ignore errors
    }
    
    // Method 3: Check for console timing differences
    let consoleTimingDifference = false;
    try {
      const startTime = Date.now();
      console.log('');
      console.clear();
      consoleTimingDifference = Date.now() - startTime > 100;
    } catch (e) {
      // Ignore errors
    }
    
    return widthThreshold || heightThreshold || debuggerTriggered || consoleTimingDifference;
  }
  
  /**
   * Takes action when DevTools is detected
   */
  function handleDevToolsOpen() {
    // Log the attempt
    try {
      // In a real implementation, you might want to report this to your server
      console.warn('Inspection attempt detected');
    } catch (e) {
      // Ignore errors
    }
    
    // Redirect or reload the page
    try {
      // Uncomment to enable redirection when DevTools is opened
      // window.location.href = 'about:blank';
    } catch (e) {
      // Ignore errors
    }
    
    // Clear sensitive data from storage
    try {
      localStorage.removeItem('accessToken');
      sessionStorage.removeItem('accessToken');
    } catch (e) {
      // Ignore errors
    }
  }
  
  /**
   * Disables right-click context menu
   */
  function disableContextMenu() {
    document.addEventListener('contextmenu', function(e) {
      e.preventDefault();
      return false;
    }, { capture: true });
  }
  
  /**
   * Disables keyboard shortcuts for developer tools
   */
  function disableDevToolsShortcuts() {
    document.addEventListener('keydown', function(e) {
      // Prevent F12
      if (e.key === 'F12' || e.keyCode === 123) {
        e.preventDefault();
        return false;
      }
      
      // Prevent Ctrl+Shift+I / Cmd+Option+I
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.keyCode === 73)) {
        e.preventDefault();
        return false;
      }
      
      // Prevent Ctrl+Shift+J / Cmd+Option+J
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'J' || e.key === 'j' || e.keyCode === 74)) {
        e.preventDefault();
        return false;
      }
      
      // Prevent Ctrl+Shift+C / Cmd+Option+C
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'C' || e.key === 'c' || e.keyCode === 67)) {
        e.preventDefault();
        return false;
      }
      
      // Prevent Ctrl+U / Cmd+U (view source)
      if ((e.ctrlKey || e.metaKey) && (e.key === 'U' || e.key === 'u' || e.keyCode === 85)) {
        e.preventDefault();
        return false;
      }
    }, { capture: true });
  }
  
  /**
   * Overrides the document.cookie property to encrypt/decrypt cookies
   */
  function secureCookies() {
    // Store the original cookie descriptor
    const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie');
    
    // Override the cookie property
    Object.defineProperty(document, 'cookie', {
      get: function() {
        // Return the original cookies (could be decrypted here if needed)
        return originalCookieDescriptor.get.call(this);
      },
      set: function(value) {
        // For sensitive cookies, encrypt them
        if (value.includes('NetflixId') || value.includes('SecureNetflixId') || 
            value.includes('accessToken') || value.includes('refreshToken')) {
          // Extract name and value
          const parts = value.split(';');
          const nameValue = parts[0].split('=');
          
          if (nameValue.length >= 2) {
            const name = nameValue[0].trim();
            const val = nameValue.slice(1).join('=');
            
            // Encrypt the value
            const encryptedValue = encryptData(val);
            
            // Reconstruct the cookie string
            const newValue = `${name}=${encryptedValue}${parts.slice(1).join(';')}`;
            
            // Set the cookie with the encrypted value
            return originalCookieDescriptor.set.call(this, newValue);
          }
        }
        
        // For non-sensitive cookies, use the original setter
        return originalCookieDescriptor.set.call(this, value);
      },
      enumerable: originalCookieDescriptor.enumerable,
      configurable: originalCookieDescriptor.configurable
    });
  }
  
  /**
   * Initializes all anti-debugging measures
   */
  function initAntiDebugging() {
    // Disable context menu
    disableContextMenu();
    
    // Disable keyboard shortcuts
    disableDevToolsShortcuts();
    
    // Secure cookies
    secureCookies();
    
    // Periodically check for DevTools
    setInterval(() => {
      if (detectDevTools()) {
        handleDevToolsOpen();
      }
    }, 1000);
    
    // Override console methods to detect usage
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };
    
    // Replace console methods with monitored versions
    console.log = function() {
      // You can add monitoring here
      return originalConsole.log.apply(console, arguments);
    };
    
    console.warn = function() {
      // You can add monitoring here
      return originalConsole.warn.apply(console, arguments);
    };
    
    console.error = function() {
      // You can add monitoring here
      return originalConsole.error.apply(console, arguments);
    };
    
    console.info = function() {
      // You can add monitoring here
      return originalConsole.info.apply(console, arguments);
    };
    
    console.debug = function() {
      // You can add monitoring here
      return originalConsole.debug.apply(console, arguments);
    };
    
    // Prevent debugging
    setInterval(() => {
      debugger;
    }, 100);
  }
  
  // Initialize anti-debugging measures
  initAntiDebugging();
  
  // Log that the script is running (this will be hidden in production)
  console.log('Anti-debugging measures activated');
})();
