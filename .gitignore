# Dependencies
node_modules/
**/node_modules/
partagily-backend/node_modules/
partagily-frontend/node_modules/

.pnp/
.pnp.js
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Build outputs
.next/
out/
build/
dist/
coverage/

# Environment files
.env
.env.*
!.env.example
.vercel

# IDE and editor files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.sublime-project
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Logs and debugging
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Extension specific
_metadata/
generated_indexed_rulesets/

# Testing
/coverage
.nyc_output

# Prisma
prisma/.env

# Misc
*.pem
.cache
*.bak
*.tmp
*.temp
.turbo