import axios from "axios";
import authService from "./authService";

// Use the backend API URL directly
const API_URL = process.env.NEXT_PUBLIC_API_URL; // Point directly to the backend API

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Include cookies in requests
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for non-GET requests
    if (config.method !== "get") {
      config.headers["X-CSRF-Token"] = authService.getCsrfToken();
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // If there's no config, just reject the promise
    if (!error.config) {
      return Promise.reject(new Error("Network error occurred"));
    }

    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${authService.getAccessToken()}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear auth but don't redirect
        // Let the component handle the redirect
        return Promise.reject(
          new Error("Authentication failed. Please log in again.")
        );
      }
    }

    // For other errors, create a clean error object with a consistent message
    let errorMessage = "An error occurred";

    if (error.response?.status === 401) {
      errorMessage = "Authentication failed. Please log in again.";
    } else if (error.response?.status === 403) {
      errorMessage = "You do not have permission to perform this action.";
    } else if (error.response?.status === 400) {
      // For validation errors
      errorMessage =
        error.response.data?.message ||
        "Invalid request. Please check your input.";
    } else if (error.response?.status === 429) {
      errorMessage = "Too many requests. Please try again later.";
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

const toolService = {
  // Get all tools
  async getAllTools() {
    try {
      console.log("Fetching tools from API:", `${API_URL}/tools`);

      // Make a direct call to the backend API
      const response = await api.get("/tools", {
        timeout: 10000, // Increase timeout to 10 seconds
        // Add cache busting parameter to prevent caching
        params: { _t: new Date().getTime() },
      });

      console.log("API response received:", response.data);

      // Check if the response has the expected structure
      if (
        response.data &&
        (response.data.tools || Array.isArray(response.data))
      ) {
        // If the response has a tools property, return it directly
        if (response.data.tools) {
          console.log(
            "Returning tools from API (tools property):",
            response.data.tools.length,
            "tools found"
          );
          return { tools: response.data };
        }

        // If the response is an array, wrap it in an object with a tools property
        if (Array.isArray(response.data)) {
          console.log(
            "Returning tools from API (array):",
            response.data.length,
            "tools found"
          );
          return { tools: response.data };
        }
      }

      // If we get here, the response doesn't have the expected structure
      console.warn("API returned unexpected format:", response.data);
      throw new Error("Unexpected response format");
    } catch (error) {
      console.error("Error fetching tools:", error);
      throw new Error(
        "Failed to fetch tools from the server. Please try again later."
      );
    }
  },

  // Get tool by ID
  async getToolById(id: string) {
    try {
      const response = await api.get(`/tools/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tool ${id}:`, error);
      throw error;
    }
  },

  // Get tools by category
  async getToolsByCategory(category: string) {
    try {
      const response = await api.get(`/tools?category=${category}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tools for category ${category}:`, error);
      throw error;
    }
  },

  // Get tools by plan
  async getToolsByPlan(plan: string) {
    try {
      const response = await api.get(`/tools?plan=${plan}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching tools for plan ${plan}:`, error);
      throw error;
    }
  },

  // Get cookies for a tool
  async getToolCookies(id: string) {
    try {
      const response = await api.get(`/tools/${id}/cookies`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching cookies for tool ${id}:`, error);
      throw error;
    }
  },

  // Access a tool (open in new tab and inject cookies)
  async accessTool(id: string, url: string) {
    try {
      // Check if the extension is installed
      if (!window.partagilyExtension) {
        throw new Error(
          "Partagily extension is not installed. Please install it to access this tool."
        );
      }

      // Call the extension to get access
      window.partagilyExtension.getAccess(id, url);

      return { success: true };
    } catch (error) {
      console.error(`Error accessing tool ${id}:`, error);
      throw error;
    }
  },

  // Get all plans
  async getAllPlans() {
    try {
      console.log("Fetching plans from API");

      // Try both endpoints for plans
      try {
        // First try the subscriptions/plans endpoint
        console.log("Trying /subscriptions/plans endpoint");
        const response = await api.get("/subscriptions/plans", {
          timeout: 5000,
        });
        console.log("API response from /subscriptions/plans:", response.data);

        // Check if the response has the expected structure
        if (response.data) {
          if (Array.isArray(response.data)) {
            console.log(
              "Returning plans from API (array):",
              response.data.length,
              "plans found"
            );
            return { plans: response.data };
          } else if (
            response.data.plans &&
            Array.isArray(response.data.plans)
          ) {
            console.log(
              "Returning plans from API (plans property):",
              response.data.plans.length,
              "plans found"
            );
            return response.data;
          }
        }

        // If we get here, try the /plans endpoint
        console.warn("Unexpected response format from /subscriptions/plans");
        throw new Error("Unexpected response format from /subscriptions/plans");
      } catch (firstError) {
        console.warn(
          "First API endpoint failed, trying alternative:",
          firstError.message
        );

        try {
          // Try the /plans endpoint as a fallback
          console.log("Trying /plans endpoint");
          const response = await api.get("/plans", { timeout: 5000 });
          console.log("API response from /plans:", response.data);

          // Check if the response has the expected structure
          if (response.data) {
            if (Array.isArray(response.data)) {
              console.log(
                "Returning plans from API (array):",
                response.data.length,
                "plans found"
              );
              return { plans: response.data };
            } else if (
              response.data.plans &&
              Array.isArray(response.data.plans)
            ) {
              console.log(
                "Returning plans from API (plans property):",
                response.data.plans.length,
                "plans found"
              );
              return response.data;
            }
          }

          // If we get here, neither endpoint worked as expected
          console.warn("Unexpected response format from /plans");
          throw new Error(
            "Unexpected response format from both plan endpoints"
          );
        } catch (secondError) {
          console.error(
            "Second API endpoint also failed:",
            secondError.message
          );
          throw new Error("Both API endpoints failed: " + secondError.message);
        }
      }
    } catch (error) {
      console.error("Error fetching plans:", error);
      throw new Error(
        "Failed to fetch subscription plans from the server. Please try again later."
      );
    }
  },

  // Get plan by name
  async getPlanByName(name: string) {
    try {
      const response = await api.get(`/subscriptions/plans/${name}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching plan ${name}:`, error);
      throw error;
    }
  },
};

export default toolService;
