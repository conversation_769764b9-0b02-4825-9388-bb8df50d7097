import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsObject, IsNotEmpty } from 'class-validator';

export class CreateCookieEventDto {
  @ApiProperty({
    description: 'The type of cookie event',
    example: 'COOKIE_INJECTION',
    enum: ['COOKIE_INJECTION', 'COOKIE_ROTATION', 'COOKIE_CLEAR'],
  })
  @IsString()
  @IsNotEmpty()
  eventType: string;

  @ApiProperty({
    description: 'The user ID associated with the event',
    example: '1',
    required: false,
  })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'Additional details about the event',
    example: {
      toolId: '123',
      domain: 'example.com',
      status: 'success',
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  details?: Record<string, any>;
}
