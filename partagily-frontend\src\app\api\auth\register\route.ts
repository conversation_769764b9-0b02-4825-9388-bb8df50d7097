import { NextRequest, NextResponse } from "next/server";

// Direct API URL to the backend
const API_URL = process.env.NEXT_PUBLIC_API_URL;

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Log the registration attempt
    console.log("Registration attempt with email:", body.email);

    // Use the correct endpoint URL - IMPORTANT: use /auth/register, not /api/auth/register
    const endpointUrl = `${API_URL}/auth/register`;
    console.log("Endpoint URL:", endpointUrl);
    console.log("Forwarding registration request to backend:", endpointUrl);

    // Forward the request to the backend using direct fetch
    const response = await fetch(endpointUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
      // Add credentials to ensure cookies are sent
      credentials: "include",
    });

    // Check if the response is ok
    if (!response.ok) {
      console.error(
        "Backend registration failed with status:",
        response.status
      );

      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: "Unknown error occurred" };
      }

      console.error("Error details:", errorData);

      return NextResponse.json(
        { error: errorData.message || "Registration failed" },
        { status: response.status }
      );
    }

    // Get the response data
    const data = await response.json();

    console.log("Registration successful! User ID:", data.user?.id);

    // Return the successful response
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("Error during registration:", error);

    // Provide more detailed error information
    let errorMessage = "Failed to register user";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = `Registration error: ${error.message}`;
      console.error("Error stack:", error.stack);
    }

    if (error instanceof TypeError && error.message.includes("fetch")) {
      errorMessage =
        "Unable to connect to the backend server. Please check if the server is running.";
      statusCode = 503; // Service Unavailable
    }

    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
}
