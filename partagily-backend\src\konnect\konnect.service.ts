import { HttpException, Injectable } from "@nestjs/common";
import { CreateKonnectDto } from "./dto/create-konnect.dto";
import { UpdateKonnectDto } from "./dto/update-konnect.dto";
import { PrismaClient } from "@prisma/client";
import { HttpService } from "@nestjs/axios";
import { plainToInstance } from "class-transformer";
import { GetKonnectDto } from "./dto/get-konnect-dto";

@Injectable()
export class KonnectService {
  private prisma: PrismaClient;

  constructor(private readonly httpService: HttpService) {
    this.prisma = new PrismaClient();
  }
  async create(createKonnectDto: CreateKonnectDto): Promise<GetKonnectDto> {
    try {
      const response = await fetch(process.env.KONNECT_API_URL, {
        method: "POST",
        headers: {
          "x-api-key": process.env.X_API_KEY,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(createKonnectDto),
      });
      if (!response.ok) {
        console.log("🚀 ~ KonnectService ~ create ~ response:", response);
        throw new HttpException(
          response.statusText ?? "Failed to get payment URL",
          response.status ?? 500
        );
      }
      const res = await response.json();
      return plainToInstance(GetKonnectDto, res, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log("🚀 ~ KonnectService ~ create ~ error:", error);
      throw new HttpException(
        error.message ?? "Error in payment process",
        error.status ?? 400
      );
    }
  }

  findAll() {
    return `This action returns all konnect`;
  }

  findOne(id: number) {
    return `This action returns a #${id} konnect`;
  }

  update(id: number, updateKonnectDto: UpdateKonnectDto) {
    return `This action updates a #${id} konnect`;
  }

  remove(id: number) {
    return `This action removes a #${id} konnect`;
  }
}
