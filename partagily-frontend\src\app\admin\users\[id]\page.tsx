'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, Trash2, User, Mail, Key, Shield, ToggleLeft, Package, Calendar } from 'lucide-react';
import adminService from '@/services/adminService';
import mockAdminService from '@/services/mockAdminService';

export default function EditUserPage({ params }: { params: { id: string } }) {
  const [user, setUser] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: '',
    isActive: true,
    assignTools: false,
    selectedTools: [] as { toolId: string, planId: string }[],
  });
  const [userSubscriptions, setUserSubscriptions] = useState<any[]>([]);
  const [tools, setTools] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoadingTools, setIsLoadingTools] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const router = useRouter();

  // Fetch user data
  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        console.log('Fetching user with ID:', params.id);

        // Try to use the real backend API first
        try {
          console.log('Attempting to use real backend API for user data');
          const userData = await adminService.getUserById(params.id);
          console.log('User data retrieved from real backend:', userData);

          setUser(userData);
          setFormData({
            name: userData.name,
            email: userData.email,
            password: '',
            role: userData.role,
            isActive: userData.isActive,
            assignTools: false,
            selectedTools: [],
          });

          // Fetch user subscriptions from real backend
          try {
            const subscriptionsFilter = { userId: params.id };
            const subscriptionsResponse = await adminService.getSubscriptions(subscriptionsFilter);

            // Check if the backend is down
            if (subscriptionsResponse._backendDown) {
              console.log('Backend is down for subscriptions, falling back to mock data');
              const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
              setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
            } else {
              setUserSubscriptions(subscriptionsResponse.subscriptions || []);
            }

            console.log('User subscriptions:', subscriptionsResponse.subscriptions);
          } catch (subError) {
            console.error('Error fetching user subscriptions from real backend:', subError);

            // Fall back to mock data for subscriptions
            try {
              const subscriptionsFilter = { userId: params.id };
              const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
              setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
              console.log('User subscriptions from mock data:', mockSubscriptionsResponse.subscriptions);
            } catch (mockSubError) {
              console.error('Error fetching user subscriptions from mock data:', mockSubError);
            }
          }
        } catch (backendError: any) {
          console.error('Error fetching user from real backend:', backendError);

          // Fall back to mock data
          console.log('Falling back to mock service for user data');
          const mockUserData = await mockAdminService.getUserById(params.id);
          console.log('User data retrieved from mock service:', mockUserData);

          setUser(mockUserData);
          setFormData({
            name: mockUserData.name,
            email: mockUserData.email,
            password: '',
            role: mockUserData.role,
            isActive: mockUserData.isActive,
            assignTools: false,
            selectedTools: [],
          });

          // Fetch user subscriptions from mock data
          try {
            const subscriptionsFilter = { userId: params.id };
            const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
            setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
            console.log('User subscriptions from mock data:', mockSubscriptionsResponse.subscriptions);
          } catch (mockSubError) {
            console.error('Error fetching user subscriptions from mock data:', mockSubError);
          }

          setError('Backend connection error: ' + backendError.message);
        }
      } catch (err: any) {
        console.error('Error fetching user:', err);
        setError(err.message || 'Failed to load user');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [params.id]);

  // Fetch tools for the dropdown
  useEffect(() => {
    const fetchTools = async () => {
      try {
        setIsLoadingTools(true);

        // Try to use the real backend API first
        try {
          console.log('Attempting to use real backend API for tools data');
          const response = await adminService.getTools();

          // Check if the backend is down
          if (response._backendDown) {
            console.log('Backend is down for tools, falling back to mock data');
            const mockResponse = await mockAdminService.getTools();
            setTools(mockResponse.tools);
          } else {
            setTools(response.tools);
          }

          console.log('Tools fetched:', response.tools || []);
        } catch (backendError: any) {
          console.error('Error fetching tools from real backend:', backendError);

          // Fall back to mock data
          console.log('Falling back to mock service for tools data');
          const mockResponse = await mockAdminService.getTools();
          setTools(mockResponse.tools);
          console.log('Tools fetched from mock data:', mockResponse.tools);
        }
      } catch (err: any) {
        console.error('Failed to fetch tools:', err);
      } finally {
        setIsLoadingTools(false);
      }
    };

    fetchTools();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle adding a tool to the user
  const handleAddTool = () => {
    setFormData({
      ...formData,
      selectedTools: [
        ...formData.selectedTools,
        { toolId: '', planId: '' }
      ]
    });
  };

  // Handle removing a tool from the user
  const handleRemoveTool = (index: number) => {
    const updatedTools = [...formData.selectedTools];
    updatedTools.splice(index, 1);
    setFormData({
      ...formData,
      selectedTools: updatedTools
    });
  };

  // Handle changing a tool or plan selection
  const handleToolChange = (index: number, field: 'toolId' | 'planId', value: string) => {
    const updatedTools = [...formData.selectedTools];
    updatedTools[index][field] = value;

    // If changing the tool, reset the plan
    if (field === 'toolId') {
      updatedTools[index].planId = '';
    }

    setFormData({
      ...formData,
      selectedTools: updatedTools
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      console.log('Updating user with ID:', params.id);
      console.log('Update data:', formData);

      // Validate tool selections if assignTools is enabled
      if (formData.assignTools && formData.selectedTools.length > 0) {
        const invalidSelections = formData.selectedTools.some(
          tool => !tool.toolId || !tool.planId
        );

        if (invalidSelections) {
          setError('Please select both tool and plan for all tool assignments');
          setIsSaving(false);
          return;
        }
      }

      // Only include password if it's not empty
      const { assignTools, selectedTools, ...userData } = formData;
      const updateData = {
        ...userData,
        ...(userData.password ? {} : { password: undefined }), // Remove empty password
      };

      // Try to use the real backend API first
      try {
        console.log('Attempting to update user with real backend API');
        await adminService.updateUser(params.id, updateData);

        // If tool assignments are enabled and we have tools selected
        if (assignTools && selectedTools.length > 0) {
          try {
            // For each selected tool, create a subscription
            for (const toolSelection of selectedTools) {
              // Check if this subscription already exists
              const existingSubscription = userSubscriptions.find(
                sub => sub.toolId === toolSelection.toolId && sub.planId === toolSelection.planId
              );

              if (existingSubscription) {
                console.log('Subscription already exists:', existingSubscription);
                continue; // Skip creating this subscription
              }

              // Get the tool to find its details
              const tool = tools.find(t => t.id === toolSelection.toolId);
              if (!tool) continue;

              // Get the plan to find its details
              const plan = tool.plans.find((p: any) => p.id === toolSelection.planId);
              if (!plan) continue;

              // Calculate end date (1 month from now)
              const startDate = new Date();
              const endDate = new Date();
              endDate.setMonth(endDate.getMonth() + 1);

              // Create subscription data
              const subscriptionData = {
                userId: params.id,
                toolId: toolSelection.toolId,
                planId: toolSelection.planId,
                status: 'active',
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                notes: 'Created during user update'
              };

              // Create the subscription with real backend
              try {
                console.log('Attempting to create subscription with real backend API');
                const newSubscription = await adminService.createSubscription(subscriptionData);
                console.log('Created new subscription with real backend:', newSubscription);
              } catch (subError: any) {
                console.error('Error creating subscription with real backend:', subError);

                // Fall back to mock service
                console.log('Falling back to mock service for subscription creation');
                const mockSubscription = await mockAdminService.createSubscription(subscriptionData);
                console.log('Created new subscription with mock service:', mockSubscription);
              }
            }

            // Refresh subscriptions from real backend
            try {
              const subscriptionsFilter = { userId: params.id };
              const subscriptionsResponse = await adminService.getSubscriptions(subscriptionsFilter);

              // Check if the backend is down
              if (subscriptionsResponse._backendDown) {
                console.log('Backend is down for subscriptions refresh, falling back to mock data');
                const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
                setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
              } else {
                setUserSubscriptions(subscriptionsResponse.subscriptions || []);
              }
            } catch (refreshError: any) {
              console.error('Error refreshing subscriptions from real backend:', refreshError);

              // Fall back to mock data
              const subscriptionsFilter = { userId: params.id };
              const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
              setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
            }
          } catch (subscriptionError: any) {
            console.error('Error creating subscriptions:', subscriptionError);
            // We don't want to block user update if subscription creation fails
            // Just log the error and continue
          }
        }

        setSuccessMessage('User updated successfully' +
          (assignTools && selectedTools.length > 0 ? ' with tool assignments' : ''));

        // Refresh user data from real backend
        try {
          console.log('Refreshing user data from real backend');
          const updatedUserData = await adminService.getUserById(params.id);
          setUser(updatedUserData);
          console.log('User updated successfully:', updatedUserData);
        } catch (refreshError: any) {
          console.error('Error refreshing user data from real backend:', refreshError);

          // Fall back to mock data
          const mockUserData = await mockAdminService.getUserById(params.id);
          setUser(mockUserData);
        }
      } catch (backendError: any) {
        console.error('Error updating user with real backend:', backendError);

        // Fall back to mock service
        console.log('Falling back to mock service for user update');

        try {
          // Update user with mock service
          await mockAdminService.updateUser(params.id, updateData);

          // If tool assignments are enabled and we have tools selected
          if (assignTools && selectedTools.length > 0) {
            try {
              // For each selected tool, create a subscription
              for (const toolSelection of selectedTools) {
                // Check if this subscription already exists
                const existingSubscription = userSubscriptions.find(
                  sub => sub.toolId === toolSelection.toolId && sub.planId === toolSelection.planId
                );

                if (existingSubscription) {
                  console.log('Subscription already exists:', existingSubscription);
                  continue; // Skip creating this subscription
                }

                // Get the tool to find its details
                const tool = tools.find(t => t.id === toolSelection.toolId);
                if (!tool) continue;

                // Get the plan to find its details
                const plan = tool.plans.find((p: any) => p.id === toolSelection.planId);
                if (!plan) continue;

                // Calculate end date (1 month from now)
                const startDate = new Date();
                const endDate = new Date();
                endDate.setMonth(endDate.getMonth() + 1);

                // Create subscription data
                const subscriptionData = {
                  userId: params.id,
                  toolId: toolSelection.toolId,
                  planId: toolSelection.planId,
                  status: 'active',
                  startDate: startDate.toISOString(),
                  endDate: endDate.toISOString(),
                  notes: 'Created during user update'
                };

                // Create the subscription with mock service
                const mockSubscription = await mockAdminService.createSubscription(subscriptionData);
                console.log('Created new subscription with mock service:', mockSubscription);
              }

              // Refresh subscriptions from mock service
              const subscriptionsFilter = { userId: params.id };
              const mockSubscriptionsResponse = await mockAdminService.getSubscriptions(subscriptionsFilter);
              setUserSubscriptions(mockSubscriptionsResponse.subscriptions || []);
            } catch (subscriptionError: any) {
              console.error('Error creating subscriptions with mock service:', subscriptionError);
              // We don't want to block user update if subscription creation fails
              // Just log the error and continue
            }
          }

          setSuccessMessage('User updated successfully (using mock data)' +
            (assignTools && selectedTools.length > 0 ? ' with tool assignments' : ''));

          // Refresh user data from mock service
          const mockUserData = await mockAdminService.getUserById(params.id);
          setUser(mockUserData);
          console.log('User updated successfully with mock service:', mockUserData);

          // Show warning about backend connection
          setError('Backend connection error: ' + backendError.message + '. Changes were saved locally but may not be persisted to the server.');
        } catch (mockError: any) {
          console.error('Error updating user with mock service:', mockError);
          throw mockError; // Re-throw to be caught by the outer catch block
        }
      }
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.message || 'Failed to update user');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      setIsLoading(true);
      console.log('Deleting user with ID:', params.id);

      // Try to use the real backend API first
      try {
        console.log('Attempting to delete user with real backend API');
        await adminService.deleteUser(params.id);
        console.log('User deleted successfully with real backend');

        router.push('/admin/users');
      } catch (backendError: any) {
        console.error('Error deleting user with real backend:', backendError);

        // Fall back to mock service
        console.log('Falling back to mock service for user deletion');

        try {
          await mockAdminService.deleteUser(params.id);
          console.log('User deleted successfully with mock service');

          router.push('/admin/users');

          // Show warning about backend connection
          alert('Backend connection error. User was removed locally but may not be deleted from the server.');
        } catch (mockError: any) {
          console.error('Error deleting user with mock service:', mockError);
          throw mockError; // Re-throw to be caught by the outer catch block
        }
      }
    } catch (err: any) {
      console.error('Error deleting user:', err);
      setError(err.message || 'Failed to delete user');
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error && !user) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
        <p className="font-medium">Error</p>
        <p>{error}</p>
        <div className="mt-4">
          <Link
            href="/admin/users"
            className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-md transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Users
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/users"
            className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <h1 className="text-2xl font-bold">Edit User</h1>
        </div>
        <button
          onClick={handleDelete}
          className="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white font-medium rounded-md transition-colors"
        >
          <Trash2 className="w-5 h-5 mr-2" />
          Delete User
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          <p className="font-medium">Success</p>
          <p>{successMessage}</p>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password (leave blank to keep current)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Key className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Shield className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active Account
                  </label>
                </div>
              </div>

              {/* Tool Assignment Section */}
              <div className="md:col-span-2 mt-6 border-t pt-6">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="assignTools"
                    name="assignTools"
                    checked={formData.assignTools}
                    onChange={(e) => setFormData({ ...formData, assignTools: e.target.checked })}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="assignTools" className="ml-2 block text-sm font-medium text-gray-700">
                    Assign Additional Tools & Plans to User
                  </label>
                </div>

                {formData.assignTools && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-gray-900">Tool Assignments</h3>
                      <button
                        type="button"
                        onClick={handleAddTool}
                        className="inline-flex items-center px-3 py-1.5 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm font-medium rounded-md transition-colors"
                      >
                        <Package className="w-4 h-4 mr-1" />
                        Add Tool
                      </button>
                    </div>

                    {formData.selectedTools.length === 0 ? (
                      <div className="bg-gray-50 p-4 rounded-md text-center text-gray-500">
                        No additional tools assigned. Click "Add Tool" to assign more tools to this user.
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.selectedTools.map((toolSelection, index) => (
                          <div key={index} className="bg-gray-50 p-4 rounded-md">
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-medium text-gray-700">Tool Assignment #{index + 1}</h4>
                              <button
                                type="button"
                                onClick={() => handleRemoveTool(index)}
                                className="text-red-600 hover:text-red-800"
                              >
                                Remove
                              </button>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Select Tool
                                </label>
                                <select
                                  value={toolSelection.toolId}
                                  onChange={(e) => handleToolChange(index, 'toolId', e.target.value)}
                                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                                >
                                  <option value="">-- Select a Tool --</option>
                                  {tools.map((tool) => (
                                    <option key={tool.id} value={tool.id}>
                                      {tool.name}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Select Plan
                                </label>
                                <select
                                  value={toolSelection.planId}
                                  onChange={(e) => handleToolChange(index, 'planId', e.target.value)}
                                  disabled={!toolSelection.toolId}
                                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 disabled:bg-gray-100 disabled:text-gray-500"
                                >
                                  <option value="">-- Select a Plan --</option>
                                  {toolSelection.toolId && tools.find(t => t.id === toolSelection.toolId)?.plans.map((plan: any) => (
                                    <option key={plan.id} value={plan.id}>
                                      {plan.name} - ${plan.price} / {plan.duration}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-5 h-5 border-2 border-gray-900 border-t-transparent rounded-full mr-2"
                    />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-5 h-5 mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* User Subscriptions */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">User Subscriptions</h2>
          {userSubscriptions.length === 0 ? (
            <p className="text-gray-500">No subscriptions found for this user.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tool
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plan
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Start Date
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      End Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {userSubscriptions.map((subscription) => {
                    // Find the tool and plan names
                    const tool = tools.find(t => t.id === subscription.toolId);
                    const plan = tool?.plans.find((p: any) => p.id === subscription.planId);

                    return (
                      <tr key={subscription.id}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                          {tool?.name || subscription.toolId}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {plan?.name || subscription.planId}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                            ${subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                              subscription.status === 'expired' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'}`}>
                            {subscription.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {new Date(subscription.startDate).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          {new Date(subscription.endDate).toLocaleDateString()}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* User Payment History */}
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Payment History</h2>
          {/* This would be populated with actual payment data */}
          <p className="text-gray-500">No payment history found for this user.</p>
        </div>
      </div>
    </div>
  );
}
