'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';
import { Package, ShoppingBag, CreditCard } from 'lucide-react';
import paymentService from '@/services/paymentService';
import Image from 'next/image';

interface OrderItem {
  id: string;
  type: string;
  itemId: string;
  name: string;
  price: number;
}

interface Order {
  id: string;
  orderNumber: string;
  totalAmount: number;
  status: string;
  paymentMethod: string;
  createdAt: string;
  items: OrderItem[];

  // Legacy fields
  toolName?: string;
  planName?: string;
  amount?: number;
  currency?: string;
  receiptUrl?: string;
}

interface OrderHistoryProps {
  orders?: Order[];
  isLoading?: boolean;
}

const OrderHistory: React.FC<OrderHistoryProps> = ({
  orders: propOrders,
  isLoading: propIsLoading
}) => {
  const [orders, setOrders] = useState<Order[]>(propOrders || []);
  const [loading, setLoading] = useState<boolean>(propIsLoading !== undefined ? propIsLoading : true);
  const [error, setError] = useState<string | null>(null);
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);

  useEffect(() => {
    // If orders are provided as props, use them
    if (propOrders) {
      setOrders(propOrders);
      return;
    }

    // Otherwise fetch orders
    const fetchOrders = async () => {
      try {
        setLoading(true);
        const data = await paymentService.getUserOrders();
        setOrders(data);
      } catch (err: any) {
        setError(err.message || 'Failed to load orders');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [propOrders]);

  // Update loading state when prop changes
  useEffect(() => {
    if (propIsLoading !== undefined) {
      setLoading(propIsLoading);
    }
  }, [propIsLoading]);

  const toggleOrderDetails = (orderId: string) => {
    if (expandedOrder === orderId) {
      setExpandedOrder(null);
    } else {
      setExpandedOrder(orderId);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'REFUNDED':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
          <p className="font-medium">Error loading orders</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-md text-center">
        <div className="flex justify-center mb-4">
          <ShoppingBag size={64} className="text-gray-400 dark:text-gray-500" />
        </div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No orders yet</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          You haven't made any purchases yet. Browse our tools and plans to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {orders.map((order) => {
        // Calculate total amount from items if available, otherwise use legacy amount
        const totalAmount = order.items?.length > 0
          ? order.items.reduce((sum, item) => sum + item.price, 0)
          : (order.totalAmount || order.amount || 0);

        return (
          <motion.div
            key={order.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden"
          >
            <div
              className="p-6 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors"
              onClick={() => toggleOrderDetails(order.id)}
            >
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                      {order.orderNumber}
                    </h3>
                    <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {new Date(order.createdAt).toLocaleDateString()} ({formatDistanceToNow(new Date(order.createdAt), { addSuffix: true })})
                  </p>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="font-bold text-gray-900 dark:text-white">${totalAmount.toFixed(2)}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center justify-end">
                      <CreditCard size={12} className="mr-1" />
                      {order.paymentMethod || 'KONNECT'}
                    </p>
                  </div>
                  <div className="text-gray-400 dark:text-gray-500">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className={`transform transition-transform ${expandedOrder === order.id ? 'rotate-180' : ''}`}
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {expandedOrder === order.id && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-gray-200 dark:border-gray-700"
              >
                <div className="p-6">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Order Items</h4>
                  <div className="space-y-4">
                    {order.items && order.items.length > 0 ? (
                      // Modern order items display
                      order.items.map((item) => (
                        <div key={item.id} className="flex items-start justify-between">
                          <div className="flex items-start">
                            <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-slate-700 flex items-center justify-center mr-3">
                              <div className="text-lg">
                                {item.type === 'TOOL' ? '🔧' : '📦'}
                              </div>
                            </div>
                            <div>
                              <h5 className="font-medium text-gray-900 dark:text-white">{item.name}</h5>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {item.type === 'TOOL' ? 'Individual Tool' : 'Subscription Plan'}
                              </p>
                            </div>
                          </div>
                          <span className="font-medium text-gray-900 dark:text-white">
                            ${item.price.toFixed(2)}
                          </span>
                        </div>
                      ))
                    ) : (
                      // Legacy display for old orders
                      <div className="space-y-4">
                        {order.toolName && (
                          <div className="flex items-start justify-between">
                            <div className="flex items-start">
                              <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-slate-700 flex items-center justify-center mr-3">
                                <div className="text-lg">🔧</div>
                              </div>
                              <div>
                                <h5 className="font-medium text-gray-900 dark:text-white">{order.toolName}</h5>
                                <p className="text-xs text-gray-500 dark:text-gray-400">Individual Tool</p>
                              </div>
                            </div>
                            <span className="font-medium text-gray-900 dark:text-white">
                              ${order.amount?.toFixed(2) || '0.00'} {order.currency}
                            </span>
                          </div>
                        )}
                        {order.planName && (
                          <div className="flex items-start justify-between">
                            <div className="flex items-start">
                              <div className="w-10 h-10 rounded-lg bg-gray-100 dark:bg-slate-700 flex items-center justify-center mr-3">
                                <div className="text-lg">📦</div>
                              </div>
                              <div>
                                <h5 className="font-medium text-gray-900 dark:text-white">{order.planName}</h5>
                                <p className="text-xs text-gray-500 dark:text-gray-400">Subscription Plan</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {order.receiptUrl && (
                    <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <a
                        href={order.receiptUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-yellow-600 dark:text-yellow-400 hover:underline flex items-center"
                      >
                        <CreditCard size={16} className="mr-2" />
                        View Receipt
                      </a>
                    </div>
                  )}

                  <div className="mt-6 flex justify-center">
                    <Image
                      src="https://s3.eu-west-3.amazonaws.com/konnect.network.public/logo_konnect_23a791d66b.svg"
                      alt="Konnect Payment"
                      width={80}
                      height={24}
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        );
      })}
    </div>
  );
};

export default OrderHistory;
