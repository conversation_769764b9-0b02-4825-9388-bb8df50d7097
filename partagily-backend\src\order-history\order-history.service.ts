import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderHistoryResponseDto } from './dto/order-history-response.dto';

@Injectable()
export class OrderHistoryService {
  constructor(private readonly prisma: PrismaService) {}

  async getOrderHistory(userId: string): Promise<OrderHistoryResponseDto> {
    try {
      const orders = await this.prisma.orderHistory.findMany({
        where: {
          userId,
        },
        include: {
          items: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        success: true,
        message: 'Order history retrieved successfully',
        data: orders,
      };
    } catch (error) {
      console.error('Error getting order history:', error);
      return {
        success: false,
        message: 'Failed to retrieve order history',
      };
    }
  }

  async getOrderById(userId: string, orderId: string): Promise<OrderHistoryResponseDto> {
    try {
      const order = await this.prisma.orderHistory.findFirst({
        where: {
          id: orderId,
          userId,
        },
        include: {
          items: true,
        },
      });

      if (!order) {
        return {
          success: false,
          message: 'Order not found',
        };
      }

      return {
        success: true,
        message: 'Order retrieved successfully',
        data: [order],
      };
    } catch (error) {
      console.error('Error getting order:', error);
      return {
        success: false,
        message: 'Failed to retrieve order',
      };
    }
  }
}
