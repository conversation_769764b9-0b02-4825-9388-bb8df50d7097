import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Admin Analytics Controller
 *
 * This controller provides endpoints for admin analytics data.
 */
@ApiTags('admin-analytics')
@Controller('admin/analytics')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminAnalyticsController {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get dashboard overview data
   */
  @Get('dashboard')
  @ApiOperation({ summary: 'Get dashboard overview data' })
  @ApiResponse({ status: 200, description: 'Dashboard overview data' })
  async getDashboardOverview() {
    try {
      // Get user stats
      const totalUsers = await this.prisma.user.count();
      const newUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      // Calculate user growth rate
      const previousMonthUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      const userGrowthRate = previousMonthUsers > 0
        ? ((newUsers - previousMonthUsers) / previousMonthUsers) * 100
        : 0;

      // Get revenue stats
      const totalRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true
        },
        where: {
          paymentStatus: 'COMPLETED'
        }
      });

      const periodRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      // Calculate revenue growth rate
      const previousMonthRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      const revenueGrowthRate = previousMonthRevenue._sum.amount > 0
        ? ((periodRevenue._sum.amount - previousMonthRevenue._sum.amount) / previousMonthRevenue._sum.amount) * 100
        : 0;

      // Get subscription stats
      const totalActiveSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE'
        }
      });

      const newSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      // Get expiring subscriptions (next 7 days)
      const expiringSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
            lte: new Date(new Date().setDate(new Date().getDate() + 7))
          }
        }
      });

      // Calculate subscription growth rate
      const previousMonthSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30))
          }
        }
      });

      const subscriptionGrowthRate = previousMonthSubscriptions > 0
        ? ((newSubscriptions - previousMonthSubscriptions) / previousMonthSubscriptions) * 100
        : 0;

      // Get tool stats
      const totalTools = await this.prisma.tool.count();
      const activeTools = await this.prisma.tool.count({
        where: {
          status: 'AVAILABLE'
        }
      });

      // Get popular tools
      const popularTools = await this.prisma.tool.findMany({
        take: 5,
        orderBy: {
          orders: {
            _count: 'desc'
          }
        },
        include: {
          _count: {
            select: {
              orders: true
            }
          }
        }
      });

      // Transform popular tools data
      const transformedPopularTools = await Promise.all(
        popularTools.map(async (tool) => {
          // Get subscription count for this tool
          const subscriptions = await this.prisma.order.count({
            where: {
              toolId: tool.id,
              paymentStatus: 'COMPLETED'
            }
          });

          // Get revenue for this tool
          const revenue = await this.prisma.order.aggregate({
            _sum: {
              amount: true
            },
            where: {
              toolId: tool.id,
              paymentStatus: 'COMPLETED'
            }
          });

          return {
            id: tool.id,
            name: tool.name,
            subscriptions,
            revenue: revenue._sum.amount || 0
          };
        })
      );

      // Get recent transactions
      const recentTransactions = await this.prisma.order.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: {
            select: {
              name: true
            }
          },
          tool: {
            select: {
              name: true
            }
          }
        }
      });

      // Get revenue over time (last 6 months)
      const revenueOverTime = [];
      for (let i = 5; i >= 0; i--) {
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - i);
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() - i + 1);
        endDate.setDate(0);
        endDate.setHours(23, 59, 59, 999);

        const monthlyRevenue = await this.prisma.order.aggregate({
          _sum: {
            amount: true
          },
          where: {
            paymentStatus: 'COMPLETED',
            createdAt: {
              gte: startDate,
              lte: endDate
            }
          }
        });

        revenueOverTime.push({
          date: startDate.toISOString().split('T')[0],
          revenue: monthlyRevenue._sum.amount || 0
        });
      }

      // Get user demographics (mocked for now, would need additional user data)
      const demographics = [
        { name: 'Tunisia', percentage: 65 },
        { name: 'Algeria', percentage: 15 },
        { name: 'Morocco', percentage: 10 },
        { name: 'Egypt', percentage: 5 },
        { name: 'Other', percentage: 5 },
      ];

      return {
        userStats: {
          totalUsers,
          newUsers,
          activeUsers: totalUsers, // Assuming all users are active for now
          growthRate: userGrowthRate
        },
        revenueStats: {
          totalRevenue: totalRevenue._sum.amount || 0,
          periodRevenue: periodRevenue._sum.amount || 0,
          averageOrderValue: totalRevenue._sum.amount && totalUsers ? totalRevenue._sum.amount / totalUsers : 0,
          growthRate: revenueGrowthRate
        },
        subscriptionStats: {
          totalActiveSubscriptions,
          newSubscriptions,
          expiringSubscriptions,
          growthRate: subscriptionGrowthRate
        },
        toolStats: {
          totalTools,
          activeTools,
          growthRate: 0 // Would need historical data to calculate
        },
        toolCount: totalTools,
        popularTools: transformedPopularTools,
        recentTransactions,
        revenueOverTime,
        demographics
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get user growth analytics
   */
  @Get('user-growth')
  @ApiOperation({ summary: 'Get user growth analytics' })
  @ApiResponse({ status: 200, description: 'User growth analytics data' })
  @ApiQuery({ name: 'timeframe', required: false, enum: ['day', 'week', 'month', 'year'] })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getUserGrowthAnalytics(
    @Query('timeframe') timeframe: string = 'month',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      // Define date range based on timeframe or explicit dates
      let start: Date;
      let end: Date = new Date();

      if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
      } else {
        switch (timeframe) {
          case 'day':
            start = new Date();
            start.setDate(start.getDate() - 1);
            break;
          case 'week':
            start = new Date();
            start.setDate(start.getDate() - 7);
            break;
          case 'year':
            start = new Date();
            start.setFullYear(start.getFullYear() - 1);
            break;
          case 'month':
          default:
            start = new Date();
            start.setMonth(start.getMonth() - 1);
            break;
        }
      }

      // Get user growth data
      const userData = [];

      // Determine interval based on timeframe
      let interval: 'day' | 'week' | 'month';
      if (timeframe === 'day') {
        interval = 'day';
      } else if (timeframe === 'week' || timeframe === 'month') {
        interval = 'day';
      } else {
        interval = 'month';
      }

      // Generate data points
      if (interval === 'day') {
        // Daily data points
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

        for (let i = 0; i < days; i++) {
          const date = new Date(start);
          date.setDate(date.getDate() + i);

          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);

          const count = await this.prisma.user.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          userData.push({
            date: date.toISOString().split('T')[0],
            users: count
          });
        }
      } else {
        // Monthly data points
        const months = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth();

        for (let i = 0; i <= months; i++) {
          const date = new Date(start);
          date.setMonth(date.getMonth() + i);
          date.setDate(1);

          const nextDate = new Date(date);
          nextDate.setMonth(nextDate.getMonth() + 1);

          const count = await this.prisma.user.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          userData.push({
            date: date.toISOString().split('T')[0],
            users: count
          });
        }
      }

      // Get total users in the period
      const totalUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Get previous period for comparison
      const previousStart = new Date(start);
      previousStart.setDate(previousStart.getDate() - (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      const previousPeriodUsers = await this.prisma.user.count({
        where: {
          createdAt: {
            gte: previousStart,
            lt: start
          }
        }
      });

      // Calculate growth rate
      const growthRate = previousPeriodUsers > 0
        ? ((totalUsers - previousPeriodUsers) / previousPeriodUsers) * 100
        : 0;

      return {
        data: userData,
        totalUsers,
        previousPeriodUsers,
        growthRate,
        timeframe,
        startDate: start.toISOString(),
        endDate: end.toISOString()
      };
    } catch (error) {
      console.error('Error fetching user growth analytics:', error);
      throw error;
    }
  }

  /**
   * Get revenue analytics
   */
  @Get('revenue')
  @ApiOperation({ summary: 'Get revenue analytics' })
  @ApiResponse({ status: 200, description: 'Revenue analytics data' })
  @ApiQuery({ name: 'timeframe', required: false, enum: ['day', 'week', 'month', 'year'] })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getRevenueAnalytics(
    @Query('timeframe') timeframe: string = 'month',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      // Define date range based on timeframe or explicit dates
      let start: Date;
      let end: Date = new Date();

      if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
      } else {
        switch (timeframe) {
          case 'day':
            start = new Date();
            start.setDate(start.getDate() - 1);
            break;
          case 'week':
            start = new Date();
            start.setDate(start.getDate() - 7);
            break;
          case 'year':
            start = new Date();
            start.setFullYear(start.getFullYear() - 1);
            break;
          case 'month':
          default:
            start = new Date();
            start.setMonth(start.getMonth() - 1);
            break;
        }
      }

      // Get revenue data
      const revenueData = [];

      // Determine interval based on timeframe
      let interval: 'day' | 'week' | 'month';
      if (timeframe === 'day') {
        interval = 'day';
      } else if (timeframe === 'week' || timeframe === 'month') {
        interval = 'day';
      } else {
        interval = 'month';
      }

      // Generate data points
      if (interval === 'day') {
        // Daily data points
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

        for (let i = 0; i < days; i++) {
          const date = new Date(start);
          date.setDate(date.getDate() + i);

          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);

          const revenue = await this.prisma.order.aggregate({
            _sum: {
              amount: true
            },
            where: {
              paymentStatus: 'COMPLETED',
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          revenueData.push({
            date: date.toISOString().split('T')[0],
            revenue: revenue._sum.amount || 0
          });
        }
      } else {
        // Monthly data points
        const months = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth();

        for (let i = 0; i <= months; i++) {
          const date = new Date(start);
          date.setMonth(date.getMonth() + i);
          date.setDate(1);

          const nextDate = new Date(date);
          nextDate.setMonth(nextDate.getMonth() + 1);

          const revenue = await this.prisma.order.aggregate({
            _sum: {
              amount: true
            },
            where: {
              paymentStatus: 'COMPLETED',
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          revenueData.push({
            date: date.toISOString().split('T')[0],
            revenue: revenue._sum.amount || 0
          });
        }
      }

      // Get total revenue in the period
      const totalRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Get previous period for comparison
      const previousStart = new Date(start);
      previousStart.setDate(previousStart.getDate() - (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      const previousPeriodRevenue = await this.prisma.order.aggregate({
        _sum: {
          amount: true
        },
        where: {
          paymentStatus: 'COMPLETED',
          createdAt: {
            gte: previousStart,
            lt: start
          }
        }
      });

      // Calculate growth rate
      const growthRate = previousPeriodRevenue._sum.amount > 0
        ? ((totalRevenue._sum.amount - previousPeriodRevenue._sum.amount) / previousPeriodRevenue._sum.amount) * 100
        : 0;

      return {
        data: revenueData,
        totalRevenue: totalRevenue._sum.amount || 0,
        previousPeriodRevenue: previousPeriodRevenue._sum.amount || 0,
        growthRate,
        timeframe,
        startDate: start.toISOString(),
        endDate: end.toISOString()
      };
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      throw error;
    }
  }

  /**
   * Get subscription analytics
   */
  @Get('subscriptions')
  @ApiOperation({ summary: 'Get subscription analytics' })
  @ApiResponse({ status: 200, description: 'Subscription analytics data' })
  @ApiQuery({ name: 'timeframe', required: false, enum: ['day', 'week', 'month', 'year'] })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getSubscriptionAnalytics(
    @Query('timeframe') timeframe: string = 'month',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      // Define date range based on timeframe or explicit dates
      let start: Date;
      let end: Date = new Date();

      if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
      } else {
        switch (timeframe) {
          case 'day':
            start = new Date();
            start.setDate(start.getDate() - 1);
            break;
          case 'week':
            start = new Date();
            start.setDate(start.getDate() - 7);
            break;
          case 'year':
            start = new Date();
            start.setFullYear(start.getFullYear() - 1);
            break;
          case 'month':
          default:
            start = new Date();
            start.setMonth(start.getMonth() - 1);
            break;
        }
      }

      // Get subscription data
      const subscriptionData = [];

      // Determine interval based on timeframe
      let interval: 'day' | 'week' | 'month';
      if (timeframe === 'day') {
        interval = 'day';
      } else if (timeframe === 'week' || timeframe === 'month') {
        interval = 'day';
      } else {
        interval = 'month';
      }

      // Generate data points
      if (interval === 'day') {
        // Daily data points
        const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

        for (let i = 0; i < days; i++) {
          const date = new Date(start);
          date.setDate(date.getDate() + i);

          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);

          const newSubscriptions = await this.prisma.subscription.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          const activeSubscriptions = await this.prisma.subscription.count({
            where: {
              status: 'ACTIVE',
              startDate: {
                lte: nextDate
              },
              endDate: {
                gte: date
              }
            }
          });

          subscriptionData.push({
            date: date.toISOString().split('T')[0],
            newSubscriptions,
            activeSubscriptions
          });
        }
      } else {
        // Monthly data points
        const months = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth();

        for (let i = 0; i <= months; i++) {
          const date = new Date(start);
          date.setMonth(date.getMonth() + i);
          date.setDate(1);

          const nextDate = new Date(date);
          nextDate.setMonth(nextDate.getMonth() + 1);

          const newSubscriptions = await this.prisma.subscription.count({
            where: {
              createdAt: {
                gte: date,
                lt: nextDate
              }
            }
          });

          const activeSubscriptions = await this.prisma.subscription.count({
            where: {
              status: 'ACTIVE',
              startDate: {
                lte: nextDate
              },
              endDate: {
                gte: date
              }
            }
          });

          subscriptionData.push({
            date: date.toISOString().split('T')[0],
            newSubscriptions,
            activeSubscriptions
          });
        }
      }

      // Get total subscriptions in the period
      const totalSubscriptions = await this.prisma.subscription.count({
        where: {
          createdAt: {
            gte: start,
            lte: end
          }
        }
      });

      // Get active subscriptions
      const activeSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          startDate: {
            lte: end
          },
          endDate: {
            gte: start
          }
        }
      });

      // Get expiring subscriptions (next 30 days)
      const expiringSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
            lte: new Date(new Date().setDate(new Date().getDate() + 30))
          }
        }
      });

      // Get previous period for comparison
      const previousStart = new Date(start);
      previousStart.setDate(previousStart.getDate() - (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));

      const previousPeriodSubscriptions = await this.prisma.subscription.count({
        where: {
          createdAt: {
            gte: previousStart,
            lt: start
          }
        }
      });

      // Calculate growth rate
      const growthRate = previousPeriodSubscriptions > 0
        ? ((totalSubscriptions - previousPeriodSubscriptions) / previousPeriodSubscriptions) * 100
        : 0;

      // Get subscriptions by plan
      const plans = await this.prisma.plan.findMany();
      const subscriptionsByPlan = await Promise.all(
        plans.map(async (plan) => {
          const count = await this.prisma.subscription.count({
            where: {
              planId: plan.id,
              status: 'ACTIVE'
            }
          });

          return {
            planId: plan.id,
            planName: plan.name,
            count
          };
        })
      );

      return {
        data: subscriptionData,
        totalSubscriptions,
        activeSubscriptions,
        expiringSubscriptions,
        growthRate,
        subscriptionsByPlan,
        timeframe,
        startDate: start.toISOString(),
        endDate: end.toISOString()
      };
    } catch (error) {
      console.error('Error fetching subscription analytics:', error);
      throw error;
    }
  }

  /**
   * Get tool usage analytics
   */
  @Get('tool-usage')
  @ApiOperation({ summary: 'Get tool usage analytics' })
  @ApiResponse({ status: 200, description: 'Tool usage analytics data' })
  @ApiQuery({ name: 'timeframe', required: false, enum: ['day', 'week', 'month', 'year'] })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  async getToolUsageAnalytics(
    @Query('timeframe') timeframe: string = 'month',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      // Define date range based on timeframe or explicit dates
      let start: Date;
      let end: Date = new Date();

      if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
      } else {
        switch (timeframe) {
          case 'day':
            start = new Date();
            start.setDate(start.getDate() - 1);
            break;
          case 'week':
            start = new Date();
            start.setDate(start.getDate() - 7);
            break;
          case 'year':
            start = new Date();
            start.setFullYear(start.getFullYear() - 1);
            break;
          case 'month':
          default:
            start = new Date();
            start.setMonth(start.getMonth() - 1);
            break;
        }
      }

      // Get all tools
      const tools = await this.prisma.tool.findMany();

      // Get usage data for each tool
      const toolUsageData = await Promise.all(
        tools.map(async (tool) => {
          const orders = await this.prisma.order.count({
            where: {
              toolId: tool.id,
              paymentStatus: 'COMPLETED',
              createdAt: {
                gte: start,
                lte: end
              }
            }
          });

          const revenue = await this.prisma.order.aggregate({
            _sum: {
              amount: true
            },
            where: {
              toolId: tool.id,
              paymentStatus: 'COMPLETED',
              createdAt: {
                gte: start,
                lte: end
              }
            }
          });

          return {
            id: tool.id,
            name: tool.name,
            orders,
            revenue: revenue._sum.amount || 0
          };
        })
      );

      // Sort by orders (usage) in descending order
      toolUsageData.sort((a, b) => b.orders - a.orders);

      return {
        data: toolUsageData,
        timeframe,
        startDate: start.toISOString(),
        endDate: end.toISOString()
      };
    } catch (error) {
      console.error('Error fetching tool usage analytics:', error);
      throw error;
    }
  }
}
