import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import axios from "axios";

// Define API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Create a custom axios instance for auth
const authApi = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// NextAuth configuration
const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        rememberMe: { label: "Remember Me", type: "checkbox" },
      },
      async authorize(credentials) {
        try {
          // Call the existing backend API for login
          const response = await authApi.post("/auth/login", {
            email: credentials?.email,
            password: credentials?.password,
          });

          if (response.data && response.data.user) {
            // Store the tokens in the token object (will be encrypted in the session)
            return {
              id: response.data.user.id,
              name: response.data.user.name,
              email: response.data.user.email,
              role: response.data.user.role,
              accessToken: response.data.accessToken,
              refreshToken: response.data.refreshToken,
              rememberMe: credentials?.rememberMe === "true",
            };
          }
          return null;
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.accessToken = user.accessToken;
        token.refreshToken = user.refreshToken;
        token.rememberMe = user.rememberMe;
      }

      // Handle token refresh
      if (trigger === "update" && session?.accessToken) {
        token.accessToken = session.accessToken;
        token.refreshToken = session.refreshToken;
      }

      // Check if token is expired and needs refresh
      const tokenExpiration = (token.exp as number) || 0;
      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = tokenExpiration - currentTime;

      // If token is about to expire (less than 5 minutes) and we have a refresh token
      if (timeUntilExpiry < 300 && token.refreshToken) {
        try {
          // Call the refresh token endpoint
          const response = await authApi.post("/auth/refresh", {
            refreshToken: token.refreshToken,
          });

          if (response.data && response.data.accessToken) {
            token.accessToken = response.data.accessToken;
            token.refreshToken = response.data.refreshToken;
          }
        } catch (error) {
          console.error("Token refresh error:", error);
          // If refresh fails, we'll continue with the existing token
          // It will eventually fail and user will need to log in again
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.accessToken = token.accessToken as string;
        session.refreshToken = token.refreshToken as string;
      }
      return session;
    },
  },
  pages: {
    signIn: "/signin",
    signOut: "/",
    error: "/signin",
  },
  session: {
    strategy: "jwt",
    maxAge: 60 * 60, // 1 hour
  },
  debug: process.env.NODE_ENV === "development",
});

export { handler as GET, handler as POST };
