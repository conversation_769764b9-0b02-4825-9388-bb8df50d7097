import { Controller, Get, Param, Request, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ToolsService } from '../tools.service';
import { AuditService } from '../../common/services/audit.service';

@ApiTags('tools')
@Controller('tools')
export class AccessController {
  constructor(
    private readonly toolsService: ToolsService,
    private readonly auditService: AuditService,
  ) {}

  @Get(':id/access')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Check if user has access to a tool' })
  @ApiResponse({ status: 200, description: 'Access check result' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async checkAccess(@Param('id') toolId: string, @Request() req) {
    // Get the user ID from the request
    // For development purposes, use a default ID if user is not defined
    const userId = req.user?.id || 'development-user-id';

    try {
      // Check if the tool exists
      const tool = await this.toolsService.findOne(toolId);
      if (!tool) {
        return {
          hasAccess: false,
          message: 'Tool not found'
        };
      }

      // Check if the user has an active subscription for this tool
      const hasAccess = await this.toolsService.userHasAccessToTool(userId, toolId);

      // Log the access check
      await this.auditService.log({
        type: 'tool.access.check' as any,
        userId,
        severity: 'info' as any,
        details: {
          toolName: tool.name,
          toolId,
          hasAccess,
          timestamp: new Date().toISOString(),
        },
      });

      if (hasAccess) {
        return {
          hasAccess: true,
          message: 'You have access to this tool'
        };
      } else {
        return {
          hasAccess: false,
          message: 'You do not have access to this tool. Please subscribe to a plan that includes this tool.'
        };
      }
    } catch (error) {
      // Log the error
      await this.auditService.log({
        type: 'tool.access.check.error' as any,
        userId,
        severity: 'error' as any,
        details: {
          toolId,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      });

      return {
        hasAccess: false,
        message: 'Error checking access to tool'
      };
    }
  }
}
