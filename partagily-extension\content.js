// Enhanced content script for Partagily extension with anti-debugging features
console.log('Partagily content script loaded');

// Inject the anti-debugging script into the page
function injectAntiDebuggingScript() {
  try {
    // Create a script element
    const script = document.createElement('script');

    // Set the source to our inject.js file
    script.src = chrome.runtime.getURL('src/inject.js');

    // Add the script to the page
    (document.head || document.documentElement).appendChild(script);

    // Remove the script element after it has loaded (optional)
    script.onload = function() {
      script.remove();
    };

    console.log('Anti-debugging script injected');
  } catch (error) {
    console.error('Error injecting anti-debugging script:', error);
  }
}

// Inject the script as soon as possible
injectAntiDebuggingScript();

// Implement basic anti-inspection measures directly in the content script
document.addEventListener('contextmenu', function(e) {
  e.preventDefault();
  return false;
}, { capture: true });

document.addEventListener('keydown', function(e) {
  // Prevent F12
  if (e.key === 'F12' || e.keyCode === 123) {
    e.preventDefault();
    return false;
  }

  // Prevent Ctrl+Shift+I / Cmd+Option+I
  if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.keyCode === 73)) {
    e.preventDefault();
    return false;
  }

  // Prevent Ctrl+Shift+J / Cmd+Option+J
  if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'J' || e.key === 'j' || e.keyCode === 74)) {
    e.preventDefault();
    return false;
  }

  // Prevent Ctrl+Shift+C / Cmd+Option+C
  if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'C' || e.key === 'c' || e.keyCode === 67)) {
    e.preventDefault();
    return false;
  }

  // Prevent Ctrl+U / Cmd+U (view source)
  if ((e.ctrlKey || e.metaKey) && (e.key === 'U' || e.key === 'u' || e.keyCode === 85)) {
    e.preventDefault();
    return false;
  }
}, { capture: true });

// Listen for messages from the background script
try {
  if (chrome && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      try {
        console.log('Content script received message:', message);

        if (!message || !message.action) {
          sendResponse({ success: false, error: 'Invalid message format' });
          return true;
        }

        switch (message.action) {
          case 'checkDomain':
            sendResponse({
              domain: window.location.hostname,
              success: true
            });
            return true;

          case 'getPageInfo':
            sendResponse({
              url: window.location.href,
              title: document.title,
              success: true
            });
            return true;

          default:
            console.warn(`Unknown action: ${message.action}`);
            sendResponse({ success: false, error: 'Unknown action' });
            return true;
        }
      } catch (error) {
        console.error('Error handling message:', error);
        sendResponse({ success: false, error: 'Internal error: ' + error.message });
        return true;
      }
    });
    console.log('Message listener registered successfully');
  } else {
    console.warn('Chrome runtime API not available');
  }
} catch (error) {
  console.error('Error setting up message listener:', error);
}

// Notify the extension is installed (for platform detection)
try {
  // Create a custom element to communicate with the page
  const notifier = document.createElement('div');
  notifier.id = 'partagily-extension-installed';
  notifier.style.display = 'none';
  document.body.appendChild(notifier);

  // Dispatch a custom event that the page can listen for
  const event = new CustomEvent('partagilyExtensionInstalled', {
    detail: { installed: true }
  });
  document.dispatchEvent(event);

  console.log('Extension installation notified to page');
} catch (error) {
  console.error('Error notifying extension installation:', error);
}

// Log that the content script is ready
console.log('Partagily content script ready on:', window.location.href);
