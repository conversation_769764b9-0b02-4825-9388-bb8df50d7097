/*
  This CSS file is specifically designed to prevent layout shifts
  when the cart drawer opens and closes.
*/

/*
  Force the dashboard root to maintain its position and dimensions
  regardless of what happens with the cart drawer
*/
#dashboard-root {
  width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  overflow-x: hidden !important;
}

/*
  Force the dashboard main content to maintain its position and dimensions
  regardless of what happens with the cart drawer
*/
#dashboard-main-content {
  width: 100% !important;
  max-width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  overflow-x: hidden !important;
}

/*
  Target the main content by class as well for redundancy
*/
.flex.flex-col.flex-1.pl-0.lg\:pl-64 {
  width: 100% !important;
  max-width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  overflow-x: hidden !important;
}

/*
  Ensure the cart drawer is absolutely positioned and doesn't affect layout
*/
.cart-drawer {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 10000 !important;
  width: 360px !important;
  max-width: 100% !important;
  height: 100% !important;
  transform: none !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/*
  Mobile cart drawer styles
*/
@media (max-width: 768px) {
  .cart-drawer {
    top: auto !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 80vh !important;
    transform: none !important;
    border-radius: 16px 16px 0 0 !important;
    box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.15) !important;
  }
}

/*
  Ensure the backdrop doesn't affect layout
*/
.cart-backdrop {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  pointer-events: all !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
  transition: opacity 0.2s ease !important;
}

/*
  When cart is open, ensure the body and html don't change in ways that would affect layout
*/
html.cart-open,
body.cart-open {
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  width: 100% !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
  transform: none !important;
  transition: none !important;
}

/*
  Ensure the dashboard main content doesn't shift when cart is open
*/
body.cart-open #dashboard-main-content,
body.cart-open .flex.flex-col.flex-1.pl-0.lg\:pl-64 {
  width: 100% !important;
  max-width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  overflow-x: hidden !important;
}

/*
  Ensure the dashboard root doesn't shift when cart is open
*/
body.cart-open #dashboard-root {
  width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  overflow-x: hidden !important;
}

/*
  Ensure the sidebar stays in place
*/
.sidebar {
  position: fixed !important;
  z-index: 40 !important;
}

/*
  Ensure proper spacing for the main content
*/
@media (min-width: 1024px) {
  .lg\:pl-64 {
    padding-left: 16rem !important;
  }
}

/*
  Mobile-specific styles to ensure no padding on small screens
*/
@media (max-width: 1023px) {
  #dashboard-main-content {
    padding-left: 0 !important;
    width: 100% !important;
  }

  /* Ensure the mobile menu button has enough space */
  .lg\:hidden.fixed.top-4.left-4.z-50 {
    top: 0.75rem !important;
    left: 0.75rem !important;
    z-index: 50 !important;
  }
}
