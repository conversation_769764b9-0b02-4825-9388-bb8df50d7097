const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // Find Netflix tool
    const netflixTool = await prisma.tool.findFirst({
      where: {
        name: { contains: 'Netflix', mode: 'insensitive' },
      },
    });

    if (!netflixTool) {
      console.log('Netflix tool not found');
      return;
    }

    console.log('Netflix tool found:');
    console.log(netflixTool);

    // Find cookies for Netflix tool
    const cookies = await prisma.cookie.findMany({
      where: { toolId: netflixTool.id },
    });

    console.log(`Found ${cookies.length} cookies for Netflix tool:`);
    console.log(JSON.stringify(cookies, null, 2));
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
