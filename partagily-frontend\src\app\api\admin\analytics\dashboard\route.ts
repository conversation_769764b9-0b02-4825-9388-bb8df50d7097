import { NextRequest, NextResponse } from 'next/server';

// Generate mock analytics dashboard data
const generateMockDashboardData = () => {
  // Generate random user stats
  const userStats = {
    totalUsers: Math.floor(Math.random() * 1000) + 100,
    newUsers: Math.floor(Math.random() * 100) + 10,
    activeUsers: Math.floor(Math.random() * 500) + 50,
    growthRate: Math.random() * 10 + 2,
  };
  
  // Generate random revenue stats
  const revenueStats = {
    totalRevenue: Math.floor(Math.random() * 10000) + 1000,
    periodRevenue: Math.floor(Math.random() * 5000) + 500,
    averageOrderValue: Math.floor(Math.random() * 100) + 20,
    growthRate: Math.random() * 15 + 5,
  };
  
  // Generate random subscription stats
  const subscriptionStats = {
    totalActiveSubscriptions: Math.floor(Math.random() * 500) + 50,
    newSubscriptions: Math.floor(Math.random() * 50) + 5,
    expiringSubscriptions: Math.floor(Math.random() * 20) + 2,
    growthRate: Math.random() * 12 + 3,
  };
  
  // Generate random tool stats
  const toolStats = {
    totalTools: Math.floor(Math.random() * 50) + 10,
    activeTools: Math.floor(Math.random() * 40) + 5,
    growthRate: Math.random() * 8 + 2,
  };
  
  // Generate random popular tools
  const popularTools = [
    {
      id: '1',
      name: 'Netflix',
      subscriptions: Math.floor(Math.random() * 200) + 50,
      revenue: Math.floor(Math.random() * 2000) + 500,
    },
    {
      id: '2',
      name: 'Spotify',
      subscriptions: Math.floor(Math.random() * 150) + 40,
      revenue: Math.floor(Math.random() * 1500) + 400,
    },
    {
      id: '3',
      name: 'ChatGPT',
      subscriptions: Math.floor(Math.random() * 100) + 30,
      revenue: Math.floor(Math.random() * 1000) + 300,
    },
    {
      id: '4',
      name: 'Adobe Creative Cloud',
      subscriptions: Math.floor(Math.random() * 80) + 20,
      revenue: Math.floor(Math.random() * 800) + 200,
    },
    {
      id: '5',
      name: 'Microsoft 365',
      subscriptions: Math.floor(Math.random() * 60) + 10,
      revenue: Math.floor(Math.random() * 600) + 100,
    },
  ];
  
  // Generate random recent transactions
  const recentTransactions = [
    {
      id: '1',
      user: { name: 'Ahmed Ben Ali' },
      tool: { name: 'Netflix' },
      amount: 19.99,
      status: 'completed',
      date: new Date(Date.now() - 1000 * 60 * 60).toISOString(),
    },
    {
      id: '2',
      user: { name: 'Sarra Mejri' },
      tool: { name: 'Spotify' },
      amount: 9.99,
      status: 'completed',
      date: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
    },
    {
      id: '3',
      user: { name: 'Mohamed Trabelsi' },
      tool: { name: 'ChatGPT' },
      amount: 20.00,
      status: 'pending',
      date: new Date(Date.now() - 1000 * 60 * 180).toISOString(),
    },
  ];
  
  // Generate random monthly revenue data
  const revenueOverTime = [];
  const now = new Date();
  for (let i = 5; i >= 0; i--) {
    const date = new Date(now);
    date.setMonth(date.getMonth() - i);
    revenueOverTime.push({
      date: date.toISOString().split('T')[0],
      revenue: Math.floor(Math.random() * 3000) + 1000,
    });
  }
  
  // Generate random demographics data
  const demographics = [
    { name: 'Tunisia', percentage: 65 },
    { name: 'Algeria', percentage: 15 },
    { name: 'Morocco', percentage: 10 },
    { name: 'Egypt', percentage: 5 },
    { name: 'Other', percentage: 5 },
  ];
  
  return {
    userStats,
    revenueStats,
    subscriptionStats,
    toolStats,
    toolCount: toolStats.totalTools,
    popularTools,
    recentTransactions,
    revenueOverTime,
    demographics,
  };
};

export async function GET(request: NextRequest) {
  try {
    // Generate mock dashboard data
    const data = generateMockDashboardData();
    
    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error generating dashboard data:', error);
    return NextResponse.json({ 
      error: 'Failed to generate dashboard data',
      message: error.message 
    }, { status: 500 });
  }
}
