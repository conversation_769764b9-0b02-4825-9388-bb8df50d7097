import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuditService, AuditEventType } from '../../common/services/audit.service';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  constructor(private auditService: AuditService) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Get the request
    const request = context.switchToHttp().getRequest();
    const { email } = request.body;
    const ip = request.ip;
    const userAgent = request.headers['user-agent'] || 'unknown';

    try {
      // Attempt to authenticate
      const result = await super.canActivate(context) as boolean;

      // If authentication failed, log the failure
      if (!result) {
        await this.auditService.logAuthEvent(
          AuditEventType.AUTH_LOGIN_FAILURE,
          null, // No user ID for failed login
          ip,
          userAgent,
          { email, reason: 'Authentication failed' }
        );
      }

      return result;
    } catch (error) {
      // Log the failed login attempt
      await this.auditService.logAuthEvent(
        AuditEventType.AUTH_LOGIN_FAILURE,
        null, // No user ID for failed login
        ip,
        userAgent,
        { email, reason: error.message || 'Authentication failed' }
      );

      // Rethrow the error
      throw error;
    }
  }
}
