import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsN<PERSON>ber, IsDate, <PERSON>Int, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

export enum PaymentMethod {
  KONNECT = 'KONNECT',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

export class UpdatePaymentDto {
  @ApiProperty({
    description: 'The payment status',
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
  })
  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus;

  @ApiProperty({
    description: 'Notes about the payment update',
    example: 'Payment manually verified',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class RefundPaymentDto {
  @ApiProperty({
    description: 'The payment ID to refund',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  paymentId: string;

  @ApiProperty({
    description: 'The amount to refund',
    example: 100.50,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'The reason for the refund',
    example: 'Customer requested cancellation',
  })
  @IsString()
  reason: string;
}

export class PaymentFilterDto {
  @ApiProperty({
    description: 'Filter by user ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'Filter by tool ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolId?: string;

  @ApiProperty({
    description: 'Filter by payment status',
    enum: PaymentStatus,
    example: PaymentStatus.COMPLETED,
    required: false,
  })
  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus;

  @ApiProperty({
    description: 'Filter by payment method',
    enum: PaymentMethod,
    example: PaymentMethod.KONNECT,
    required: false,
  })
  @IsEnum(PaymentMethod)
  @IsOptional()
  paymentMethod?: PaymentMethod;

  @ApiProperty({
    description: 'Filter by start date',
    example: '2023-01-01',
    required: false,
  })
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  startDate?: Date;

  @ApiProperty({
    description: 'Filter by end date',
    example: '2023-12-31',
    required: false,
  })
  @Type(() => Date)
  @IsDate()
  @IsOptional()
  endDate?: Date;

  @ApiProperty({
    description: 'Sort by field',
    example: 'createdAt',
    required: false,
  })
  @IsString()
  @IsOptional()
  sortBy?: string;

  @ApiProperty({
    description: 'Sort order (ASC or DESC)',
    example: 'DESC',
    required: false,
  })
  @IsEnum(['ASC', 'DESC'])
  @IsOptional()
  sortOrder?: 'ASC' | 'DESC' = 'DESC';

  @ApiProperty({
    description: 'Page number',
    example: 1,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number = 10;
}
