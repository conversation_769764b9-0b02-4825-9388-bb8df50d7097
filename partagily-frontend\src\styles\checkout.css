/* Checkout page specific styles */

/* Add extra space at the top to account for the fixed navbar */
.checkout-container {
  padding-top: 120px !important;
  min-height: calc(100vh - 90px) !important;
}

/* Ensure proper spacing between header and content */
.checkout-header {
  margin-bottom: 2.5rem !important;
  padding-top: 1.5rem !important;
}

/* Ensure proper spacing between content and footer */
.checkout-content {
  margin-bottom: 4rem !important;
}

/* Ensure proper spacing for empty cart state */
.empty-cart {
  padding: 3rem !important;
  margin: 3rem 0 !important;
}

/* Ensure proper spacing for cart items */
.cart-item {
  padding: 2rem !important;
}

/* Ensure proper spacing for order summary */
.order-summary {
  padding: 2rem !important;
}

/* Add extra space at the bottom to account for the footer */
.checkout-footer {
  margin-top: 4rem !important;
}

/* Ensure proper spacing for mobile devices */
@media (max-width: 768px) {
  .checkout-container {
    padding-top: 100px !important;
  }
  
  .checkout-header {
    margin-bottom: 2rem !important;
  }
  
  .empty-cart {
    padding: 2rem !important;
    margin: 2rem 0 !important;
  }
}
