import { ApiProperty } from '@nestjs/swagger';

export class OrderItemDto {
  @ApiProperty({
    description: 'Order item ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Type of item (TOOL or PLAN)',
    example: 'TOOL',
  })
  type: string;

  @ApiProperty({
    description: 'ID of the item (tool or plan)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  itemId: string;

  @ApiProperty({
    description: 'Name of the item',
    example: 'Netflix Premium',
  })
  name: string;

  @ApiProperty({
    description: 'Price of the item',
    example: 19.99,
  })
  price: number;
}

export class OrderHistoryItemDto {
  @ApiProperty({
    description: 'Order ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Order number',
    example: 'ORD-1234567890',
  })
  orderNumber: string;

  @ApiProperty({
    description: 'Total amount',
    example: 39.98,
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Order status',
    example: 'COMPLETED',
  })
  status: string;

  @ApiProperty({
    description: 'Payment method',
    example: 'KONNECT',
  })
  paymentMethod: string;

  @ApiProperty({
    description: 'Created at',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Order items',
    type: 'array',
    items: {
      $ref: '#/components/schemas/OrderItemDto'
    }
  })
  items: OrderItemDto[];
}

export class OrderHistoryResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Order history retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Order history data',
    type: 'array',
    items: {
      $ref: '#/components/schemas/OrderHistoryItemDto'
    }
  })
  data?: OrderHistoryItemDto[];
}
