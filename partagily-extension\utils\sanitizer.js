/**
 * Utility functions for input sanitization in the Chrome extension
 */

/**
 * Sanitize a string to prevent XSS attacks
 * @param {string} input - The input string to sanitize
 * @returns {string} - The sanitized string
 */
export function sanitizeString(input) {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .replace(/`/g, '&#x60;')
    .replace(/\$/g, '&#x24;');
}

/**
 * Sanitize an object by sanitizing all string properties
 * @param {Object} obj - The object to sanitize
 * @returns {Object} - The sanitized object
 */
export function sanitizeObject(obj) {
  if (!obj || typeof obj !== 'object') {
    return {};
  }
  
  const result = {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      
      if (typeof value === 'string') {
        result[key] = sanitizeString(value);
      } else if (Array.isArray(value)) {
        result[key] = sanitizeArray(value);
      } else if (value && typeof value === 'object') {
        result[key] = sanitizeObject(value);
      } else {
        result[key] = value;
      }
    }
  }
  
  return result;
}

/**
 * Sanitize an array by sanitizing all string elements
 * @param {Array} arr - The array to sanitize
 * @returns {Array} - The sanitized array
 */
export function sanitizeArray(arr) {
  if (!Array.isArray(arr)) {
    return [];
  }
  
  return arr.map(item => {
    if (typeof item === 'string') {
      return sanitizeString(item);
    } else if (Array.isArray(item)) {
      return sanitizeArray(item);
    } else if (item && typeof item === 'object') {
      return sanitizeObject(item);
    } else {
      return item;
    }
  });
}

/**
 * Validate and sanitize a URL
 * @param {string} url - The URL to validate and sanitize
 * @returns {string|null} - The sanitized URL or null if invalid
 */
export function validateAndSanitizeUrl(url) {
  if (typeof url !== 'string') {
    return null;
  }
  
  // Basic URL validation
  try {
    const parsedUrl = new URL(url);
    
    // Only allow http and https protocols
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
      return null;
    }
    
    return parsedUrl.toString();
  } catch (error) {
    return null;
  }
}

/**
 * Validate and sanitize an email address
 * @param {string} email - The email to validate and sanitize
 * @returns {string|null} - The sanitized email or null if invalid
 */
export function validateAndSanitizeEmail(email) {
  if (typeof email !== 'string') {
    return null;
  }
  
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return null;
  }
  
  return sanitizeString(email);
}

/**
 * Validate and sanitize a domain name
 * @param {string} domain - The domain to validate and sanitize
 * @returns {string|null} - The sanitized domain or null if invalid
 */
export function validateAndSanitizeDomain(domain) {
  if (typeof domain !== 'string') {
    return null;
  }
  
  // Remove protocol and path if present
  let cleanDomain = domain.trim().toLowerCase();
  cleanDomain = cleanDomain.replace(/^https?:\/\//, '');
  cleanDomain = cleanDomain.split('/')[0];
  
  // Basic domain validation
  const domainRegex = /^[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,}$/;
  if (!domainRegex.test(cleanDomain)) {
    return null;
  }
  
  return cleanDomain;
}

/**
 * Sanitize HTML content
 * @param {string} html - The HTML content to sanitize
 * @returns {string} - The sanitized HTML
 */
export function sanitizeHtml(html) {
  if (typeof html !== 'string') {
    return '';
  }
  
  // Create a temporary DOM element
  const tempElement = document.createElement('div');
  tempElement.textContent = html;
  
  // Return the sanitized HTML
  return tempElement.innerHTML;
}

/**
 * Validate JSON input
 * @param {string} jsonString - The JSON string to validate
 * @returns {Object|null} - The parsed JSON object or null if invalid
 */
export function validateJson(jsonString) {
  if (typeof jsonString !== 'string') {
    return null;
  }
  
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return null;
  }
}
