'use client';

import { motion } from 'framer-motion';

const SimplifiedPaymentSection = () => {
  const paymentMethods = [
    {
      name: "E-Din<PERSON>",
      icon: "💳",
      color: "bg-blue-50 dark:bg-[rgba(79,142,255,0.08)]",
      borderColor: "border-transparent dark:border-[rgba(79,142,255,0.2)]"
    },
    {
      name: "La Poste",
      icon: "📬",
      color: "bg-yellow-50 dark:bg-[rgba(255,173,0,0.08)]",
      borderColor: "border-transparent dark:border-[rgba(255,173,0,0.2)]"
    },
    {
      name: "Bank Transfer",
      icon: "🏦",
      color: "bg-green-50 dark:bg-[rgba(48,209,88,0.08)]",
      borderColor: "border-transparent dark:border-[rgba(48,209,88,0.2)]"
    }
  ];

  return (
    <section className="py-16 bg-white dark:bg-gray-900" id="payment-methods">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            Pay with <span className="text-[#FFAD00]">Local Methods</span> 🇹🇳
          </h2>
          <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            No international payment methods required. Use the payment options you already have in Tunisia.
          </p>
        </motion.div>

        <div className="flex flex-wrap justify-center gap-8 mb-12">
          {paymentMethods.map((method, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className={`${method.color} p-6 rounded-xl border ${method.borderColor} shadow-md hover-card flex flex-col items-center text-center w-64`}
            >
              <div className="text-5xl mb-4">{method.icon}</div>
              <h3 className="text-xl font-bold">{method.name}</h3>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-center"
        >
          <a
            href="#pricing"
            className="btn btn-primary px-8 py-3 text-lg hover-glow inline-flex items-center gap-2"
          >
            See our plans 🚀
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default SimplifiedPaymentSection;
