.review-card {
  position: relative;
  background: white;
  border-radius: 24px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.review-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.review-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #8b5cf6, #d946ef);
  opacity: 0.7;
}

.review-star {
  color: #10b981;
  font-size: 1.5rem;
  line-height: 1;
}

.review-star-empty {
  color: #e5e7eb;
  font-size: 1.5rem;
  line-height: 1;
}

.review-rating {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.review-name {
  font-weight: 600;
  color: #4b5563;
  font-size: 1rem;
}

.review-text {
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Flex layout for reviews */
.reviews-grid {
  display: flex;
  gap: 1.5rem;
  padding: 0.5rem;
}

/* Make sure review cards have proper spacing */
.review-card {
  margin-right: 0.5rem;
  width: 100%;
  min-width: 250px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .review-card {
    min-width: 200px;
  }
}

@media (max-width: 640px) {
  .review-card {
    min-width: 180px;
  }
}

/* Ensure the container doesn't allow horizontal scrolling */
.reviews-container {
  overflow: hidden;
  position: relative;
  padding: 0 1rem;
}

/* Style for disabled navigation buttons */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
