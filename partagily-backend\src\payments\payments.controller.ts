import { 
  Controller, 
  Post, 
  Body, 
  Get, 
  Param, 
  UseGuards, 
  Req, 
  Headers,
  HttpCode,
  HttpStatus,
  Logger,
  Ip,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth, 
  ApiHeader,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentWebhookDto } from './dto/payment-webhook.dto';
import { PaymentResponseDto } from './dto/payment-response.dto';
import { Order } from './entities/order.entity';
import { AuditService, AuditEventType } from '../common/services/audit.service';

@ApiTags('payments')
@Controller('payments')
export class PaymentsController {
  private readonly logger = new Logger(PaymentsController.name);

  constructor(
    private readonly paymentsService: PaymentsService,
    private readonly auditService: AuditService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiResponse({ status: 201, description: 'Payment initiated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createPayment(
    @Req() req,
    @Body() createPaymentDto: CreatePaymentDto,
    @Ip() ip: string,
  ): Promise<PaymentResponseDto> {
    try {
      const result = await this.paymentsService.createPayment(req.user.id, createPaymentDto);
      
      // Audit log for payment creation
      await this.auditService.logPaymentEvent(
        AuditEventType.PAYMENT_CREATED,
        req.user.id,
        ip,
        {
          toolId: createPaymentDto.toolId,
          toolName: createPaymentDto.toolName,
          planId: createPaymentDto.planId,
          planName: createPaymentDto.planName,
          amount: createPaymentDto.amount,
          currency: createPaymentDto.currency || 'TND',
          orderId: result.data?.orderId,
          orderNumber: result.data?.orderNumber,
        }
      );
      
      return result;
    } catch (error) {
      // Audit log for payment failure
      await this.auditService.logPaymentEvent(
        AuditEventType.PAYMENT_FAILED,
        req.user.id,
        ip,
        {
          toolId: createPaymentDto.toolId,
          toolName: createPaymentDto.toolName,
          planId: createPaymentDto.planId,
          planName: createPaymentDto.planName,
          amount: createPaymentDto.amount,
          error: error.message,
        }
      );
      
      throw error;
    }
  }

  @Post('webhook')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle payment webhook from Konnect' })
  @ApiHeader({ name: 'x-konnect-webhook-signature', description: 'Webhook signature for verification' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request or invalid signature' })
  async handleWebhook(
    @Body() webhookDto: PaymentWebhookDto,
    @Headers('x-konnect-webhook-signature') signature: string,
    @Ip() ip: string,
  ): Promise<PaymentResponseDto> {
    this.logger.log('Received webhook from Konnect');
    
    try {
      const result = await this.paymentsService.handleWebhook(webhookDto, signature);
      
      // Determine the event type based on the payment status
      const eventType = result.data?.status === 'COMPLETED' 
        ? AuditEventType.PAYMENT_COMPLETED 
        : (result.data?.status === 'FAILED' ? AuditEventType.PAYMENT_FAILED : AuditEventType.PAYMENT_CREATED);
      
      // Extract user ID from the order if available
      const order = await this.paymentsService.getOrderById(result.data?.orderId);
      
      // Audit log for payment webhook
      await this.auditService.logPaymentEvent(
        eventType,
        order.userId,
        ip,
        {
          orderId: result.data?.orderId,
          orderNumber: result.data?.orderNumber,
          status: result.data?.status,
          paymentId: webhookDto.paymentId,
          webhookEvent: webhookDto.event,
        }
      );
      
      return result;
    } catch (error) {
      // Audit log for webhook processing failure
      await this.auditService.logSystemEvent(
        AuditEventType.SYSTEM_ERROR,
        {
          component: 'PaymentsController.handleWebhook',
          error: error.message,
          paymentId: webhookDto.paymentId,
          webhookEvent: webhookDto.event,
        }
      );
      
      throw error;
    }
  }

  @Get('order/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order details by ID' })
  @ApiResponse({ status: 200, description: 'Return order details' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async getOrderById(@Param('id') id: string): Promise<Order> {
    return this.paymentsService.getOrderById(id);
  }

  @Get('user/orders')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all orders for the current user' })
  @ApiResponse({ status: 200, description: 'Return user orders' })
  async getUserOrders(@Req() req): Promise<Order[]> {
    return this.paymentsService.getUserOrders(req.user.id);
  }
}
