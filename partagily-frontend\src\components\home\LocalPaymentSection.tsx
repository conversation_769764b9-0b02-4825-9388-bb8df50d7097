'use client';

import { motion } from 'framer-motion';

const LocalPaymentSection = () => {
  const paymentMethods = [
    {
      name: "E-Dinar",
      description: "Pay directly with your e-Dinar card",
      icon: "💳"
    },
    {
      name: "Postal Payment",
      description: "Use Tunisian postal payment services",
      icon: "📬"
    },
    {
      name: "Local Bank Transfer",
      description: "Transfer directly from your Tunisian bank",
      icon: "🏦"
    },
    {
      name: "D17",
      description: "Pay using the D17 mobile payment app",
      icon: "📱"
    }
  ];

  return (
    <section className="py-20 bg-white" id="local-payment">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6 terminal-text">
              <span className="text-yellow-400">Local Payment</span> Methods 🇹🇳
            </h2>
            
            <p className="text-xl mb-8 text-gray-700">
              We understand the challenges Tunisians face with international payments.
              That's why we've integrated local payment methods that work for you.
            </p>
            
            <div className="space-y-6">
              {paymentMethods.map((method, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.2 * index, duration: 0.5 }}
                  className="flex items-start gap-4 p-4 bg-pink-50 rounded-lg hover:bg-pink-100 transition-colors"
                >
                  <div className="text-3xl">{method.icon}</div>
                  <div>
                    <h3 className="text-lg font-bold terminal-text">{method.name}</h3>
                    <p className="text-gray-700">{method.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="relative"
          >
            <div className="bg-gradient-to-r from-pink-100 to-blue-100 p-8 rounded-xl shadow-xl">
              <div className="bg-white rounded-lg p-6 shadow-inner">
                <h3 className="text-2xl font-bold mb-4 terminal-text text-center">
                  How It Works
                </h3>
                
                <ol className="space-y-6">
                  <li className="flex gap-4">
                    <div className="bg-yellow-400 text-gray-900 w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center font-bold">1</div>
                    <div>
                      <p className="font-medium">Choose your subscription plan</p>
                      <p className="text-sm text-gray-600">Select from Standard, Premium, or Gold</p>
                    </div>
                  </li>
                  
                  <li className="flex gap-4">
                    <div className="bg-yellow-400 text-gray-900 w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center font-bold">2</div>
                    <div>
                      <p className="font-medium">Select your preferred local payment method</p>
                      <p className="text-sm text-gray-600">E-Dinar, postal payment, bank transfer, or D17</p>
                    </div>
                  </li>
                  
                  <li className="flex gap-4">
                    <div className="bg-yellow-400 text-gray-900 w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center font-bold">3</div>
                    <div>
                      <p className="font-medium">Complete your payment</p>
                      <p className="text-sm text-gray-600">Follow the instructions for your chosen method</p>
                    </div>
                  </li>
                  
                  <li className="flex gap-4">
                    <div className="bg-yellow-400 text-gray-900 w-8 h-8 rounded-full flex-shrink-0 flex items-center justify-center font-bold">4</div>
                    <div>
                      <p className="font-medium">Get instant access to premium tools</p>
                      <p className="text-sm text-gray-600">No waiting - start using tools immediately</p>
                    </div>
                  </li>
                </ol>
                
                <div className="mt-8 text-center">
                  <p className="text-sm text-gray-500 italic">
                    "Finally, a service that understands Tunisian payment challenges!"
                  </p>
                </div>
              </div>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-12 h-12 bg-yellow-400 rounded-full opacity-70"></div>
            <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-pink-400 rounded-full opacity-70"></div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default LocalPaymentSection;
