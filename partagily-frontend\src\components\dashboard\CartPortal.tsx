'use client';

import React, { useRef, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { X, ShoppingCart, Trash2, CreditCard } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface CartItem {
  id: string;
  type: string;
  itemId: string;
  name: string;
  price: number;
}

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
  cartItems: CartItem[];
  onRemoveItem: (itemId: string) => void;
  onCheckout: () => void;
  isLoading?: boolean;
}

const CartPortal: React.FC<CartProps> = ({
  isOpen,
  onClose,
  cartItems,
  onRemoveItem,
  onCheckout,
  isLoading = false
}) => {
  const cartRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery('(max-width: 768px)');
  const totalAmount = cartItems.reduce((total, item) => total + item.price, 0);
  const [mounted, setMounted] = useState(false);

  // Set mounted state to true after component mounts
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Prevent background scrolling when cart is open
  useEffect(() => {
    if (!isOpen) return;
    
    // Lock body scroll
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';
    
    return () => {
      // Restore scroll
      document.body.style.overflow = '';
      document.documentElement.style.overflow = '';
    };
  }, [isOpen]);

  // Handle escape key to close cart
  useEffect(() => {
    if (!isOpen) return;
    
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  // Handle click outside to close cart
  useEffect(() => {
    if (!isOpen || !cartRef.current) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (cartRef.current && !cartRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    window.addEventListener('mousedown', handleClickOutside);
    return () => window.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // Don't render anything on the server or if not mounted
  if (!mounted || !isOpen) return null;

  // Create portal content
  const cartContent = (
    <div 
      className="fixed inset-0 z-[9999] bg-black bg-opacity-50 backdrop-blur-sm"
      style={{ 
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: 9999,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        backdropFilter: 'blur(4px)'
      }}
    >
      {/* Cart drawer */}
      <div 
        ref={cartRef}
        className={`fixed ${
          isMobile
            ? 'bottom-0 left-0 right-0 h-[80vh] rounded-t-xl'
            : 'top-0 right-0 h-full w-[360px]'
        } bg-white dark:bg-slate-800 shadow-2xl flex flex-col border-l border-gray-200 dark:border-gray-700`}
        style={{
          zIndex: 10000,
          boxShadow: isMobile
            ? '0 -4px 20px rgba(0, 0, 0, 0.15)'
            : '-4px 0 20px rgba(0, 0, 0, 0.15)'
        }}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-slate-900">
          {isMobile && (
            <div className="absolute top-1.5 left-0 right-0 flex justify-center">
              <div className="w-12 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full" />
            </div>
          )}
          <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
            <ShoppingCart size={20} className="mr-2 text-yellow-500" />
            Your Cart
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-800"
            aria-label="Close cart"
          >
            <X size={18} />
          </button>
        </div>

        {/* Cart content */}
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-slate-800">
          {cartItems.length === 0 ? (
            <div className="text-center py-12 bg-white dark:bg-slate-800/50 rounded-lg shadow-sm p-8">
              <div className="mb-4 flex justify-center">
                <div className="w-16 h-16 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                  <ShoppingCart size={32} className="text-gray-400 dark:text-gray-500" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Your cart is empty
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                Add tools or plans to get started
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {cartItems.map((item) => (
                <div
                  key={item.id}
                  className="flex items-start justify-between bg-white dark:bg-slate-700/30 p-4 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start">
                    <div className="w-10 h-10 rounded-lg bg-gray-200 dark:bg-slate-600 flex items-center justify-center mr-3 overflow-hidden">
                      <div className="text-lg">
                        {item.type === 'TOOL' ? '🔧' : '📦'}
                      </div>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {item.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {item.type === 'TOOL' ? 'Individual Tool' : 'Subscription Plan'}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className="font-medium text-gray-900 dark:text-white mb-2">
                      ${item.price.toFixed(2)}
                    </span>
                    <button
                      onClick={() => onRemoveItem(item.id)}
                      className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors p-1 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20"
                      aria-label="Remove item"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900 rounded-b-lg">
          <div className="mb-6 space-y-3 bg-gray-50 dark:bg-slate-800/50 p-4 rounded-lg">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
              <span className="text-gray-900 dark:text-white">${totalAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Tax</span>
              <span className="text-gray-900 dark:text-white">$0.00</span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-1 flex justify-between">
              <span className="font-bold text-gray-900 dark:text-white">Total</span>
              <span className="font-bold text-xl text-gray-900 dark:text-white">${totalAmount.toFixed(2)}</span>
            </div>
          </div>

          <div className="space-y-3">
            <button
              onClick={onCheckout}
              disabled={cartItems.length === 0 || isLoading}
              className="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard size={18} className="mr-2" />
                  Pay Securely with Konnect
                </>
              )}
            </button>

            <Link
              href="/checkout"
              className="w-full bg-white dark:bg-slate-700 hover:bg-gray-100 dark:hover:bg-slate-600 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-all duration-200"
            >
              View Full Cart
            </Link>
          </div>

          <div className="mt-6 flex justify-center items-center">
            <div className="bg-white dark:bg-slate-800 p-2 rounded-lg shadow-sm">
              <Image
                src="https://s3.eu-west-3.amazonaws.com/konnect.network.public/logo_konnect_23a791d66b.svg"
                alt="Konnect Payment"
                width={120}
                height={36}
                className="h-8 w-auto"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render using portal to ensure it's at the root level
  return createPortal(cartContent, document.body);
};

export default CartPortal;
