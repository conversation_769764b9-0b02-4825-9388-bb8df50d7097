export interface Tool {
  id: string;
  name: string;
  description: string;
  status: string;
  category: string;
  websiteUrl: string;
  logoUrl: string;
  requiredPlan: string;
  plans?: ToolPlan[];
}

export interface ToolPlan {
  id: string;
  name: string;
  price: number;
  duration: string;
  isPopular: boolean;
}

export interface Cookie {
  name: string;
  value: string;
  domain?: string;
  path?: string;
  secure?: boolean;
  httpOnly?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  expiresAt?: string;
}

export interface ToolCookies {
  toolId: string;
  name: string;
  url: string;
  cookies: <PERSON>ie[];
}

// Add this to the global Window interface
declare global {
  interface Window {
    partagilyExtension?: {
      getAccess: (toolId: string, toolUrl: string) => void;
      isInstalled: () => boolean;
    };
  }
}
