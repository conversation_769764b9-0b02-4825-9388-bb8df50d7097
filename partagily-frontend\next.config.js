/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript type checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // images: {
  //   domains: [
  //     "localhost",
  //     process.env.NEXT_PUBLIC_API_URL,
  //     "partagily.com",
  //     "app.partagily.com",
  //     "www.partagily.com",
  //     "cdn4.iconfinder.com",
  //     "upload.wikimedia.org",
  //     "cdn.artgrid.io",
  //     "encrypted-tbn0.gstatic.com",
  //     "logo.clearbit.com",
  //     "freepnglogo.com",
  //     "images.seeklogo.com",
  //     "webshoptiger.com",
  //     "www.closerscopy.com",
  //     "10web.io",
  //     "eleven-public-cdn.elevenlabs.io",
  //     "creatorwala.in",
  //     "cdn.zbaseglobal.com",
  //     "cdn.worldvectorlogo.com",
  //     "images.sftcdn.net",
  //     "kb.helium10.com",
  //     "goodies.icons8.com",
  //     "imageupscaler.com",
  //     "assets.wheelhouse.com",
  //     "svgmix.com",
  //     "leonardo.ai",
  //     "i0.wp.com",
  //     "assets.nflxext.com",
  //     "s3.eu-west-3.amazonaws.com",
  //   ],
  // },
  // experimental: {
  //   serverActions: {
  //     allowedOrigins: [process.env.NEXT_PUBLIC_API_URL],
  //   },
  // },
  // async rewrites() {
  //   return [
  //     {
  //       source: "/api/:path*",
  //       destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
  //     },
  //     // Add a specific rewrite for auth endpoints
  //     {
  //       source: "/auth/:path*",
  //       destination: `${process.env.NEXT_PUBLIC_API_URL}/api/auth/:path*`,
  //     },
  //     // Add a specific rewrite for users endpoints
  //     {
  //       source: "/users/:path*",
  //       destination: `${process.env.NEXT_PUBLIC_API_URL}/users/:path*`,
  //     },
  //   ];
  // },
};

module.exports = nextConfig;
