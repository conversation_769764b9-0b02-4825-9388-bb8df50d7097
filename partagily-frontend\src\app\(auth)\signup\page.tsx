'use client';

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { useNotification } from "@/contexts/NotificationContext";
import { motion } from "framer-motion";
import PasswordInput from "@/components/common/PasswordInput";

export default function SignUp() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    passwordConfirm: ""
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    passwordConfirm: ""
  });

  const [touched, setTouched] = useState({
    name: false,
    email: false,
    password: false,
    passwordConfirm: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const { register, isAuthenticated } = useAuth();
  const { showNotification } = useNotification();
  const router = useRouter();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, router]);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear errors when user types
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // Handle input blur (for validation)
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    validateField(name as keyof typeof formData);
  };

  // Validate a single field
  const validateField = (fieldName: keyof typeof formData) => {
    let error = "";

    switch (fieldName) {
      case "name":
        if (!formData.name.trim()) {
          error = "Name is required";
        } else if (formData.name.trim().length < 2) {
          error = "Name must be at least 2 characters";
        }
        break;

      case "email":
        if (!formData.email) {
          error = "Email is required";
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          error = "Please enter a valid email address";
        }
        break;

      case "password":
        if (!formData.password) {
          error = "Password is required";
        } else if (formData.password.length < 8) {
          error = "Password must be at least 8 characters";
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9!@#$%^&*])/.test(formData.password)) {
          error = "Password must include uppercase, lowercase, and a number or special character";
        }
        break;

      case "passwordConfirm":
        if (!formData.passwordConfirm) {
          error = "Please confirm your password";
        } else if (formData.password !== formData.passwordConfirm) {
          error = "Passwords do not match";
        }
        break;
    }

    setErrors(prev => ({ ...prev, [fieldName]: error }));
    return !error;
  };

  // Validate all fields
  const validateForm = () => {
    const nameValid = validateField("name");
    const emailValid = validateField("email");
    const passwordValid = validateField("password");
    const passwordConfirmValid = validateField("passwordConfirm");

    return nameValid && emailValid && passwordValid && passwordConfirmValid;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Touch all fields to show validation errors
    setTouched({
      name: true,
      email: true,
      password: true,
      passwordConfirm: true
    });

    // Validate all fields
    if (!validateForm()) {
      // Find the first error to display in notification
      const firstError = Object.values(errors).find(error => error) || "Please fix the errors in the form";
      showNotification("error", firstError);
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if the email is one of our mock users first
      if (
        formData.email === "<EMAIL>" ||
        formData.email === "<EMAIL>"
      ) {
        const errorMsg =
          "This email is already registered. Please use a different email or try logging in.";
        showNotification("error", errorMsg);
        setIsSubmitting(false);
        return;
      }

      await register(
        formData.name,
        formData.email,
        formData.password,
        formData.passwordConfirm
      );

      // If we get here, registration was successful
      // Reset form
      setFormData({
        name: "",
        email: "",
        password: "",
        passwordConfirm: ""
      });

      setTouched({
        name: false,
        email: false,
        password: false,
        passwordConfirm: false
      });

      // Success notification will be shown, but we don't need to do it here
      // as the AuthContext will handle the redirect to the dashboard
      console.log("Registration successful, redirecting to dashboard...");

    } catch (error: any) {
      console.error("Registration error:", error);

      // Handle specific error cases
      let errorMsg = "";
      if (error.message && error.message.includes("Email already in use")) {
        errorMsg =
          "This email is already registered. Please use a different email or try logging in.";
      } else if (
        error.message &&
        error.message.includes("Passwords do not match")
      ) {
        errorMsg = "Passwords do not match. Please try again.";
      } else {
        errorMsg = error.message || "Registration failed. Please try again.";
      }

      showNotification("error", errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="auth-container">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="auth-card"
      >
        <div className="px-6 py-8 sm:p-10">
          <div className="auth-header">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="auth-title">
                Join <span className="auth-highlight">Partagily</span> 🚀
              </h2>
            </motion.div>
          </div>



          <form className="space-y-8" onSubmit={handleSubmit}>
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text"
              >
                Full Name
              </label>
              <div className="mt-1">
                <input
                  id="name"
                  name="name"
                  type="text"
                  autoComplete="name"
                  required
                  value={formData.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`auth-input ${
                    touched.name && errors.name ? "border-red-500" : ""
                  }`}
                />
                {touched.name && errors.name && (
                  <p className="mt-1 text-xs text-red-500">{errors.name}</p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text"
              >
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className={`auth-input ${
                    touched.email && errors.email ? "border-red-500" : ""
                  }`}
                />
                {touched.email && errors.email && (
                  <p className="mt-1 text-xs text-red-500">{errors.email}</p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text"
              >
                Password
              </label>
              <div className="mt-1">
                <PasswordInput
                  id="password"
                  value={formData.password}
                  onChange={(e) => {
                    const event = {
                      target: {
                        name: "password",
                        value: e.target.value
                      }
                    } as React.ChangeEvent<HTMLInputElement>;
                    handleChange(event);
                  }}
                  autoComplete="new-password"
                  minLength={8}
                  className={touched.password && errors.password ? "border-red-500" : ""}
                  error={touched.password && errors.password ? errors.password : ""}
                />
                {!errors.password && (
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Password must be at least 8 characters with uppercase,
                    lowercase, and a number or special character
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="passwordConfirm"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text"
              >
                Confirm Password
              </label>
              <div className="mt-1">
                <PasswordInput
                  id="passwordConfirm"
                  value={formData.passwordConfirm}
                  onChange={(e) => {
                    const event = {
                      target: {
                        name: "passwordConfirm",
                        value: e.target.value
                      }
                    } as React.ChangeEvent<HTMLInputElement>;
                    handleChange(event);
                  }}
                  autoComplete="new-password"
                  className={touched.passwordConfirm && errors.passwordConfirm ? "border-red-500" : ""}
                  error={touched.passwordConfirm && errors.passwordConfirm ? errors.passwordConfirm : ""}
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="auth-button"
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-900"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Creating account...
                  </span>
                ) : (
                  "Create account"
                )}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="auth-divider">
              <div className="auth-divider-line"></div>
              <div className="auth-divider-text">
                <span className="px-2 bg-white text-gray-500">
                  Already have an account?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                href="/signin"
                className="w-full flex justify-center py-3 px-4 border-2 border-yellow-400 rounded-full shadow-sm text-sm font-medium text-gray-900 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400 transition-all duration-300"
              >
                Sign in
              </Link>
            </div>
          </div>
        </div>

        <div className="auth-footer"></div>
      </motion.div>
    </div>
  );
}
