import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class InitiatePaymentDto {
  @ApiProperty({
    description: 'Optional custom success URL to redirect after payment',
    example: 'https://partagily.com/payment/success',
    required: false,
  })
  @IsString()
  @IsOptional()
  successUrl?: string;

  @ApiProperty({
    description: 'Optional custom cancel URL to redirect if payment is cancelled',
    example: 'https://partagily.com/payment/cancel',
    required: false,
  })
  @IsString()
  @IsOptional()
  cancelUrl?: string;
}
