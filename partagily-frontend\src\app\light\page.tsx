'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';

export default function LightModeRedirect() {
  const router = useRouter();
  const { setAutoTheme, toggleTheme, theme } = useTheme();

  useEffect(() => {
    // Set theme to light mode and disable auto theme
    setAutoTheme(false);

    // If current theme is dark, toggle it to light
    if (theme === 'dark') {
      toggleTheme();
    }

    // Add a small delay to ensure theme is applied before redirect
    const timer = setTimeout(() => {
      router.replace('/');
    }, 300);

    return () => clearTimeout(timer);
  }, [router, setAutoTheme, toggleTheme, theme]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <p className="text-lg">Setting light mode and redirecting to the main page...</p>
    </div>
  );
}
