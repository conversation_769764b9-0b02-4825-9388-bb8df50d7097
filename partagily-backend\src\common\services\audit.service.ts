import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Audit event types
 */
export enum AuditEventType {
  // Authentication events
  AUTH_LOGIN_SUCCESS = 'auth.login.success',
  AUTH_LOGIN_FAILURE = 'auth.login.failure',
  AUTH_LOGOUT = 'auth.logout',
  AUTH_REGISTER = 'auth.register',
  AUTH_PASSWORD_RESET_REQUEST = 'auth.password.reset.request',
  AUTH_PASSWORD_RESET = 'auth.password.reset',
  AUTH_TOKEN_REFRESH = 'auth.token.refresh',

  // Payment events
  PAYMENT_CREATED = 'payment.created',
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_REFUNDED = 'payment.refunded',

  // Admin events
  ADMIN_USER_CREATE = 'admin.user.create',
  ADMIN_USER_UPDATE = 'admin.user.update',
  ADMIN_USER_DELETE = 'admin.user.delete',
  ADMIN_TOOL_CREATE = 'admin.tool.create',
  ADMIN_TOOL_UPDATE = 'admin.tool.update',
  ADMIN_TOOL_DELETE = 'admin.tool.delete',
  ADMIN_SUBSCRIPTION_UPDATE = 'admin.subscription.update',
  ADMIN_SECURITY_BLOCK_IP = 'admin.security.block.ip',
  ADMIN_SECURITY_UNBLOCK_IP = 'admin.security.unblock.ip',

  // Cookie events
  COOKIE_INJECTION = 'cookie.injection',
  COOKIE_ROTATION = 'cookie.rotation',
  COOKIE_CLEAR = 'cookie.clear',
  COOKIE_STORAGE = 'cookie.storage',
  COOKIE_RETRIEVAL = 'cookie.retrieval',

  // System events
  SYSTEM_ERROR = 'system.error',
  SYSTEM_WARNING = 'system.warning',
}

/**
 * Audit event severity
 */
export enum AuditEventSeverity {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
}

/**
 * Audit event interface
 */
export interface AuditEvent {
  type: AuditEventType;
  userId?: number | string;
  ipAddress?: string;
  userAgent?: string;
  severity: AuditEventSeverity;
  details: Record<string, any>;
}

/**
 * Audit Service
 *
 * This service provides methods to log audit events for security-sensitive actions.
 */
@Injectable()
export class AuditService {
  private readonly enableAuditLogging: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.enableAuditLogging = this.configService.get<boolean>('ENABLE_AUDIT_LOGGING', true);
  }

  /**
   * Log an audit event
   * @param event The audit event to log
   */
  async log(event: AuditEvent): Promise<void> {
    if (!this.enableAuditLogging) {
      return;
    }

    try {
      // Sanitize details to prevent circular references
      const sanitizedDetails = this.sanitizeDetails(event.details);

      // Create audit log entry
      await this.prisma.auditLog.create({
        data: {
          eventType: event.type,
          userId: event.userId ? String(event.userId) : null,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          severity: event.severity,
          details: sanitizedDetails,
        },
      });
    } catch (error) {
      // Log to console if database logging fails
      console.error('Failed to create audit log:', error);
      console.error('Audit event:', {
        type: event.type,
        userId: event.userId,
        ipAddress: event.ipAddress,
        severity: event.severity,
        details: event.details,
      });
    }
  }

  /**
   * Log an authentication event
   * @param type The event type
   * @param userId The user ID
   * @param ipAddress The IP address
   * @param userAgent The user agent
   * @param details Additional details
   */
  async logAuthEvent(
    type: AuditEventType,
    userId: number | string | null,
    ipAddress: string,
    userAgent: string,
    details: Record<string, any> = {},
  ): Promise<void> {
    const severity = type.includes('.failure')
      ? AuditEventSeverity.WARNING
      : AuditEventSeverity.INFO;

    await this.log({
      type,
      userId,
      ipAddress,
      userAgent,
      severity,
      details,
    });
  }

  /**
   * Log a payment event
   * @param type The event type
   * @param userId The user ID
   * @param ipAddress The IP address
   * @param details Additional details
   */
  async logPaymentEvent(
    type: AuditEventType,
    userId: number | string,
    ipAddress: string,
    details: Record<string, any> = {},
  ): Promise<void> {
    const severity = type === AuditEventType.PAYMENT_FAILED
      ? AuditEventSeverity.WARNING
      : AuditEventSeverity.INFO;

    await this.log({
      type,
      userId,
      ipAddress,
      severity,
      details,
    });
  }

  /**
   * Log an admin event
   * @param type The event type
   * @param adminId The admin user ID
   * @param ipAddress The IP address
   * @param details Additional details
   */
  async logAdminEvent(
    type: AuditEventType,
    adminId: number | string,
    ipAddress: string,
    details: Record<string, any> = {},
  ): Promise<void> {
    await this.log({
      type,
      userId: adminId,
      ipAddress,
      severity: AuditEventSeverity.INFO,
      details,
    });
  }

  /**
   * Log a cookie event
   * @param type The event type
   * @param userId The user ID
   * @param details Additional details
   */
  async logCookieEvent(
    type: AuditEventType,
    userId: number | string,
    details: Record<string, any> = {},
  ): Promise<void> {
    await this.log({
      type,
      userId,
      severity: AuditEventSeverity.INFO,
      details,
    });
  }

  /**
   * Log a system event
   * @param type The event type
   * @param details Additional details
   */
  async logSystemEvent(
    type: AuditEventType,
    details: Record<string, any> = {},
  ): Promise<void> {
    const severity = type === AuditEventType.SYSTEM_ERROR
      ? AuditEventSeverity.ERROR
      : AuditEventSeverity.WARNING;

    await this.log({
      type,
      severity,
      details,
    });
  }

  /**
   * Get audit logs with filtering
   * @param filters Filters for the audit logs
   * @returns Filtered audit logs
   */
  async getAuditLogs(filters: any): Promise<any> {
    const {
      eventType,
      userId,
      ipAddress,
      severity,
      startDate,
      endDate,
      page = 1,
      limit = 10,
    } = filters;

    // Build the where clause
    const where: any = {};

    if (eventType) {
      where.eventType = eventType;
    }

    if (userId) {
      where.userId = userId;
    }

    if (ipAddress) {
      where.ipAddress = ipAddress;
    }

    if (severity) {
      where.severity = severity;
    }

    // Date filtering
    if (startDate || endDate) {
      where.createdAt = {};

      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }

      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const totalCount = await this.prisma.auditLog.count({ where });

    // Get logs
    const logs = await this.prisma.auditLog.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Parse details JSON
    const parsedLogs = logs.map(log => ({
      ...log,
      details: this.parseDetails(log.details),
    }));

    return {
      data: parsedLogs,
      meta: {
        totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  /**
   * Get audit log by ID
   * @param id The audit log ID
   * @returns The audit log
   */
  async getAuditLogById(id: string): Promise<any> {
    const log = await this.prisma.auditLog.findUnique({
      where: { id },
    });

    if (!log) {
      return null;
    }

    return {
      ...log,
      details: this.parseDetails(log.details),
    };
  }

  /**
   * Parse details JSON
   * @param details The details JSON string
   * @returns Parsed details
   */
  private parseDetails(details: string): Record<string, any> {
    try {
      return JSON.parse(details);
    } catch (error) {
      return { error: 'Failed to parse details' };
    }
  }

  /**
   * Sanitize details to prevent circular references and sensitive data
   * @param details The details to sanitize
   * @returns Sanitized details
   */
  private sanitizeDetails(details: Record<string, any>): string {
    try {
      // Remove sensitive fields
      const sanitized = { ...details };

      // Remove sensitive fields
      const sensitiveFields = ['password', 'passwordConfirm', 'token', 'accessToken', 'refreshToken', 'secret'];
      for (const field of sensitiveFields) {
        if (sanitized[field]) {
          sanitized[field] = '[REDACTED]';
        }
      }

      return JSON.stringify(sanitized);
    } catch (error) {
      return JSON.stringify({ error: 'Failed to serialize details' });
    }
  }
}
