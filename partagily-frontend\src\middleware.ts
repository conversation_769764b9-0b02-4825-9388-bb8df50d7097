import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  console.log(`Middleware: Processing request for path: ${path}`);

  // Define public paths that don't require authentication
  const isPublicPath =
    path === '/' ||
    path === '/signin' ||
    path === '/signup' ||
    path === '/forgot-password' ||
    path === '/reset-password' ||
    path.startsWith('/api/') ||
    path.startsWith('/_next/') ||
    path.includes('.') || // Static files like images, css, etc.
    path.startsWith('/favicon');

  // Check if user is authenticated by looking for the token in cookies
  // Check both accessToken and refreshToken
  const accessToken = request.cookies.get('accessToken')?.value;
  const refreshToken = request.cookies.get('refreshToken')?.value;

  // Also check localStorage/sessionStorage tokens via a custom header
  // This header would be set by client-side JavaScript
  const clientSideToken = request.headers.get('x-auth-token');

  // User is authenticated if any token exists
  const isAuthenticated = !!(accessToken || refreshToken || clientSideToken);

  console.log(`Middleware: Authentication status: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
  console.log(`Middleware: Token sources - Cookie: ${!!accessToken}, RefreshToken: ${!!refreshToken}, ClientSide: ${!!clientSideToken}`);

  // If the path is dashboard or admin and user is not authenticated, redirect to signin
  if ((path.startsWith('/dashboard') || path.startsWith('/admin')) && !isAuthenticated) {
    console.log('Middleware: Redirecting unauthenticated user from protected route to signin');
    return NextResponse.redirect(new URL('/signin', request.url));
  }

  // If the path is signin or signup and user is authenticated, redirect to dashboard
  if ((path === '/signin' || path === '/signup') && isAuthenticated) {
    console.log('Middleware: Redirecting authenticated user from auth page to dashboard');
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Otherwise, continue with the request
  console.log('Middleware: Allowing request to proceed');
  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
