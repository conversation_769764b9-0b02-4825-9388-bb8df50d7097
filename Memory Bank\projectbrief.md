# Partagily Project Brief

## Core Requirements
- Shared account platform with Chrome extension
- Enable users to access premium tools through subscription plans
- Cookie injection mechanism for account sharing
- Multiple subscription tiers (Standard, Premium, Gold)
- User authentication and account management
- Tool catalog with availability status
- Secure cookie handling and encryption
- Responsive web interface
- Admin dashboard for managing accounts and subscriptions
