'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';

export default function InlineTestPage() {
  return (
    <div style={{ minHeight: '100vh', paddingTop: '80px', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        style={{
          maxWidth: '400px',
          width: '100%',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          overflow: 'hidden',
          margin: '0 16px'
        }}
      >
        <div style={{ padding: '24px' }}>
          <h1 style={{
            fontSize: '30px',
            fontWeight: 'bold',
            marginBottom: '24px',
            fontFamily: 'var(--font-geist-sans), Inter, sans-serif',
            letterSpacing: '-0.02em'
          }}>
            Inline <span style={{ color: '#facc15' }}>Test</span> 🧪
          </h1>

          <p style={{ marginBottom: '24px', color: '#4b5563' }}>
            This is a test page with inline styles to check if our styles are being applied correctly.
          </p>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <button style={{
              backgroundColor: '#facc15',
              color: '#111111',
              padding: '12px 16px',
              borderRadius: '9999px',
              fontWeight: '500',
              border: 'none',
              cursor: 'pointer',
              transition: 'all 0.3s',
              width: '100%'
            }}>
              Primary Button
            </button>

            <button style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '12px 16px',
              borderRadius: '9999px',
              fontWeight: '500',
              border: 'none',
              cursor: 'pointer',
              transition: 'all 0.3s',
              width: '100%'
            }}>
              Secondary Button
            </button>

            <button style={{
              backgroundColor: 'transparent',
              color: '#111111',
              padding: '12px 16px',
              borderRadius: '9999px',
              fontWeight: '500',
              border: '2px solid #facc15',
              cursor: 'pointer',
              transition: 'all 0.3s',
              width: '100%'
            }}>
              Outline Button
            </button>
          </div>

          <div style={{ marginTop: '32px', display: 'flex', justifyContent: 'center' }}>
            <Link href="/" style={{ color: '#facc15', fontWeight: '500', transition: 'color 0.3s' }}>
              Back to Home
            </Link>
          </div>
        </div>

        <div style={{
          height: '8px',
          background: 'linear-gradient(to right, #facc15, #f472b6, #3b82f6)'
        }}></div>
      </motion.div>
    </div>
  );
}
