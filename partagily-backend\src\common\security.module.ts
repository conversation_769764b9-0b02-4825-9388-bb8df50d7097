import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RateLimitService } from './services/rate-limit.service';
import { AuditService } from './services/audit.service';
import { RateLimitMiddleware } from './middleware/rate-limit.middleware';
import { CsrfMiddleware } from './middleware/csrf.middleware';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * Security Module
 *
 * This module provides security features like CSRF protection and rate limiting.
 */
@Module({
  imports: [ConfigModule, PrismaModule],
  providers: [
    RateLimitService,
    AuditService,
    RateLimitMiddleware,
    CsrfMiddleware,
  ],
  exports: [
    RateLimitService,
    AuditService,
    RateLimitMiddleware,
    CsrfMiddleware,
  ],
})
export class SecurityModule {}
