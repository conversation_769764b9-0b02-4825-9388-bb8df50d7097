// Utility functions for encryption and decryption of sensitive data

/**
 * Generates a random encryption key
 * @returns {Promise<ArrayBuffer>} The generated key
 */
async function generateEncryptionKey() {
  return crypto.subtle.generateKey(
    {
      name: 'AES-GCM',
      length: 256
    },
    true,
    ['encrypt', 'decrypt']
  );
}

/**
 * Exports a key to raw format for storage
 * @param {CryptoKey} key - The key to export
 * @returns {Promise<string>} Base64 encoded key
 */
async function exportKey(key) {
  const exported = await crypto.subtle.exportKey('raw', key);
  return arrayBufferToBase64(exported);
}

/**
 * Imports a key from raw format
 * @param {string} keyData - Base64 encoded key
 * @returns {Promise<CryptoKey>} The imported key
 */
async function importKey(keyData) {
  const keyBuffer = base64ToArrayBuffer(keyData);
  return crypto.subtle.importKey(
    'raw',
    keyBuffer,
    {
      name: 'AES-GCM',
      length: 256
    },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Encrypts data using AES-GCM
 * @param {string} data - Data to encrypt
 * @param {CryptoKey} key - Encryption key
 * @returns {Promise<string>} Encrypted data as base64 string
 */
async function encryptData(data, key) {
  // Generate a random IV for each encryption
  const iv = crypto.getRandomValues(new Uint8Array(12));
  
  // Convert data to ArrayBuffer
  const encoder = new TextEncoder();
  const dataBuffer = encoder.encode(data);
  
  // Encrypt the data
  const encryptedBuffer = await crypto.subtle.encrypt(
    {
      name: 'AES-GCM',
      iv
    },
    key,
    dataBuffer
  );
  
  // Combine IV and encrypted data
  const result = new Uint8Array(iv.length + encryptedBuffer.byteLength);
  result.set(iv);
  result.set(new Uint8Array(encryptedBuffer), iv.length);
  
  // Convert to base64 for storage
  return arrayBufferToBase64(result);
}

/**
 * Decrypts data using AES-GCM
 * @param {string} encryptedData - Base64 encoded encrypted data
 * @param {CryptoKey} key - Decryption key
 * @returns {Promise<string>} Decrypted data
 */
async function decryptData(encryptedData, key) {
  // Convert from base64
  const encryptedBuffer = base64ToArrayBuffer(encryptedData);
  
  // Extract IV (first 12 bytes)
  const iv = encryptedBuffer.slice(0, 12);
  
  // Extract encrypted data (everything after IV)
  const dataBuffer = encryptedBuffer.slice(12);
  
  // Decrypt the data
  const decryptedBuffer = await crypto.subtle.decrypt(
    {
      name: 'AES-GCM',
      iv
    },
    key,
    dataBuffer
  );
  
  // Convert ArrayBuffer to string
  const decoder = new TextDecoder();
  return decoder.decode(decryptedBuffer);
}

/**
 * Converts ArrayBuffer to Base64 string
 * @param {ArrayBuffer} buffer - The buffer to convert
 * @returns {string} Base64 encoded string
 */
function arrayBufferToBase64(buffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Converts Base64 string to ArrayBuffer
 * @param {string} base64 - Base64 encoded string
 * @returns {ArrayBuffer} The converted buffer
 */
function base64ToArrayBuffer(base64) {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Initializes encryption key, generating a new one if needed
 * @returns {Promise<CryptoKey>} The encryption key
 */
async function initializeEncryptionKey() {
  // Check if we already have a key in storage
  const data = await chrome.storage.local.get('encryptionKey');
  
  if (data.encryptionKey) {
    // Use existing key
    return importKey(data.encryptionKey);
  } else {
    // Generate and store a new key
    const key = await generateEncryptionKey();
    const exportedKey = await exportKey(key);
    await chrome.storage.local.set({ encryptionKey: exportedKey });
    return key;
  }
}

export {
  initializeEncryptionKey,
  encryptData,
  decryptData
};
