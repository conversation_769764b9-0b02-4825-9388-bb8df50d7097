import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CookieService } from '../services/cookie.service';
import { CreateCookiesDto, GetCookiesResponseDto } from '../dto/cookie.dto';

@ApiTags('tools/cookies')
@Controller('tools')
export class CookieController {
  constructor(private readonly cookieService: CookieService) {}

  @Post(':id/cookies')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Store cookies for a tool' })
  @ApiResponse({ status: 201, description: 'Cookies stored successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async storeCookies(
    @Param('id') toolId: string,
    @Body() createCookiesDto: CreateCookiesDto,
    @Request() req,
  ) {
    // Override toolId from path parameter
    createCookiesDto.toolId = toolId;

    await this.cookieService.storeCookies(createCookiesDto, req.user.id);
    return { success: true, message: 'Cookies stored successfully' };
  }

  @Get(':id/cookies')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get cookies for a tool' })
  @ApiResponse({
    status: 200,
    description: 'Cookies retrieved successfully',
    type: GetCookiesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async getCookies(
    @Param('id') toolId: string,
    @Request() req,
  ) {
    try {
      // Get user ID from request
      const userId = req.user?.id || 'development-user-id';

      // Try to get cookies from the database
      const cookies = await this.cookieService.getCookies(toolId, userId);

      // If we have cookies, return them
      if (cookies && cookies.length > 0) {
        return { success: true, cookies };
      }

      // If no cookies in database, use the addNetflixCookies method for Netflix
      if (toolId === '520e0f4b-1273-475e-b2f8-2de932eebfd8' || toolId.toLowerCase().includes('netflix')) {
        await this.cookieService.addNetflixCookies(userId);
        const cookies = await this.cookieService.getCookies(toolId, userId);
        return { success: true, cookies };
      }

      // For other tools, return empty cookies array
      return { success: true, cookies: [] };
    } catch (error) {
      console.error('Error getting cookies:', error);
      return {
        success: false,
        message: error.message || 'Error getting cookies',
        cookies: []
      };
    }
  }

  @Delete(':id/cookies')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete cookies for a tool' })
  @ApiResponse({ status: 200, description: 'Cookies deleted successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async deleteCookies(
    @Param('id') toolId: string,
    @Request() req,
  ) {
    await this.cookieService.deleteCookies(toolId, req.user.id);
    return { success: true, message: 'Cookies deleted successfully' };
  }

  @Post('netflix-cookies')
  // @UseGuards(JwtAuthGuard) // Disabled for testing
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add Netflix cookies to the database' })
  @ApiResponse({ status: 201, description: 'Netflix cookies added successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async addNetflixCookies(@Request() req) {
    // Use a dummy user ID for testing
    await this.cookieService.addNetflixCookies('test-user-id');
    return { success: true, message: 'Netflix cookies added successfully' };
  }
}
