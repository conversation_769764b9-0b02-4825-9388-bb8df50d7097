// This is a client component that handles localStorage persistence for tools
'use client';

import { useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Sample tool data for initial setup
const sampleTools = [
  {
    id: uuidv4(),
    name: 'Netflix',
    description: 'Stream TV shows, movies, and more.',
    websiteUrl: 'https://netflix.com',
    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/08/Netflix_2015_logo.svg/1920px-Netflix_2015_logo.svg.png',
    category: 'streaming',
    isActive: true,
    plans: [
      {
        name: 'Basic',
        price: 9.99,
        duration: '1 month',
        features: ['Standard Definition', 'Watch on 1 device at a time'],
        isPopular: false,
        isActive: true,
      },
      {
        name: 'Standard',
        price: 15.49,
        duration: '1 month',
        features: ['Full HD', 'Watch on 2 devices at a time'],
        isPopular: true,
        isActive: true,
      },
      {
        name: 'Premium',
        price: 19.99,
        duration: '1 month',
        features: ['Ultra HD', 'Watch on 4 devices at a time'],
        isPopular: false,
        isActive: true,
      },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: uuidv4(),
    name: 'Spotify',
    description: 'Digital music streaming service with millions of songs.',
    websiteUrl: 'https://spotify.com',
    logoUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/2/26/Spotify_logo_with_text.svg/1920px-Spotify_logo_with_text.svg.png',
    category: 'streaming',
    isActive: true,
    plans: [
      {
        name: 'Individual',
        price: 9.99,
        duration: '1 month',
        features: ['Ad-free music', 'Download to listen offline', 'Play anywhere'],
        isPopular: true,
        isActive: true,
      },
      {
        name: 'Duo',
        price: 12.99,
        duration: '1 month',
        features: ['2 Premium accounts', 'Ad-free music', 'Download to listen offline'],
        isPopular: false,
        isActive: true,
      },
      {
        name: 'Family',
        price: 15.99,
        duration: '1 month',
        features: ['6 Premium accounts', 'Block explicit music', 'Ad-free music'],
        isPopular: false,
        isActive: true,
      },
    ],
    createdAt: new Date(Date.now() - ********).toISOString(), // 1 day ago
    updatedAt: new Date(Date.now() - ********).toISOString(),
  },
];

export default function ToolsLocalStorage() {
  useEffect(() => {
    // Initialize localStorage with sample tools if it doesn't exist
    const storedTools = localStorage.getItem('partagily-tools');
    if (!storedTools) {
      localStorage.setItem('partagily-tools', JSON.stringify(sampleTools));
      console.log('Initialized localStorage with sample tools');
    }

    // Sync tools with the server
    const syncTools = async () => {
      try {
        const tools = localStorage.getItem('partagily-tools');
        if (tools) {
          await fetch('/api/admin/tools/sync', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ tools: JSON.parse(tools) }),
          });
          console.log('Tools synced with server');
        }
      } catch (error) {
        console.error('Error syncing tools with server:', error);
      }
    };

    syncTools();
  }, []);

  return null; // This component doesn't render anything
}
