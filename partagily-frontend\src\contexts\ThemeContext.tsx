'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  isAutoTheme: boolean;
  setAutoTheme: (auto: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('light');
  const [isAutoTheme, setAutoTheme] = useState<boolean>(true);

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    // Check if auto theme setting is stored
    const storedAutoTheme = localStorage.getItem('autoTheme');
    const isAuto = storedAutoTheme !== null ? storedAutoTheme === 'true' : true;
    setAutoTheme(isAuto);

    if (isAuto) {
      // Use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setTheme(prefersDark ? 'dark' : 'light');
    } else {
      // Check if manual theme is stored in localStorage
      const storedTheme = localStorage.getItem('theme') as Theme | null;
      if (storedTheme) {
        setTheme(storedTheme);
      }
    }
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      if (isAutoTheme) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    // Add event listener
    mediaQuery.addEventListener('change', handleChange);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [isAutoTheme]);

  // Update HTML class when theme changes
  useEffect(() => {
    const root = document.documentElement;
    const body = document.body;

    if (theme === 'dark') {
      root.classList.add('dark');
      body.classList.add('dark-mode');
      body.classList.remove('light-mode');
      document.documentElement.style.colorScheme = 'dark';
    } else {
      root.classList.remove('dark');
      body.classList.remove('dark-mode');
      body.classList.add('light-mode');
      document.documentElement.style.colorScheme = 'light';
    }

    // Save to localStorage only if not in auto mode
    if (!isAutoTheme) {
      localStorage.setItem('theme', theme);
    }
  }, [theme, isAutoTheme]);

  // Save auto theme setting
  useEffect(() => {
    localStorage.setItem('autoTheme', isAutoTheme.toString());
  }, [isAutoTheme]);

  // Toggle between light and dark (only used when auto is off)
  const toggleTheme = () => {
    if (!isAutoTheme) {
      setTheme(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, isAutoTheme, setAutoTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

// Custom hook to use the theme context
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
