'use client';

import Link from 'next/link';
import { ReactNode } from 'react';

interface LinkComponentProps {
  href: string;
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  'aria-label'?: string;
}

/**
 * A wrapper around Next.js Link component to prevent hydration errors
 *
 * This component uses suppressHydrationWarning to prevent hydration errors
 * when the server and client render different HTML.
 */
const LinkComponent = ({
  href,
  children,
  className,
  onClick,
  'aria-label': ariaLabel,
  ...props
}: LinkComponentProps) => {
  return (
    <div suppressHydrationWarning>
      <Link
        href={href}
        className={className}
        onClick={onClick}
        aria-label={ariaLabel}
        {...props}
      >
        {children}
      </Link>
    </div>
  );
};

export default LinkComponent;
