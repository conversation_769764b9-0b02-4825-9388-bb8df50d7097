'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import paymentService from '@/services/paymentService';
import '@/styles/checkout.css';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [order, setOrder] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      // Check for Konnect payment reference
      const paymentRef = searchParams.get('payment_ref');
      const orderId = searchParams.get('orderId');

      if (paymentRef) {
        // This is a Konnect payment callback
        try {
          // Verify the payment with the backend
          const verificationResult = await paymentService.verifyKonnectPayment(paymentRef);

          if (verificationResult?.success) {
            setOrder(verificationResult.data);
          } else {
            setError('Payment verification failed. Please contact support.');
          }
        } catch (err: any) {
          setError(err.message || 'Failed to verify payment');
        } finally {
          setLoading(false);
        }
        return;
      }

      // If no payment_ref, try to get order by ID
      if (!orderId) {
        setError('Order ID not found');
        setLoading(false);
        return;
      }

      try {
        const orderData = await paymentService.getOrderById(orderId);
        setOrder(orderData);
      } catch (err: any) {
        setError(err.message || 'Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-24 flex flex-col justify-center items-center min-h-[80vh] checkout-container">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full mb-6"
        />
        <p className="text-gray-600 dark:text-gray-400 text-lg">Loading order details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
        <div className="checkout-header pt-8 mb-16">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white text-center mb-4">
            Payment Status
          </h1>
        </div>
        <div className="max-w-2xl mx-auto bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden checkout-content">
          <div className="p-12 text-center">
            <div className="flex justify-center mb-8">
              <div className="w-24 h-24 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                <svg
                  className="w-12 h-12 text-red-500 dark:text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
              </div>
            </div>
            <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-6">
              Something went wrong
            </h2>
            <p className="text-gray-600 dark:text-gray-400 text-lg text-center mb-10">{error}</p>
            <div className="flex justify-center">
              <Link
                href="/dashboard"
                className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg shadow-md hover:shadow-lg"
              >
                Go to Dashboard
              </Link>
            </div>
          </div>
        </div>
        <div className="checkout-footer"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
      <div className="checkout-header pt-8 mb-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white text-center mb-4">
          Payment Successful
        </h1>
      </div>
      <div className="max-w-2xl mx-auto bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden checkout-content">
        <div className="p-12 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center mb-8"
          >
            <div className="w-24 h-24 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-green-500 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
          </motion.div>
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-6">
            Thank You for Your Purchase!
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-lg text-center mb-10">
            Your payment has been processed successfully. You now have access to the tools and plans you purchased.
          </p>

          {order && (
            <div className="bg-gray-50 dark:bg-slate-700/30 rounded-lg p-6 mb-10 mx-auto max-w-md">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Order Details
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Order Number</span>
                  <span className="font-medium text-gray-900 dark:text-white">{order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Tool</span>
                  <span className="font-medium text-gray-900 dark:text-white">{order.toolName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Plan</span>
                  <span className="font-medium text-gray-900 dark:text-white">{order.planName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Amount</span>
                  <span className="font-medium text-gray-900 dark:text-white">{order.amount} {order.currency}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Date</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="/dashboard/subscriptions"
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg shadow-md hover:shadow-lg"
            >
              View My Subscriptions
            </Link>
            <Link
              href="/dashboard"
              className="bg-white dark:bg-slate-700 hover:bg-gray-100 dark:hover:bg-slate-600 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg"
            >
              Go to Dashboard
            </Link>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-slate-700/50 p-8 text-center">
          <p className="text-base text-gray-600 dark:text-gray-400">
            If you have any questions about your purchase, please contact our support team.
          </p>
        </div>
      </div>
      <div className="checkout-footer"></div>
    </div>
  );
}

// Loading fallback component
function LoadingFallback() {
  return (
    <div className="container mx-auto px-4 py-24 flex flex-col justify-center items-center min-h-[80vh] checkout-container">
      <motion.div
        animate={{
          rotate: 360,
        }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear',
        }}
        className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full mb-6"
      />
      <p className="text-gray-600 dark:text-gray-400 text-lg">Loading payment details...</p>
    </div>
  );
}

// Export the main page component with Suspense boundary
export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <PaymentSuccessContent />
    </Suspense>
  );
}
