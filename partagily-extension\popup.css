* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  width: 350px;
  min-height: 400px;
}

.container {
  padding: 16px;
  background-color: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

h1 {
  color: #1e88e5;
  font-size: 24px;
  margin-bottom: 5px;
}

.subtitle {
  color: #666;
  font-size: 14px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #333;
}

input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.btn {
  display: block;
  width: 100%;
  padding: 10px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #1e88e5;
  color: white;
}

.btn-primary:hover {
  background-color: #1976d2;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #1e88e5;
  color: #1e88e5;
  margin-top: 15px;
}

.btn-outline:hover {
  background-color: #f0f7ff;
}

.text-center {
  text-align: center;
}

.mt-3 {
  margin-top: 12px;
}

a {
  color: #1e88e5;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.user-info {
  background-color: #f0f7ff;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.badge {
  background-color: #1e88e5;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
}

.tools-section {
  flex: 1;
}

h2 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #333;
}

.tools-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
}

.tool-card {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tool-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.tool-name {
  font-size: 12px;
  font-weight: 500;
}

footer {
  margin-top: auto;
  text-align: center;
  font-size: 12px;
  color: #999;
  padding-top: 15px;
  border-top: 1px solid #eee;
}
