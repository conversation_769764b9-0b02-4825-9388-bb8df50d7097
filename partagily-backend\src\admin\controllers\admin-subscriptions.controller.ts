import { Controller, Get, Post, Put, Patch, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Admin Subscriptions Controller
 * 
 * This controller provides endpoints to manage subscriptions.
 */
@ApiTags('admin-subscriptions')
@Controller('admin/subscriptions')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminSubscriptionsController {
  constructor(private readonly prisma: PrismaService) {}
  
  /**
   * Get all subscriptions with filtering and pagination
   */
  @Get()
  @ApiOperation({ summary: 'Get all subscriptions' })
  @ApiResponse({ status: 200, description: 'List of subscriptions' })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'planId', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async getAllSubscriptions(
    @Query('userId') userId?: string,
    @Query('planId') planId?: string,
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
  ) {
    try {
      // Build filter conditions
      const where: any = {};
      
      if (userId) {
        where.userId = userId;
      }
      
      if (planId) {
        where.planId = planId;
      }
      
      if (status) {
        where.status = status;
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Build sort options
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;
      
      // Get subscriptions with pagination
      const subscriptions = await this.prisma.subscription.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      });
      
      // Get total count for pagination
      const total = await this.prisma.subscription.count({ where });
      
      return {
        data: subscriptions,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      throw error;
    }
  }
  
  /**
   * Get a subscription by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a subscription by ID' })
  @ApiResponse({ status: 200, description: 'Subscription details' })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async getSubscriptionById(@Param('id') id: string) {
    try {
      const subscription = await this.prisma.subscription.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      });
      
      if (!subscription) {
        return {
          success: false,
          message: 'Subscription not found',
        };
      }
      
      return {
        success: true,
        data: subscription,
      };
    } catch (error) {
      console.error('Error fetching subscription:', error);
      throw error;
    }
  }
  
  /**
   * Create a new subscription
   */
  @Post()
  @ApiOperation({ summary: 'Create a new subscription' })
  @ApiResponse({ status: 201, description: 'Subscription created successfully' })
  async createSubscription(@Body() subscriptionData: any) {
    try {
      const { userId, planId, startDate, endDate, status, autoRenew } = subscriptionData;
      
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });
      
      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      // Check if plan exists
      const plan = await this.prisma.plan.findUnique({
        where: { id: planId },
      });
      
      if (!plan) {
        return {
          success: false,
          message: 'Plan not found',
        };
      }
      
      // Create subscription
      const newSubscription = await this.prisma.subscription.create({
        data: {
          userId,
          planId,
          startDate: startDate ? new Date(startDate) : new Date(),
          endDate: new Date(endDate),
          status: status || 'ACTIVE',
          autoRenew: autoRenew !== undefined ? autoRenew : true,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      });
      
      return {
        success: true,
        message: 'Subscription created successfully',
        data: newSubscription,
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }
  
  /**
   * Update a subscription
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a subscription' })
  @ApiResponse({ status: 200, description: 'Subscription updated successfully' })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async updateSubscription(@Param('id') id: string, @Body() subscriptionData: any) {
    try {
      // Check if subscription exists
      const existingSubscription = await this.prisma.subscription.findUnique({
        where: { id },
      });
      
      if (!existingSubscription) {
        return {
          success: false,
          message: 'Subscription not found',
        };
      }
      
      // Prepare update data
      const updateData: any = {};
      
      if (subscriptionData.userId !== undefined) {
        // Check if user exists
        const user = await this.prisma.user.findUnique({
          where: { id: subscriptionData.userId },
        });
        
        if (!user) {
          return {
            success: false,
            message: 'User not found',
          };
        }
        
        updateData.userId = subscriptionData.userId;
      }
      
      if (subscriptionData.planId !== undefined) {
        // Check if plan exists
        const plan = await this.prisma.plan.findUnique({
          where: { id: subscriptionData.planId },
        });
        
        if (!plan) {
          return {
            success: false,
            message: 'Plan not found',
          };
        }
        
        updateData.planId = subscriptionData.planId;
      }
      
      if (subscriptionData.startDate !== undefined) {
        updateData.startDate = new Date(subscriptionData.startDate);
      }
      
      if (subscriptionData.endDate !== undefined) {
        updateData.endDate = new Date(subscriptionData.endDate);
      }
      
      if (subscriptionData.status !== undefined) {
        updateData.status = subscriptionData.status;
      }
      
      if (subscriptionData.autoRenew !== undefined) {
        updateData.autoRenew = subscriptionData.autoRenew;
      }
      
      // Update subscription
      const updatedSubscription = await this.prisma.subscription.update({
        where: { id },
        data: updateData,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
      });
      
      return {
        success: true,
        message: 'Subscription updated successfully',
        data: updatedSubscription,
      };
    } catch (error) {
      console.error('Error updating subscription:', error);
      throw error;
    }
  }
  
  /**
   * Delete a subscription
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a subscription' })
  @ApiResponse({ status: 200, description: 'Subscription deleted successfully' })
  @ApiResponse({ status: 404, description: 'Subscription not found' })
  async deleteSubscription(@Param('id') id: string) {
    try {
      // Check if subscription exists
      const existingSubscription = await this.prisma.subscription.findUnique({
        where: { id },
      });
      
      if (!existingSubscription) {
        return {
          success: false,
          message: 'Subscription not found',
        };
      }
      
      // Delete subscription
      await this.prisma.subscription.delete({
        where: { id },
      });
      
      return {
        success: true,
        message: 'Subscription deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting subscription:', error);
      throw error;
    }
  }
  
  /**
   * Get expiring soon subscriptions
   */
  @Get('expiring/soon')
  @ApiOperation({ summary: 'Get expiring soon subscriptions' })
  @ApiResponse({ status: 200, description: 'List of expiring soon subscriptions' })
  async getExpiringSoonSubscriptions() {
    try {
      // Get subscriptions expiring in the next 7 days
      const expiringSubscriptions = await this.prisma.subscription.findMany({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
            lte: new Date(new Date().setDate(new Date().getDate() + 7)),
          },
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: true,
        },
        orderBy: {
          endDate: 'asc',
        },
      });
      
      return {
        success: true,
        data: expiringSubscriptions,
      };
    } catch (error) {
      console.error('Error fetching expiring subscriptions:', error);
      throw error;
    }
  }
  
  /**
   * Get subscription statistics
   */
  @Get('stats/overview')
  @ApiOperation({ summary: 'Get subscription statistics' })
  @ApiResponse({ status: 200, description: 'Subscription statistics' })
  async getSubscriptionStats() {
    try {
      // Get total subscriptions
      const totalSubscriptions = await this.prisma.subscription.count();
      
      // Get active subscriptions
      const activeSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
        },
      });
      
      // Get expired subscriptions
      const expiredSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'EXPIRED',
        },
      });
      
      // Get cancelled subscriptions
      const cancelledSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'CANCELLED',
        },
      });
      
      // Get pending subscriptions
      const pendingSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'PENDING',
        },
      });
      
      // Get new subscriptions (last 30 days)
      const newSubscriptions = await this.prisma.subscription.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });
      
      // Get expiring soon subscriptions (next 7 days)
      const expiringSoonSubscriptions = await this.prisma.subscription.count({
        where: {
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
            lte: new Date(new Date().setDate(new Date().getDate() + 7)),
          },
        },
      });
      
      // Get auto-renew enabled subscriptions
      const autoRenewSubscriptions = await this.prisma.subscription.count({
        where: {
          autoRenew: true,
        },
      });
      
      // Get subscriptions by plan
      const plans = await this.prisma.plan.findMany();
      
      const subscriptionsByPlan = await Promise.all(
        plans.map(async (plan) => {
          const count = await this.prisma.subscription.count({
            where: {
              planId: plan.id,
            },
          });
          
          return {
            planId: plan.id,
            planName: plan.name,
            count,
          };
        })
      );
      
      // Calculate growth rate
      const previousMonthSubscriptions = await this.prisma.subscription.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setDate(new Date().getDate() - 60)),
            lt: new Date(new Date().setDate(new Date().getDate() - 30)),
          },
        },
      });
      
      const growthRate = previousMonthSubscriptions > 0 
        ? ((newSubscriptions - previousMonthSubscriptions) / previousMonthSubscriptions) * 100 
        : 0;
      
      return {
        totalSubscriptions,
        activeSubscriptions,
        expiredSubscriptions,
        cancelledSubscriptions,
        pendingSubscriptions,
        newSubscriptions,
        expiringSoonSubscriptions,
        autoRenewSubscriptions,
        subscriptionsByPlan,
        growthRate,
      };
    } catch (error) {
      console.error('Error fetching subscription stats:', error);
      throw error;
    }
  }
}
