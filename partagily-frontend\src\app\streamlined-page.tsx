'use client';

import HeroSection from "@/components/home/<USER>";
import EnhancedHowItWorksSection from "@/components/home/<USER>";
import PricingSection from "@/components/home/<USER>";
import ProblemSection from "@/components/home/<USER>";
import SimplifiedPaymentSection from "@/components/home/<USER>";
import StickyCTA from "@/components/home/<USER>";

export default function StreamlinedHome() {
  return (
    <main className="pt-44"> {/* Increased padding for banner (80px) + navbar (90px) + extra space */}
      <HeroSection />
      <ProblemSection />
      <EnhancedHowItWorksSection />
      <SimplifiedPaymentSection />
      <PricingSection />
      <StickyCTA /> {/* Mobile sticky CTA */}
    </main>
  );
}
