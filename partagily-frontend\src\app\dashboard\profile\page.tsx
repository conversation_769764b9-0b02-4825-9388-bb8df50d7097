'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import authService from '@/services/authService';
import userService from '@/services/userService';
import { useNotification } from '@/contexts/NotificationContext';
import PasswordInput from '@/components/common/PasswordInput';

export default function ProfileSettings() {
  const { user, updateProfile, changePassword } = useAuth();
  const router = useRouter();
  const { showNotification } = useNotification();

  // Check if user is authenticated and load user data
  useEffect(() => {
    const checkUser = async () => {
      // If no user in context, try to get from localStorage
      if (!user) {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const storedUser = JSON.parse(userStr);
            if (storedUser && storedUser.email) {
              console.log('Found user in localStorage:', storedUser);
              // We have a user in localStorage, but not in context
              // This is fine for the profile page, so we'll just use the localStorage data
              return;
            }
          } catch (e) {
            console.error('Error parsing user from localStorage:', e);
          }
        }

        // If we get here, we couldn't find a user in localStorage either
        console.warn('No user found, redirecting to signin');
        router.push('/signin');
      } else {
        console.log('User from context:', user);
      }
    };

    checkUser();
  }, [user, router]);

  // Initialize state with user data
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');

  // Update state when user data changes
  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setEmail(user.email || '');
    }
  }, [user]);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [language, setLanguage] = useState('en');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage({ type: '', text: '' });

    try {
      // Only update if values have changed
      const updates: { name?: string; email?: string } = {};

      if (name !== user?.name) {
        updates.name = name;
      }

      if (email !== user?.email) {
        updates.email = email;
      }

      // If nothing has changed, don't make the API call
      if (Object.keys(updates).length === 0) {
        setMessage({ type: 'success', text: 'No changes to save.' });
        showNotification('info', 'No changes to save.', { autoClose: true });
        setIsSubmitting(false);
        return;
      }

      console.log('Updating profile with data:', updates);

      // Call the userService to update the profile
      const response = await userService.updateProfile(updates);

      console.log('Profile update response:', response);

      if (response.success && response.user) {
        // Update the user data in the auth context if available
        if (typeof updateProfile === 'function') {
          updateProfile(response.user);
        }

        // Show success message
        setMessage({
          type: 'success',
          text: response.message || 'Profile updated successfully!'
        });

        // Show notification
        showNotification('success', 'Profile updated successfully!', {
          autoClose: true
        });
      } else {
        throw new Error(response.message || 'Failed to update profile');
      }
    } catch (error: any) {
      console.error('Error updating profile:', error);

      // Handle specific error cases
      if (error.message?.includes('Email already in use')) {
        setMessage({
          type: 'error',
          text: 'This email is already in use. Please use a different email.'
        });

        showNotification('error', 'This email is already in use. Please use a different email.', {
          autoClose: true
        });
      } else {
        setMessage({
          type: 'error',
          text: error.message || 'Failed to update profile. Please try again.'
        });

        showNotification('error', error.message || 'Failed to update profile. Please try again.', {
          autoClose: true
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setMessage({
        type: 'error',
        text: 'New passwords do not match.'
      });
      showNotification('error', 'New passwords do not match.', { autoClose: true });
      return;
    }

    // Validate password length
    if (newPassword.length < 8) {
      setMessage({
        type: 'error',
        text: 'New password must be at least 8 characters long.'
      });
      showNotification('error', 'New password must be at least 8 characters long.', { autoClose: true });
      return;
    }

    setIsSubmitting(true);
    setMessage({ type: '', text: '' });

    try {
      // Check if user exists
      if (!user || !user.email) {
        throw new Error('User not found. Please log in again.');
      }

      console.log('Changing password for user:', user.email);

      // Call the userService to change the password
      const response = await userService.changePassword({
        currentPassword,
        newPassword,
        confirmPassword
      });

      console.log('Password change response:', response);

      // Clear password fields on success
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Show success message
      setMessage({
        type: 'success',
        text: response.message || 'Password changed successfully!'
      });

      // Show notification
      showNotification('success', 'Password changed successfully!', {
        autoClose: true
      });
    } catch (error: any) {
      console.error('Error changing password:', error);

      // Handle different error types
      const errorMessage = error.message || 'Failed to change password. Please try again.';

      if (errorMessage.includes('Current password is incorrect')) {
        setMessage({
          type: 'error',
          text: 'Current password is incorrect. Please try again.'
        });

        showNotification('error', 'Current password is incorrect. Please try again.', {
          autoClose: true
        });

        // Only clear the current password field
        setCurrentPassword('');
      }
      else if (errorMessage.includes('User not found')) {
        setMessage({
          type: 'error',
          text: 'User not found. Please log in again.'
        });

        // Show a notification with a login button
        showNotification('error', 'User not found. Please log in again.', {
          autoClose: false,
          action: {
            label: 'Login',
            onClick: () => router.push('/signin')
          }
        });
      }
      else if (errorMessage.includes('Network Error') ||
               errorMessage.includes('connect to the server')) {
        setMessage({
          type: 'error',
          text: 'Unable to connect to the server. Please check your internet connection and try again.'
        });

        showNotification('error', 'Unable to connect to the server. Please check your internet connection and try again.', {
          autoClose: true
        });
      }
      else {
        // For other errors
        setMessage({
          type: 'error',
          text: errorMessage
        });

        showNotification('error', errorMessage, {
          autoClose: true
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAccountDelete = async () => {
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));

      // For mock users, handle deletion
      if (user) {
        // For built-in mock users, don't allow deletion
        if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
          // Show error for built-in mock users
          setMessage({
            type: 'error',
            text: 'Demo accounts cannot be deleted.'
          });
          setShowDeleteConfirm(false);
          setIsSubmitting(false);
          return;
        }

        // For registered users, remove from localStorage
        const registeredUsersStr = localStorage.getItem('registeredUsers');
        if (registeredUsersStr) {
          try {
            const registeredUsers = JSON.parse(registeredUsersStr);
            if (Array.isArray(registeredUsers)) {
              // Filter out the current user
              const updatedUsers = registeredUsers.filter(u =>
                !u || !u.email || u.email.toLowerCase() !== user.email?.toLowerCase()
              );

              localStorage.setItem('registeredUsers', JSON.stringify(updatedUsers));
            }
          } catch (e) {
            console.error('Error removing user from localStorage:', e);
          }
        }

        // Clear auth tokens
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        sessionStorage.removeItem('accessToken');
        sessionStorage.removeItem('refreshToken');
      }

      // Show success message
      setMessage({
        type: 'success',
        text: 'Account deleted successfully. Redirecting...'
      });

      // Show notification
      showNotification('success', 'Account deleted successfully. Redirecting to home page...', {
        autoClose: true
      });

      // Redirect to home page after account deletion
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    } catch (error) {
      setMessage({
        type: 'error',
        text: 'Failed to delete account. Please try again.'
      });
      setShowDeleteConfirm(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header with gradient background */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-6 text-white shadow-lg">
          <h2 className="text-2xl font-bold mb-2">
            ⚙️ Profile Settings
          </h2>
          <p className="mb-4">
            Manage your account information and preferences
          </p>
        </div>
      </div>

      {message.text && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`mb-6 p-4 rounded-lg ${
            message.type === 'success'
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400'
          }`}
        >
          {message.type === 'success' ? '✅ ' : '❌ '}
          {message.text}
        </motion.div>
      )}

      {/* Personal Information Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden mb-8"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 p-2 rounded-lg mr-3">👤</span>
            Personal Information
          </h3>
          <form onSubmit={handleProfileUpdate}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Full Name
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  required
                />
              </div>
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-2 px-6 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </button>
          </form>
        </div>
      </motion.div>

      {/* Password Change Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden mb-8"
      >
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 p-2 rounded-lg mr-3">🔒</span>
            Change Password
          </h3>
          <form onSubmit={handlePasswordChange}>
            <div className="space-y-4 mb-6">
              <div>
                <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Current Password
                </label>
                <PasswordInput
                  id="currentPassword"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  required
                />
              </div>
              <div>
                <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  New Password
                </label>
                <PasswordInput
                  id="newPassword"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  minLength={8}
                  autoComplete="new-password"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Password must be at least 8 characters long
                </p>
              </div>
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Confirm New Password
                </label>
                <PasswordInput
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  autoComplete="new-password"
                />
              </div>
            </div>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-2 px-6 rounded-lg transition-colors duration-200 disabled:opacity-50"
            >
              {isSubmitting ? 'Changing...' : 'Change Password'}
            </button>
          </form>
        </div>
      </motion.div>

      {/* Language Preferences Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden mb-8"
      >
        <div className="p-6">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <span className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 p-2 rounded-lg mr-3">🌐</span>
            Language Preferences
          </h3>
          <div className="mb-6">
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Display Language
            </label>
            <select
              id="language"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            >
              <option value="en">🇬🇧 English</option>
              <option value="fr">🇫🇷 French</option>
              <option value="ar">🇹🇳 Arabic</option>
            </select>
          </div>
          <button
            type="button"
            className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Save Preferences
          </button>
        </div>
      </motion.div>
    </div>
  );
}
