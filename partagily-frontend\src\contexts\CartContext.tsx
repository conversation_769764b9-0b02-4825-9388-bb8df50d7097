'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import userService from '@/services/userService';
import { useNotification } from './NotificationContext';
import { useAuth } from './AuthContext';
import { isValidUUID, convertToUUID } from '@/utils/validation';

export interface CartItem {
  id: string;
  type: string;
  itemId: string;
  name: string;
  price: number;
  icon?: string;
}

interface CartContextType {
  cartItems: CartItem[];
  cartItemCount: number;
  totalAmount: number;
  isCartOpen: boolean;
  isLoading: boolean;
  openCart: () => void;
  closeCart: () => void;
  addToCart: (itemId: string, itemType: 'TOOL' | 'PLAN') => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  checkout: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { showNotification } = useNotification();
  const { isAuthenticated } = useAuth();

  // Calculate derived values
  const cartItemCount = cartItems.length;
  const totalAmount = cartItems.reduce((total, item) => total + item.price, 0);

  // Use client-side only rendering to avoid hydration issues
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch cart on initial load and when auth state changes, but only on client side
  useEffect(() => {
    if (isMounted && isAuthenticated) {
      console.log('User is authenticated, refreshing cart...');
      refreshCart();
    } else if (isMounted) {
      console.log('User is not authenticated, setting empty cart...');
      setCartItems([]);
    } else {
      setCartItems([]);
    }
  }, [isAuthenticated, isMounted]);

  // Refresh cart every 30 seconds if authenticated, but only on client side
  useEffect(() => {
    if (!isMounted || !isAuthenticated) return;

    const interval = setInterval(() => {
      refreshCart();
    }, 30000);

    return () => clearInterval(interval);
  }, [isAuthenticated, isMounted]);

  const refreshCart = async () => {
    // Only run on client side
    if (!isMounted) return;

    try {
      console.log('Refreshing cart...');

      // Check if user is authenticated
      if (!isAuthenticated) {
        console.log('User not authenticated, cannot refresh cart');
        setCartItems([]);
        return;
      }

      try {
        // Make the API call to get the cart
        const response = await userService.getCart();
        console.log('Cart API response:', response);

        if (response?.success && response?.data) {
          console.log('Cart refreshed successfully:', response.data);

          // Update the cart items
          if (Array.isArray(response.data.items)) {
            setCartItems(response.data.items);
          } else {
            console.warn('Cart items is not an array:', response.data.items);
            setCartItems([]);
          }
        } else {
          console.warn('Cart refresh returned unsuccessful response:', response);
          setCartItems([]);
        }
      } catch (err) {
        console.error('Error fetching cart:', err);

        // Don't show notification for cart fetch errors as this happens in the background
        // Just set empty cart items to avoid stale data
        setCartItems([]);
      }
    } catch (err: any) {
      console.error('Error in refreshCart:', err);
      // Don't show notification for cart refresh errors
      setCartItems([]);
    }
  };

  // Helper function to use localStorage cart as fallback
  const useLocalStorageCart = () => {
    try {
      const storedCart = localStorage.getItem('mockCart');
      if (storedCart) {
        const mockCart = JSON.parse(storedCart);
        console.log('Using cart from localStorage as fallback:', mockCart);
        setCartItems(mockCart.items || []);
      } else {
        // Create an empty cart if none exists
        const emptyCart = {
          id: '1',
          userId: '1',
          status: 'OPEN',
          items: []
        };
        localStorage.setItem('mockCart', JSON.stringify(emptyCart));
        setCartItems([]);
      }
    } catch (storageError) {
      console.warn('Failed to get cart from localStorage:', storageError);
      setCartItems([]);
    }
  };

  const openCart = () => {
    console.log('Opening cart...');
    setIsCartOpen(true);
    console.log('Cart open state set to:', true);
  };

  const closeCart = () => {
    console.log('Closing cart...');
    setIsCartOpen(false);
    console.log('Cart open state set to:', false);
  };

  const addToCart = async (itemId: string, itemType: 'TOOL' | 'PLAN') => {
    console.log('CartContext - addToCart called with:', { itemId, itemType });
    console.log('CartContext - isAuthenticated:', isAuthenticated);

    // Only proceed if we're on the client side
    if (!isMounted) {
      console.log('CartContext - Not mounted yet, ignoring addToCart call');
      return;
    }

    if (!isAuthenticated) {
      console.log('CartContext - User not authenticated, showing notification');
      showNotification('error', 'Please sign in to add items to your cart', {
        autoClose: true,
      });
      return;
    }

    try {
      setIsLoading(true);
      console.log(`Adding to cart: ${itemType} with ID ${itemId}`);

      // Ensure itemId exists
      if (!itemId) {
        console.error('Missing itemId in addToCart');
        showNotification('error', 'Item ID is required.', {
          autoClose: true,
        });
        setIsLoading(false);
        return;
      }

      // Log the original itemId for debugging
      console.log('Original itemId in CartContext:', itemId);

      // Check if item is already in cart
      const existingItem = cartItems.find(
        item => item.itemId === itemId && item.type === itemType
      );

      if (existingItem) {
        console.log('CartContext - Item already in cart, opening cart drawer');
        showNotification('info', 'This item is already in your cart', {
          autoClose: true,
        });
        openCart(); // Open the cart to show the user
        setIsLoading(false);
        return;
      }

      // Create a temporary item to add to cart immediately for better UX
      const tempItem = {
        id: `temp-${Date.now()}`,
        type: itemType,
        itemId: itemId,
        name: 'Loading...',
        price: 0,
        icon: undefined
      };

      // Add the temporary item to the cart immediately for better UX
      setCartItems(prev => [...prev, tempItem]);

      // Make the API call to add the item to the cart
      const response = await userService.addToCart({
        itemId,
        type: itemType,
      });

      console.log('Add to cart response:', response);

      // Remove the temporary item regardless of the outcome
      setCartItems(prev => prev.filter(item => item.id !== tempItem.id));

      if (response?.success && response?.data) {
        console.log('CartContext - Item added successfully, updating cart items');

        // Update the cart with the real data from the server
        setCartItems(response.data.items || []);

        // Show success notification
        showNotification('success', `${itemType === 'TOOL' ? 'Tool' : 'Plan'} added to cart`, {
          autoClose: true,
        });

        // Open the cart to show the user what they added
        console.log('CartContext - Opening cart after successful add');
        openCart();
      } else {
        throw new Error('Failed to add item to cart');
      }
    } catch (err: any) {
      console.error('Error adding to cart:', err);
      showNotification('error', err.message || 'Failed to add item to cart', {
        autoClose: true,
      });

      // Refresh the cart to ensure it's in sync with the server
      await refreshCart();
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to add item to localStorage cart
  const addToLocalStorageCart = (itemId: string, itemType: 'TOOL' | 'PLAN') => {
    try {
      // Get current cart from localStorage
      const storedCart = localStorage.getItem('mockCart');
      let currentCart = storedCart ? JSON.parse(storedCart) : { id: '1', userId: '1', status: 'OPEN', items: [] };

      // Use default values without DOM manipulation
      let name = itemType === 'TOOL' ? 'Tool' : 'Plan';
      let price = itemType === 'TOOL' ? 9.99 : 19.99;
      let icon = undefined;

      // Create a mock item
      const mockItem = {
        id: Math.random().toString(36).substring(2, 15),
        type: itemType,
        itemId: itemId,
        name: name,
        price: price,
        icon: icon
      };

      // Add the item to the cart
      currentCart.items = [...(currentCart.items || []), mockItem];

      // Save the cart to localStorage
      localStorage.setItem('mockCart', JSON.stringify(currentCart));

      // Update the cart items
      setCartItems(currentCart.items || []);

      // Show success notification
      showNotification('success', `${itemType === 'TOOL' ? 'Tool' : 'Plan'} added to cart (offline mode)`, {
        autoClose: true,
      });

      // Open the cart
      openCart();
    } catch (fallbackError) {
      console.error('Error using localStorage fallback:', fallbackError);
      showNotification('error', 'Failed to add item to cart', {
        autoClose: true,
      });
    }
  };

  const removeFromCart = async (itemId: string) => {
    // Only proceed if we're on the client side
    if (!isMounted) {
      console.log('CartContext - Not mounted yet, ignoring removeFromCart call');
      return;
    }

    try {
      setIsLoading(true);
      console.log(`Removing item from cart: ${itemId}`);

      // Optimistically remove the item from the UI for better UX
      const originalCartItems = [...cartItems];
      const itemToRemove = cartItems.find(item => item.id === itemId);

      if (!itemToRemove) {
        console.log('Item not found in cart');
        return;
      }

      // Temporarily remove the item from the UI
      setCartItems(cartItems.filter(item => item.id !== itemId));

      try {
        // Make the API call to remove the item
        const response = await userService.removeFromCart(itemId);

        if (response?.success && response?.data) {
          // Update with the server response to ensure consistency
          setCartItems(response.data.items || []);
          showNotification('success', 'Item removed from cart', {
            autoClose: true,
          });
        } else {
          throw new Error('Failed to remove item from cart');
        }
      } catch (err) {
        console.error('Error removing item from cart:', err);

        // Restore the original cart items if the API call fails
        setCartItems(originalCartItems);

        showNotification('error', err.message || 'Failed to remove item from cart', {
          autoClose: true,
        });

        // Refresh the cart to ensure it's in sync with the server
        await refreshCart();
      }
    } catch (err: any) {
      console.error('Error in removeFromCart:', err);
      showNotification('error', err.message || 'Failed to remove item from cart', {
        autoClose: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to remove item from localStorage cart
  const removeFromLocalStorageCart = (itemId: string) => {
    try {
      // Get current cart from localStorage
      const storedCart = localStorage.getItem('mockCart');
      if (!storedCart) {
        console.warn('No cart found in localStorage');
        return;
      }

      const currentCart = JSON.parse(storedCart);

      // Remove the item from the cart
      const updatedItems = currentCart.items.filter((item: any) => item.id !== itemId);

      // Update the cart
      const updatedCart = {
        ...currentCart,
        items: updatedItems
      };

      // Save the cart to localStorage
      localStorage.setItem('mockCart', JSON.stringify(updatedCart));

      // Update the cart items
      setCartItems(updatedItems || []);

      // Show success notification
      showNotification('success', 'Item removed from cart (offline mode)', {
        autoClose: true,
      });
    } catch (fallbackError) {
      console.error('Error using localStorage fallback:', fallbackError);
      showNotification('error', 'Failed to remove item from cart', {
        autoClose: true,
      });
    }
  };

  const checkout = async () => {
    // Only proceed if we're on the client side
    if (!isMounted) {
      console.log('CartContext - Not mounted yet, ignoring checkout call');
      return { success: false, message: 'Not mounted yet' };
    }

    try {
      setIsLoading(true);
      console.log('Starting checkout in CartContext...');

      // Check if cart is empty
      if (cartItems.length === 0) {
        showNotification('error', 'Your cart is empty. Please add items before checkout.', {
          autoClose: true,
        });
        return { success: false, message: 'Cart is empty' };
      }

      // Store original cart items in case we need to restore them
      const originalCartItems = [...cartItems];

      try {
        // Make the API call to checkout
        const response = await userService.checkout();
        console.log('Checkout response in CartContext:', response);

        if (response?.success) {
          // Check if there's a payment URL to redirect to
          if (response.paymentUrl || response.data?.paymentUrl) {
            const paymentUrl = response.paymentUrl || response.data?.paymentUrl;
            console.log('Redirecting to payment URL:', paymentUrl);

            // Close the cart before redirecting
            closeCart();

            // Show notification
            showNotification('info', 'Redirecting to payment gateway...', {
              autoClose: true,
            });

            // Redirect to the payment URL
            window.location.href = paymentUrl;
            return response;
          }

          // If no payment URL, proceed with normal checkout flow
          // If the response includes a cart, update the cart items
          if (response.cart && response.cart.items) {
            setCartItems(response.cart.items || []);
          } else {
            // Otherwise, just clear the cart
            setCartItems([]);
          }

          // Close the cart drawer
          closeCart();

          // Show success notification
          showNotification('success', 'Checkout successful', {
            autoClose: true,
          });

          return response;
        } else {
          throw new Error(response?.message || 'Checkout failed');
        }
      } catch (err) {
        console.error('Error during checkout:', err);

        // Show error notification
        showNotification('error', err.message || 'Failed to complete checkout', {
          autoClose: true,
        });

        // Refresh the cart to ensure it's in sync with the server
        await refreshCart();

        throw err;
      }
    } catch (err: any) {
      console.error('Error in checkout:', err);
      showNotification('error', err.message || 'Failed to complete checkout', {
        autoClose: true,
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <CartContext.Provider
      value={{
        cartItems,
        cartItemCount,
        totalAmount,
        isCartOpen,
        isLoading,
        openCart,
        closeCart,
        addToCart,
        removeFromCart,
        checkout,
        refreshCart,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
