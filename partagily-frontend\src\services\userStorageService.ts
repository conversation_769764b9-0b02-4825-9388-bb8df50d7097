/**
 * User Storage Service
 *
 * This service handles all user data storage and retrieval.
 * It provides a single source of truth for user data.
 */

// User interface
export interface StoredUser {
  id: string;
  email: string;
  name: string;
  role: string;
  createdAt?: string;
  updatedAt?: string;
  // Password is no longer stored in the frontend
}

// Get all registered users
export const getRegisteredUsers = (): StoredUser[] => {
  try {
    const usersStr = localStorage.getItem('registeredUsers');
    if (!usersStr) return [];

    const users = JSON.parse(usersStr);
    if (!Array.isArray(users)) return [];

    return users;
  } catch (e) {
    console.error('Error getting registered users:', e);
    return [];
  }
};

// Save registered users
export const saveRegisteredUsers = (users: StoredUser[]): void => {
  try {
    localStorage.setItem('registeredUsers', JSON.stringify(users));
  } catch (e) {
    console.error('Error saving registered users:', e);
  }
};

// Find user by email
export const findUserByEmail = (email: string): StoredUser | null => {
  const users = getRegisteredUsers();
  const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
  return user || null;
};

// Update user password
// This is now just a stub that logs the action
// Real password updates should happen via the backend API
export const updateUserPassword = (email: string, newPassword: string): boolean => {
  try {
    console.log('Local password update is deprecated');
    console.log('Passwords should be updated via the backend API');

    // For development/testing purposes only
    console.log(`Password update requested for user: ${email}`);

    // Update the user's updatedAt timestamp
    const users = getRegisteredUsers();
    const userIndex = users.findIndex(u => u.email.toLowerCase() === email.toLowerCase());

    if (userIndex !== -1) {
      users[userIndex].updatedAt = new Date().toISOString();
      saveRegisteredUsers(users);

      // Also update current user in localStorage if it exists
      const currentUser = getCurrentUser();
      if (currentUser && currentUser.email.toLowerCase() === email.toLowerCase()) {
        currentUser.updatedAt = new Date().toISOString();
        saveCurrentUser(currentUser);
      }

      console.log('User updated timestamp for:', email);
    }

    return true;
  } catch (e) {
    console.error('Error in updateUserPassword:', e);
    return false;
  }
};

// Add new user
export const addUser = (user: StoredUser): boolean => {
  try {
    const users = getRegisteredUsers();

    // Check if user already exists
    const existingUser = users.find(u => u.email.toLowerCase() === user.email.toLowerCase());
    if (existingUser) {
      console.log('User already exists, updating:', user.email);

      // Update the existing user with new information
      const userIndex = users.findIndex(u => u.email.toLowerCase() === user.email.toLowerCase());
      users[userIndex] = {
        ...existingUser,
        ...user,
        updatedAt: new Date().toISOString()
      };

      // Save the updated users
      saveRegisteredUsers(users);

      console.log('User updated:', user.email);
      return true;
    }

    // Add the user
    users.push({
      ...user,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // Save the updated users
    saveRegisteredUsers(users);

    console.log('User added:', user.email);

    return true;
  } catch (e) {
    console.error('Error adding user:', e);
    return false;
  }
};

// Verify user credentials
// This is now just a stub that returns a user object based on email
// Real verification happens in the backend via the login API
export const verifyUserCredentials = (email: string, password: string): StoredUser | null => {
  try {
    console.log('Local credential verification is deprecated');
    console.log('Credentials should be verified by the backend API');

    // For development/testing purposes only
    // In production, this should always return null to force API usage

    // Special case for built-in users (for development only)
    if (email.toLowerCase() === '<EMAIL>') {
      console.log('Returning admin user for development');
      return {
        id: 'admin-id',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin'
      };
    }

    if (email.toLowerCase() === '<EMAIL>') {
      console.log('Returning regular user for development');
      return {
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'user'
      };
    }

    // Check if we have a user with this email
    const user = findUserByEmail(email);
    if (user) {
      console.log('Found user in local storage:', user.email);
      return user;
    }

    console.log('User not found in local storage');
    return null;
  } catch (e) {
    console.error('Error in verifyUserCredentials:', e);
    return null;
  }
};

// Clear all user data
export const clearAllUserData = (): void => {
  try {
    localStorage.removeItem('registeredUsers');
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('mockUserRole');

    sessionStorage.removeItem('user');
    sessionStorage.removeItem('accessToken');
    sessionStorage.removeItem('refreshToken');
    sessionStorage.removeItem('mockUserRole');

    console.log('All user data cleared');
  } catch (e) {
    console.error('Error clearing user data:', e);
  }
};

// Get current user
export const getCurrentUser = (): StoredUser | null => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;

    return JSON.parse(userStr);
  } catch (e) {
    console.error('Error getting current user:', e);
    return null;
  }
};

// Save current user
export const saveCurrentUser = (user: StoredUser): void => {
  try {
    localStorage.setItem('user', JSON.stringify(user));
  } catch (e) {
    console.error('Error saving current user:', e);
  }
};

// Get user by email (alias for findUserByEmail for better naming)
export const getUserByEmail = (email: string): StoredUser | null => {
  return findUserByEmail(email);
};

// The userStorageService object
const userStorageService = {
  getRegisteredUsers,
  saveRegisteredUsers,
  findUserByEmail,
  getUserByEmail,
  updateUserPassword,
  addUser,
  verifyUserCredentials,
  clearAllUserData,
  getCurrentUser,
  saveCurrentUser
};

export default userStorageService;
