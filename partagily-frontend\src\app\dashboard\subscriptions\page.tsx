'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Clock, Check, AlertTriangle, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import userService from '@/services/userService';
import ToolCard from '@/components/dashboard/ToolCard';

// Define the tool type
interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  price: number;
  originalPrice?: number;
  category?: string;
  status?: string;
}

// Filter component
const ToolsFilter = ({
  activeFilter,
  setActiveFilter
}: {
  activeFilter: string,
  setActiveFilter: (filter: string) => void
}) => {
  const filters = ['All', 'Streaming', 'Productivity', 'Design', 'AI'];

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {filters.map((filter) => (
        <button
          key={filter}
          onClick={() => setActiveFilter(filter)}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
            activeFilter === filter
              ? 'bg-yellow-400 text-black shadow-sm'
              : 'bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'
          }`}
        >
          {filter}
        </button>
      ))}
    </div>
  );
};

export default function MyTools() {
  const [activeFilter, setActiveFilter] = useState('All');
  const [purchasedTools, setPurchasedTools] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPurchasedTools();
  }, []);

  const fetchPurchasedTools = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get user's order history to determine purchased tools
      const orderHistoryResponse = await userService.getOrderHistory();

      if (orderHistoryResponse?.success && orderHistoryResponse?.data) {
        // Extract all tool IDs from completed orders
        const purchasedToolIds = new Set<string>();

        orderHistoryResponse.data.forEach((order: any) => {
          if (order.status === 'COMPLETED' && order.items) {
            order.items.forEach((item: any) => {
              if (item.type === 'TOOL') {
                purchasedToolIds.add(item.itemId);
              }
            });
          }
        });

        // If there are purchased tools, fetch their details
        if (purchasedToolIds.size > 0) {
          // Get all tools
          const toolsResponse = await userService.getTools();

          if (toolsResponse?.tools) {
            // Filter to only include purchased tools
            const userPurchasedTools = toolsResponse.tools.filter((tool: Tool) =>
              purchasedToolIds.has(tool.id)
            );

            setPurchasedTools(userPurchasedTools);
          }
        } else {
          setPurchasedTools([]);
        }
      } else {
        setPurchasedTools([]);
      }
    } catch (error) {
      console.error('Error fetching purchased tools:', error);
      setError('Failed to load your tools. Please try again later.');
      setPurchasedTools([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter tools based on selected category
  const filteredTools = activeFilter === 'All'
    ? purchasedTools
    : purchasedTools.filter(tool =>
        tool.category?.toLowerCase() === activeFilter.toLowerCase()
      );

  return (
    <div>
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-6 text-white shadow-lg">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                🧰 My Tools
              </h2>
              <p className="mb-4">
                Access all the tools you've purchased. These are ready to use!
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link
                href="/dashboard"
                className="bg-white text-purple-600 hover:bg-gray-100 font-medium py-2 px-6 rounded-lg transition-colors duration-200 flex items-center"
              >
                Browse More Tools
                <ArrowRight size={16} className="ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {purchasedTools.length > 0 && (
        <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <ToolsFilter activeFilter={activeFilter} setActiveFilter={setActiveFilter} />
        </div>
      )}

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg flex items-center">
          <AlertTriangle size={20} className="mr-2" />
          <p>{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <motion.div
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: 'linear',
            }}
            className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
          />
        </div>
      ) : (
        <div>
          {filteredTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTools.map(tool => (
                <ToolCard
                  key={tool.id}
                  tool={tool}
                  isPurchased={true}
                />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-slate-800 rounded-xl p-8 text-center shadow-md">
              <div className="w-20 h-20 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <AlertTriangle size={32} className="text-gray-400 dark:text-gray-500" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">No tools found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {activeFilter === 'All'
                  ? "You haven't purchased any tools yet."
                  : `You don't have any ${activeFilter.toLowerCase()} tools.`}
              </p>
              <Link
                href="/dashboard"
                className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-2 px-6 rounded-lg transition-colors duration-200 inline-flex items-center"
              >
                Browse Tools
                <ArrowRight size={16} className="ml-2" />
              </Link>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
