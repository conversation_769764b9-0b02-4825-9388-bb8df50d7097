'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, Package, FileText, Globe, Image, Tag, Plus, Trash2, Star } from 'lucide-react';
import adminService from '@/services/adminService';
import mockAdminService from '@/services/mockAdminService';

export default function CreateToolPage() {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    websiteUrl: '',
    logoUrl: '',
    category: '',
    isActive: true,
    plans: [
      {
        name: 'Basic',
        price: 9.99,
        duration: '1 month',
        features: ['Basic access'],
        isPopular: false,
        isActive: true,
      },
    ],
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);

      // Create a preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // In a real app, you would upload this to a server and get a URL
      // For now, we'll just use a mock URL
      setFormData({
        ...formData,
        logoUrl: URL.createObjectURL(file),
      });
    }
  };

  const handlePlanChange = (index: number, field: string, value: any) => {
    const updatedPlans = [...formData.plans];
    updatedPlans[index] = {
      ...updatedPlans[index],
      [field]: value,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const handlePlanFeatureChange = (planIndex: number, featureIndex: number, value: string) => {
    const updatedPlans = [...formData.plans];
    const features = [...updatedPlans[planIndex].features];
    features[featureIndex] = value;
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const addPlanFeature = (planIndex: number) => {
    const updatedPlans = [...formData.plans];
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features: [...updatedPlans[planIndex].features, ''],
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const removePlanFeature = (planIndex: number, featureIndex: number) => {
    const updatedPlans = [...formData.plans];
    const features = [...updatedPlans[planIndex].features];
    features.splice(featureIndex, 1);
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const addPlan = () => {
    setFormData({
      ...formData,
      plans: [
        ...formData.plans,
        {
          name: '',
          price: 0,
          duration: '1 month',
          features: [''],
          isPopular: false,
          isActive: true,
        },
      ],
    });
  };

  const removePlan = (index: number) => {
    const updatedPlans = [...formData.plans];
    updatedPlans.splice(index, 1);
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage(null);

      // In a real app, you would upload the logo file to a server here
      // and get back a URL to use in the tool data

      // Use the frontend API route instead of the backend service
      console.log('Submitting tool data to frontend API route');
      const response = await fetch('/api/admin/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Failed to create tool: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Tool created successfully:', data);

      // Show success message
      setSuccessMessage('Tool created successfully!');

      // Save to localStorage manually
      try {
        // Get existing tools from localStorage
        const storedTools = localStorage.getItem('partagily-tools');
        const tools = storedTools ? JSON.parse(storedTools) : [];

        // Add the new tool
        tools.push(data.tool);

        // Save back to localStorage
        localStorage.setItem('partagily-tools', JSON.stringify(tools));
        console.log('Tool added to localStorage');
      } catch (error) {
        console.error('Error updating localStorage:', error);
      }

      // Redirect to tools list after a short delay
      setTimeout(() => {
        router.push('/admin/tools');
      }, 2000);
    } catch (err: any) {
      console.error('Error creating tool:', err);
      setError(err.message || 'Failed to create tool');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link
          href="/admin/tools"
          className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </Link>
        <h1 className="text-2xl font-bold">Create New Tool</h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">⚠️</span>
          <div>
            <p className="font-medium">Error</p>
            <p>{error}</p>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">✅</span>
          <div>
            <p className="font-medium">Success</p>
            <p>{successMessage}</p>
          </div>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Tool Name
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Package className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="e.g. Netflix, Spotify, ChatGPT"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Tag className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">Select a category</option>
                    <option value="streaming">Streaming</option>
                    <option value="productivity">Productivity</option>
                    <option value="design">Design</option>
                    <option value="ai">AI</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <div className="relative">
                  <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                    <FileText className="h-5 w-5 text-gray-400" />
                  </div>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    required
                    rows={4}
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="Describe what this tool does and why users would want it"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 mb-1">
                  Website URL
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="url"
                    id="websiteUrl"
                    name="websiteUrl"
                    value={formData.websiteUrl}
                    onChange={handleChange}
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="https://example.com"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="logoUrl" className="block text-sm font-medium text-gray-700 mb-1">
                  Logo
                </label>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0 h-16 w-16 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden">
                    {logoPreview ? (
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <Image className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <label
                      htmlFor="logo-upload"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-yellow-600 hover:text-yellow-500 focus-within:outline-none"
                    >
                      <span>Upload a file</span>
                      <input
                        id="logo-upload"
                        name="logo-upload"
                        type="file"
                        accept="image/*"
                        className="sr-only"
                        onChange={handleLogoChange}
                      />
                    </label>
                    <p className="text-xs text-gray-500">
                      PNG, JPG, GIF up to 2MB
                    </p>
                  </div>
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active Tool (visible to users)
                  </label>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Subscription Plans</h2>

              <div className="space-y-6">
                {formData.plans.map((plan, planIndex) => (
                  <div key={planIndex} className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-md font-medium text-gray-900">Plan {planIndex + 1}</h3>
                      {formData.plans.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removePlan(planIndex)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor={`plan-name-${planIndex}`} className="block text-sm font-medium text-gray-700 mb-1">
                          Plan Name
                        </label>
                        <input
                          type="text"
                          id={`plan-name-${planIndex}`}
                          value={plan.name}
                          onChange={(e) => handlePlanChange(planIndex, 'name', e.target.value)}
                          required
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                          placeholder="e.g. Basic, Premium, Pro"
                        />
                      </div>

                      <div>
                        <label htmlFor={`plan-price-${planIndex}`} className="block text-sm font-medium text-gray-700 mb-1">
                          Price ($)
                        </label>
                        <input
                          type="number"
                          id={`plan-price-${planIndex}`}
                          value={plan.price}
                          onChange={(e) => handlePlanChange(planIndex, 'price', parseFloat(e.target.value))}
                          required
                          min="0"
                          step="0.01"
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                          placeholder="9.99"
                        />
                      </div>

                      <div>
                        <label htmlFor={`plan-duration-${planIndex}`} className="block text-sm font-medium text-gray-700 mb-1">
                          Duration
                        </label>
                        <select
                          id={`plan-duration-${planIndex}`}
                          value={plan.duration}
                          onChange={(e) => handlePlanChange(planIndex, 'duration', e.target.value)}
                          required
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                        >
                          <option value="1 month">1 Month</option>
                          <option value="3 months">3 Months</option>
                          <option value="6 months">6 Months</option>
                          <option value="1 year">1 Year</option>
                        </select>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`plan-popular-${planIndex}`}
                            checked={plan.isPopular}
                            onChange={(e) => handlePlanChange(planIndex, 'isPopular', e.target.checked)}
                            className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`plan-popular-${planIndex}`} className="ml-2 block text-sm text-gray-700">
                            Popular
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`plan-active-${planIndex}`}
                            checked={plan.isActive}
                            onChange={(e) => handlePlanChange(planIndex, 'isActive', e.target.checked)}
                            className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`plan-active-${planIndex}`} className="ml-2 block text-sm text-gray-700">
                            Active
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Features
                      </label>
                      <div className="space-y-2">
                        {plan.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={feature}
                              onChange={(e) => handlePlanFeatureChange(planIndex, featureIndex, e.target.value)}
                              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                              placeholder="e.g. Unlimited access"
                            />
                            {plan.features.length > 1 && (
                              <button
                                type="button"
                                onClick={() => removePlanFeature(planIndex, featureIndex)}
                                className="text-red-600 hover:text-red-800"
                              >
                                <Trash2 className="w-5 h-5" />
                              </button>
                            )}
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={() => addPlanFeature(planIndex)}
                          className="inline-flex items-center text-sm text-yellow-600 hover:text-yellow-800"
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          Add Feature
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addPlan}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Add Another Plan
                </button>
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Cookie Injection Settings</h2>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-4">
                  Configure how the Chrome extension will inject cookies for this tool. This information will be used by the extension to authenticate users.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="cookieDomain" className="block text-sm font-medium text-gray-700 mb-1">
                      Cookie Domain
                    </label>
                    <input
                      type="text"
                      id="cookieDomain"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      placeholder="e.g. netflix.com"
                    />
                  </div>

                  <div>
                    <label htmlFor="cookiePath" className="block text-sm font-medium text-gray-700 mb-1">
                      Cookie Path
                    </label>
                    <input
                      type="text"
                      id="cookiePath"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      placeholder="e.g. /"
                      defaultValue="/"
                    />
                  </div>

                  <div>
                    <label htmlFor="cookieName" className="block text-sm font-medium text-gray-700 mb-1">
                      Cookie Name
                    </label>
                    <input
                      type="text"
                      id="cookieName"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      placeholder="e.g. NetflixId"
                    />
                  </div>

                  <div>
                    <label htmlFor="cookieValue" className="block text-sm font-medium text-gray-700 mb-1">
                      Cookie Value Format
                    </label>
                    <input
                      type="text"
                      id="cookieValue"
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      placeholder="e.g. {{token}}"
                      defaultValue="{{token}}"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="cookieSecure"
                        className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                        defaultChecked
                      />
                      <label htmlFor="cookieSecure" className="ml-2 block text-sm text-gray-700">
                        Secure Cookie (HTTPS only)
                      </label>
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="cookieHttpOnly"
                        className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                        defaultChecked
                      />
                      <label htmlFor="cookieHttpOnly" className="ml-2 block text-sm text-gray-700">
                        HTTP Only (not accessible via JavaScript)
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-5 h-5 border-2 border-gray-900 border-t-transparent rounded-full mr-2"
                    />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-5 h-5 mr-2" />
                    Create Tool
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
