import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { CookieDto, CreateCookiesDto } from '../dto/cookie.dto';
import { AuditService, AuditEventType } from '../../common/services/audit.service';
import { ToolCategory, ToolStatus, PlanTier } from '@prisma/client';

@Injectable()
export class CookieService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Store cookies for a tool
   */
  async storeCookies(createCookiesDto: CreateCookiesDto, userId: string): Promise<void> {
    const { toolId, cookies } = createCookiesDto;

    // Check if tool exists
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new NotFoundException(`Tool with ID ${toolId} not found`);
    }

    // Begin a transaction to ensure all cookies are stored or none
    await this.prisma.$transaction(async (prisma) => {
      // Delete existing cookies for this tool
      await prisma.cookie.deleteMany({
        where: { toolId },
      });

      // Create new cookies
      for (const cookie of cookies) {
        await prisma.cookie.create({
          data: {
            toolId,
            domain: cookie.domain,
            name: cookie.name,
            value: cookie.value,
            path: cookie.path || '/',
            expiresAt: new Date(cookie.expirationDate),
            isSecure: cookie.secure !== false,
            isHttpOnly: cookie.httpOnly || false,
            sameSite: cookie.sameSite || 'lax',
          },
        });
      }
    });

    // Log the cookie storage event
    await this.auditService.logCookieEvent(
      AuditEventType.COOKIE_STORAGE,
      userId,
      {
        toolId,
        cookieCount: cookies.length,
        toolName: tool.name,
      },
    );
  }

  /**
   * Get cookies for a tool
   */
  async getCookies(toolId: string, userId: string): Promise<CookieDto[]> {
    // Check if tool exists
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new NotFoundException(`Tool with ID ${toolId} not found`);
    }

    // Get cookies
    const cookies = await this.prisma.cookie.findMany({
      where: { toolId },
    });

    // Log the cookie retrieval event
    await this.auditService.logCookieEvent(
      AuditEventType.COOKIE_RETRIEVAL,
      userId,
      {
        toolId,
        cookieCount: cookies.length,
        toolName: tool.name,
      },
    );

    // Filter out expired cookies
    const validCookies = cookies.filter(cookie => cookie.expiresAt > new Date());

    // Map to DTO format
    return validCookies.map(cookie => ({
      name: cookie.name,
      value: cookie.value,
      domain: cookie.domain,
      path: cookie.path,
      expirationDate: cookie.expiresAt.toISOString(),
      secure: cookie.isSecure,
      httpOnly: cookie.isHttpOnly,
      sameSite: cookie.sameSite,
    }));
  }

  /**
   * Delete cookies for a tool
   */
  async deleteCookies(toolId: string, userId: string): Promise<void> {
    // Check if tool exists
    const tool = await this.prisma.tool.findUnique({
      where: { id: toolId },
    });

    if (!tool) {
      throw new NotFoundException(`Tool with ID ${toolId} not found`);
    }

    // Delete cookies
    const result = await this.prisma.cookie.deleteMany({
      where: { toolId },
    });

    // Log the cookie deletion event
    await this.auditService.logCookieEvent(
      AuditEventType.COOKIE_CLEAR,
      userId,
      {
        toolId,
        cookieCount: result.count,
        toolName: tool.name,
      },
    );
  }

  /**
   * Add Netflix cookies to the database
   */
  async addNetflixCookies(userId: string): Promise<void> {
    // Find or create Netflix tool
    let netflixTool = await this.prisma.tool.findFirst({
      where: {
        name: { contains: 'Netflix', mode: 'insensitive' },
      },
    });

    if (!netflixTool) {
      // Create Netflix tool if it doesn't exist
      netflixTool = await this.prisma.tool.create({
        data: {
          name: 'Netflix Premium',
          description: 'Access Netflix premium content with a shared account.',
          price: 5.99,
          category: ToolCategory.streaming, // Changed from entertainment
          status: ToolStatus.AVAILABLE,
          requiredPlan: PlanTier.STANDARD,
        },
      });
    }

    // Netflix cookies
    const netflixCookies = [
      {
        name: 'flwssn',
        value: '9183eb1f-c0f6-4d23-9809-90561e4d8749',
        domain: '.netflix.com',
        path: '/',
        expirationDate: '2025-04-25T19:45:40.288Z',
        secure: false,
        httpOnly: false,
        sameSite: 'unspecified',
      },
      {
        name: 'gsid',
        value: '89e23473-9660-445f-995b-8ff8bb7665aa',
        domain: '.netflix.com',
        path: '/',
        expirationDate: '2025-04-26T16:45:36.441Z',
        secure: true,
        httpOnly: true,
        sameSite: 'no_restriction',
      },
      {
        name: 'NetflixId',
        value: 'ct%3DBgjHlOvcAxLJAzqif7OkpE3euD5ThAzE5aVbG6QWShqWParCLVjeZWCR1Nxi4bd75pmkNuAi_idh5mFaVe0rgwOkKdqxl0cIP3_s9GnK2iP7IazlDWASLDdQVe9qtod-JDETXuypJTPXSglH-p1Z3J9s-7kEmvkdZkewe6zbLIzs1DN3B4A6CT7d0ZPKtPF97_MKfVCtUZ-VhqZSummLvKZR7NQFI5KwGMNrhIvF_qGp6_RF9G4-k8ZgyKz2l64O1gDtWbSyB2fuvogGtgw3Bs2Ap8Ua80Fv8YXYdnKMByGc3mpkjZKnCzqufKi0WogDsGFx4a90wwWv-tIvDvk2CTRME1d_vsyqaSQ0ot1iNMGcgbTsBi-KjeKCtjPS4zN6284IGZMh0IO0FKqhj825liAxas3y1bhQrzC--wQBART33B5Ri_RPpIixZ6jeXZ211rnxZyl-9nRYhPzKFQyF6MTi0OxAN6a3VD31m4VHaJYreL10wytkBeLkAIempsUPI2st1mM4ejkAg_Ex58Rqh3RG9x8gkzAd9QI7Ztzi-rhIt4lTpoFYknimXNMk7E3MPI-xZWvl6iXbO3DXi_EMlgAyGNblCg0pZT1WrtpbrKigoJkYBiIOCgy5Bw27fiDgO5uLCuM.%26ch%3DAQEAEAABABSOEQIKuuTe_eQeXL7j6pyVuF4Fi7iISbs.%26v%3D3',
        domain: '.netflix.com',
        path: '/',
        expirationDate: '2026-04-25T19:45:35.080Z',
        secure: true,
        httpOnly: true,
        sameSite: 'lax',
      },
      {
        name: 'SecureNetflixId',
        value: 'v%3D3%26mac%3DAQEAEQABABSdeXWyxcmjA9ORFlm8qGBhxy_vkrTpPMw.%26dt%3D1745610340903',
        domain: '.netflix.com',
        path: '/',
        expirationDate: '2026-04-25T19:45:35.080Z',
        secure: true,
        httpOnly: true,
        sameSite: 'strict',
      },
    ];

    // Store cookies
    await this.storeCookies(
      {
        toolId: netflixTool.id,
        cookies: netflixCookies,
      },
      userId,
    );
  }
}
