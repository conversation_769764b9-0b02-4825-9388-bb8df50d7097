'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowLeft, Users, Package, CreditCard, TrendingUp, Calendar, BarChart2, PieChart, Activity } from 'lucide-react';
import adminService from '@/services/adminService';
import { toast } from 'react-hot-toast';

// Stat card component
const StatCard = ({ title, value, icon, color }: { title: string, value: string, icon: React.ReactNode, color: string }) => (
  <div className="bg-white rounded-xl shadow-md overflow-hidden">
    <div className="p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-full ${color} text-white mr-4`}>
          {icon}
        </div>
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  </div>
);

// Chart component for monthly revenue
const MonthlyRevenueChart = ({ data }: { data: { month: string, revenue: number }[] }) => {
  // Find the maximum revenue to calculate bar heights
  const maxRevenue = Math.max(...data.map(item => item.revenue));

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue</h3>
        <div className="flex items-end justify-between h-64">
          {data.map((item, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className="w-12 bg-yellow-400 rounded-t-md"
                style={{ height: `${(item.revenue / maxRevenue) * 200}px` }}
              ></div>
              <p className="mt-2 text-sm text-gray-500">{item.month}</p>
              <p className="text-xs font-medium text-gray-700">${item.revenue}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Table component for top tools
const TopToolsTable = ({ data }: { data: { id: string, name: string, subscriptions: number, revenue: number }[] }) => (
  <div className="bg-white rounded-xl shadow-md overflow-hidden">
    <div className="p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Tools</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tool</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscriptions</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.length > 0 ? (
              data.map((tool, index) => (
                <tr key={tool.id || index}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{tool.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{tool.subscriptions}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">${tool.revenue}</div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                  No tools available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  </div>
);

// Pie chart component for demographics
const DemographicsChart = ({ data }: { data: { name: string, percentage: number }[] }) => {
  // Calculate the circumference of the circle
  const circumference = 2 * Math.PI * 50;

  // Calculate the offset for each segment
  let offset = 0;
  const segments = data.map((item, index) => {
    const segmentLength = (item.percentage / 100) * circumference;
    const segment = {
      name: item.name,
      percentage: item.percentage,
      offset,
      length: segmentLength,
      color: [
        'bg-blue-500',
        'bg-yellow-400',
        'bg-green-500',
        'bg-purple-500',
        'bg-red-500',
      ][index % 5],
    };
    offset += segmentLength;
    return segment;
  });

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">User Demographics</h3>
        <div className="flex items-center justify-center">
          <div className="relative w-64 h-64">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              {segments.map((segment, index) => (
                <circle
                  key={index}
                  cx="50"
                  cy="50"
                  r="40"
                  fill="none"
                  stroke={segment.color.replace('bg-', 'var(--color-')}
                  strokeWidth="20"
                  strokeDasharray={`${segment.length} ${circumference - segment.length}`}
                  strokeDashoffset={-segment.offset}
                  transform="rotate(-90 50 50)"
                  style={{
                    '--color-blue-500': '#3B82F6',
                    '--color-yellow-400': '#FBBF24',
                    '--color-green-500': '#10B981',
                    '--color-purple-500': '#8B5CF6',
                    '--color-red-500': '#EF4444',
                  } as any}
                />
              ))}
            </svg>
          </div>
          <div className="ml-8">
            {segments.map((segment, index) => (
              <div key={index} className="flex items-center mb-2">
                <div className={`w-4 h-4 ${segment.color} rounded-full mr-2`}></div>
                <div className="text-sm text-gray-700">
                  {segment.name} ({segment.percentage}%)
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch dashboard data from the backend
        const dashboardData = await adminService.getDashboardOverview();

        // Check if backend is down and we're getting mock data
        if (dashboardData._backendDown) {
          toast.error('Using mock data: Backend API is unavailable');
        }

        // Transform the data to match the expected format for our UI components
        const transformedData = {
          userCount: dashboardData.userStats?.totalUsers || 0,
          toolCount: dashboardData.toolCount || 0,
          subscriptionCount: dashboardData.subscriptionStats?.totalActiveSubscriptions || 0,
          revenue: dashboardData.revenueStats?.periodRevenue || 0,
          growthRate: dashboardData.userStats?.growthRate || 0,
          toolAdoptionRate: dashboardData.toolStats?.growthRate || 0,
          revenueGrowthRate: dashboardData.revenueStats?.growthRate || 0,

          // Monthly revenue data
          monthlyRevenue: dashboardData.revenueOverTime?.map((item: any) => ({
            month: new Date(item.date).toLocaleString('default', { month: 'short' }),
            revenue: item.revenue
          })) || [],

          // Top tools data
          topTools: dashboardData.popularTools?.map((tool: any) => ({
            id: tool.id,
            name: tool.name,
            subscriptions: tool.subscriptions,
            revenue: tool.revenue
          })) || [],

          // Demographics data (this might need to be adjusted based on actual API response)
          demographics: dashboardData.demographics || [
            { name: 'Tunisia', percentage: 65 },
            { name: 'Algeria', percentage: 15 },
            { name: 'Morocco', percentage: 10 },
            { name: 'Egypt', percentage: 5 },
            { name: 'Other', percentage: 5 },
          ],
        };

        setAnalyticsData(transformedData);
      } catch (err: any) {
        console.error('Error fetching analytics data:', err);
        setError(err.message || 'Failed to load analytics data');
        toast.error('Failed to load analytics data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalyticsData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-64">
        <div className="text-red-500 mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
        <p className="text-sm text-gray-500">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-yellow-400 text-white rounded-md hover:bg-yellow-500 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link
          href="/admin/analytics"
          className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </Link>
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={analyticsData.userCount.toString()}
          icon={<Users className="w-5 h-5" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Tools"
          value={analyticsData.toolCount.toString()}
          icon={<Package className="w-5 h-5" />}
          color="bg-yellow-500"
        />
        <StatCard
          title="Active Subscriptions"
          value={analyticsData.subscriptionCount.toString()}
          icon={<CreditCard className="w-5 h-5" />}
          color="bg-green-500"
        />
        <StatCard
          title="Monthly Revenue"
          value={`$${analyticsData.revenue}`}
          icon={<TrendingUp className="w-5 h-5" />}
          color="bg-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MonthlyRevenueChart data={analyticsData.monthlyRevenue} />
        <DemographicsChart data={analyticsData.demographics} />
      </div>

      <TopToolsTable data={analyticsData.topTools} />

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Growth Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                  <Users className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">User Growth</p>
                  <p className="text-xl font-bold text-gray-900">
                    +{analyticsData.growthRate ? analyticsData.growthRate.toFixed(1) : '0'}%
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4">
                  <Package className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Tool Adoption</p>
                  <p className="text-xl font-bold text-gray-900">
                    +{analyticsData.toolAdoptionRate ? analyticsData.toolAdoptionRate.toFixed(1) : (analyticsData.growthRate ? (analyticsData.growthRate + 2).toFixed(1) : '0')}%
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                  <TrendingUp className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Revenue Growth</p>
                  <p className="text-xl font-bold text-gray-900">
                    +{analyticsData.revenueGrowthRate ? analyticsData.revenueGrowthRate.toFixed(1) : (analyticsData.growthRate ? (analyticsData.growthRate + 3).toFixed(1) : '0')}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
