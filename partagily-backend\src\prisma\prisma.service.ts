import { Injectable, OnModuleInit, OnModuleDestroy, INestApplication } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private isConnected = false;

  constructor() {
    super({
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    });
  }

  // Enable shutdown hooks for graceful shutdown
  async enableShutdownHooks(app: INestApplication) {
    process.on('beforeExit', async () => {
      await app.close();
    });
  }

  async onModuleInit() {
    try {
      console.log('Connecting to database...');
      console.log('DATABASE_URL configured:', !!process.env.DATABASE_URL);

      // Validate DATABASE_URL
      if (!process.env.DATABASE_URL) {
        throw new Error('DATABASE_URL is not configured');
      }

      // Log database connection details (safely)
      const dbUrlParts = process.env.DATABASE_URL.split('@');
      if (dbUrlParts.length > 1) {
        const connectionDetails = dbUrlParts[1].split('/');
        console.log(`Connecting to PostgreSQL at ${connectionDetails[0]}, database: ${connectionDetails[1].split('?')[0]}`);
      }

      // Attempt to connect with a timeout
      const connectPromise = this.$connect();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout after 5 seconds')), 5000)
      );

      await Promise.race([connectPromise, timeoutPromise]);
      console.log('Database connection established successfully');

      // Test query to verify connection
      const userCount = await this.user.count();
      console.log(`Database connection verified. User count: ${userCount}`);
      this.isConnected = true;
    } catch (error) {
      console.error('Failed to connect to database:', error);
      console.error('Database connection error details:', error.message);

      // Log database URL (safely)
      if (process.env.DATABASE_URL) {
        const dbUrlParts = process.env.DATABASE_URL.split('@');
        const safeDbUrl = dbUrlParts.length > 1
          ? `${dbUrlParts[0].split(':')[0]}:***@${dbUrlParts[1]}`
          : 'Invalid format';
        console.error('DATABASE_URL format:', safeDbUrl);
      } else {
        console.error('DATABASE_URL: Not configured');
      }

      // Try to reconnect with more detailed logging
      try {
        console.log('Attempting to reconnect to database after 2 seconds...');
        // Wait 2 seconds before reconnecting
        await new Promise(resolve => setTimeout(resolve, 2000));

        await this.$connect();
        console.log('Reconnection successful');

        // Test query to verify connection
        try {
          const userCount = await this.user.count();
          console.log(`Database connection verified. User count: ${userCount}`);
          this.isConnected = true;
        } catch (queryError) {
          console.error('Database connection established but query failed:', queryError);
          throw new Error('Database connection established but query failed');
        }
      } catch (reconnectError) {
        console.error('Reconnection failed:', reconnectError.message);
        console.error('Detailed reconnection error:', reconnectError);

        // Exit in both production and development
        console.error('Database connection failed. Application cannot function without database access.');

        if (process.env.NODE_ENV === 'production') {
          console.error('Production environment detected. Exiting application.');
          process.exit(1);
        } else {
          console.error('Development environment detected. Exiting application.');
          process.exit(1);
          // No more mock data mode - we require a real database connection
        }
      }
    }
  }

  async onModuleDestroy() {
    if (this.isConnected) {
      await this.$disconnect();
    }
  }

  // Check if database is connected
  isDatabaseConnected(): boolean {
    return this.isConnected;
  }

  // Execute a database operation with proper error handling
  // Note: fallback parameter is kept for backward compatibility but is no longer used
  async safeExecute<T>(operation: () => Promise<T>, _fallback: () => T | Promise<T>): Promise<T> {
    // Always check database connection first
    if (!this.isConnected) {
      console.error('Database not connected');

      // Try to reconnect
      try {
        console.log('Attempting to reconnect to database...');
        await this.$connect();

        // Verify connection with a simple query
        await this.user.count();

        console.log('Reconnection successful');
        this.isConnected = true;
      } catch (reconnectError) {
        console.error('Reconnection failed:', reconnectError.message);
        console.error('Cannot proceed with database operation without connection');

        // Always throw an error - no more fallbacks to mock data
        throw new Error('Database connection error: Cannot perform operation');
      }
    }

    try {
      // Execute the operation with detailed logging
      console.log('Executing database operation...');
      const result = await operation();
      console.log('Database operation completed successfully');
      return result;
    } catch (error) {
      console.error('Database operation failed:', error);

      // Log detailed error information
      if (error.code) {
        console.error(`Database error code: ${error.code}`);
      }

      if (error.meta) {
        console.error('Database error metadata:', error.meta);
      }

      // Log the stack trace for debugging
      console.error('Error stack trace:', error.stack);

      // Always throw the error - no more fallbacks to mock data
      throw error;
    }
  }

  async cleanDatabase() {
    if ((process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') && this.isConnected) {
      // The order matters due to foreign key constraints
      const models = Reflect.ownKeys(this).filter(
        (key: string) => key[0] !== '_' && key[0] !== '$' && typeof (this as any)[key] === 'object' && (this as any)[key] !== null,
      );

      return Promise.all(
        models.map((modelKey: string) => (this as any)[modelKey].deleteMany()),
      );
    }
  }
}
