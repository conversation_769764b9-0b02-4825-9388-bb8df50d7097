import { NextRequest, NextResponse } from 'next/server';

// This would be a database in a real application
let tools = [];

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const id = params.id;
  const data = await request.json();
  
  // Find the tool index
  const index = tools.findIndex(t => t.id === id);
  
  if (index === -1) {
    return NextResponse.json({ message: 'Tool not found' }, { status: 404 });
  }
  
  // Update the tool status
  tools[index] = {
    ...tools[index],
    isActive: data.isActive,
    updatedAt: new Date().toISOString(),
  };
  
  return NextResponse.json({ 
    message: 'Tool status updated successfully',
    tool: tools[index]
  });
}
