// Enhanced background service worker for Partagily Chrome Extension with security features
console.log('Partagily extension background service worker started');

// API configuration
const API_URL = 'http://localhost:3005'; // Backend API URL

// Obfuscation key for cookie encryption
const OBFUSCATION_KEY = 'p4rt4g1ly-s3cur3-k3y';

/**
 * Encrypts cookie data using a simple XOR cipher
 * @param {string} data - The data to encrypt
 * @returns {string} - The encrypted data
 */
function encryptData(data) {
  if (!data) return '';

  let result = '';
  for (let i = 0; i < data.length; i++) {
    const charCode = data.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
    result += String.fromCharCode(charCode);
  }

  // Convert to base64 to make it harder to read
  return btoa(result);
}

/**
 * Decrypts cookie data
 * @param {string} encryptedData - The encrypted data
 * @returns {string} - The decrypted data
 */
function decryptData(encryptedData) {
  if (!encryptedData) return '';

  try {
    // Decode from base64
    const data = atob(encryptedData);

    let result = '';
    for (let i = 0; i < data.length; i++) {
      const charCode = data.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
      result += String.fromCharCode(charCode);
    }

    return result;
  } catch (error) {
    console.error('Error decrypting data:', error);
    return '';
  }
}

// Cookie manager (simplified)
const cookieManager = {
  async injectCookies(domain, cookies) {
    console.log(`Injecting ${cookies.length} cookies for domain ${domain}`);

    try {
      // Check if cookies API is available
      if (!chrome.cookies) {
        throw new Error('Cookies API not available');
      }

      // Validate domain
      if (!domain) {
        throw new Error('Invalid domain');
      }

      // Validate cookies
      if (!cookies || !Array.isArray(cookies) || cookies.length === 0) {
        throw new Error('No cookies to inject');
      }

      // Log the cookies we're injecting (for debugging)
      console.log('Cookies to inject:', JSON.stringify(cookies, null, 2));

      // Inject each cookie
      for (const cookie of cookies) {
        // Ensure the cookie has a name and value
        if (!cookie.name || !cookie.value) {
          console.warn('Skipping cookie with missing name or value:', cookie);
          continue;
        }

        // Determine the cookie domain
        const cookieDomain = cookie.domain || `.${domain}`;

        // Determine the cookie URL
        const cookieUrl = `https://${domain}`;

        // Determine the expiration date
        let expirationDate;
        if (cookie.expiresAt) {
          expirationDate = new Date(cookie.expiresAt).getTime() / 1000;
        } else if (cookie.expirationDate) {
          expirationDate = new Date(cookie.expirationDate).getTime() / 1000;
        } else {
          // Default to 30 days
          expirationDate = (Date.now() + 86400000 * 30) / 1000;
        }

        // Create the cookie object with encrypted value for sensitive cookies
        const isSecureCookie = cookie.name.includes('NetflixId') ||
                              cookie.name.includes('SecureNetflixId') ||
                              cookie.name.includes('accessToken') ||
                              cookie.name.includes('refreshToken');

        // Encrypt the value if it's a sensitive cookie
        const cookieValue = isSecureCookie ? encryptData(cookie.value) : cookie.value;

        const cookieObj = {
          url: cookieUrl,
          name: cookie.name,
          value: cookieValue,
          domain: cookieDomain,
          path: cookie.path || '/',
          secure: cookie.secure !== false,
          httpOnly: cookie.httpOnly || false,
          sameSite: cookie.sameSite || 'lax',
          expirationDate: expirationDate
        };

        try {
          // Set the cookie
          await chrome.cookies.set(cookieObj);
          console.log(`Cookie ${cookie.name} set successfully`);
        } catch (cookieError) {
          console.error(`Error setting cookie ${cookie.name}:`, cookieError);
          // Continue with other cookies even if one fails
        }
      }

      return true;
    } catch (error) {
      console.error('Error injecting cookies:', error);
      return false;
    }
  }
};

// Initialize when the extension is installed
try {
  chrome.runtime.onInstalled.addListener(() => {
    console.log('Partagily extension installed');

    // Initialize storage with default values (if storage API is available)
    if (chrome.storage && chrome.storage.local) {
      chrome.storage.local.set({
        isLoggedIn: false,
        user: null,
        activeTools: []
      });
      console.log('Storage initialized successfully');
    } else {
      console.warn('Storage API not available, skipping initialization');
    }
  });
} catch (error) {
  console.error('Error in onInstalled listener:', error);
}

// Listen for messages from content scripts
try {
  chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
    console.log('Background received message:', message);

    if (!message || !message.action) {
      sendResponse({ success: false, error: 'Invalid message format' });
      return true;
    }

    // Handle different message actions
    switch (message.action) {
      case 'openToolTab':
        handleOpenToolTab(message.data, sendResponse);
        return true; // Required for async sendResponse

      case 'checkExtensionStatus':
        sendResponse({ success: true, installed: true });
        return true;

      case 'checkCookies':
        handleCheckCookies(message, sendResponse);
        return true; // Required for async sendResponse

      case 'injectCookies':
        handleInjectCookies(message, sendResponse);
        return true; // Required for async sendResponse

      case 'loginStatus':
        // Store login status for the site
        console.log(`Login status for ${message.site}: ${message.status}`);
        sendResponse({ success: true });
        return true;

      default:
        console.warn(`Unknown action: ${message.action}`);
        sendResponse({ success: false, error: 'Unknown action' });
        return true;
    }
  });
  console.log('Message listener registered successfully');
} catch (error) {
  console.error('Error setting up message listener:', error);
}

/**
 * Handle opening a tool in a new tab
 * @param {Object} data - Tool data
 * @param {Function} sendResponse - Function to send response back to caller
 */
async function handleOpenToolTab(data, sendResponse) {
  try {
    if (!data || !data.toolId || !data.url) {
      throw new Error('Invalid tool data');
    }

    console.log('Opening tool tab:', data);

    // Check if tabs API is available
    if (!chrome.tabs) {
      throw new Error('Tabs API not available');
    }

    // Create a new tab with the tool URL
    const tab = await chrome.tabs.create({ url: data.url });

    // For Netflix, inject cookies from the backend
    if (data.url.includes('netflix.com')) {
      const domain = new URL(data.url).hostname;
      console.log('Fetching and injecting Netflix cookies');

      // Check if cookies API is available
      if (!chrome.cookies) {
        console.warn('Cookies API not available, skipping cookie injection');
        sendResponse({
          success: true,
          tabId: tab.id,
          hasAccess: true,
          injected: false,
          message: 'Cookies API not available, skipping cookie injection'
        });
        return;
      }

      try {
        // Get the JWT token from the message data
        const token = data.token;

        if (!token) {
          console.warn('No authentication token provided, using mock cookies');

          // Use mock cookies as fallback
          const mockCookies = [
            {
              name: 'NetflixId',
              value: 'ct%3DBgjHlOvcAxLJAzqif7OkpE3euD5ThAzE5aVbG6QWShqWParCLVjeZWCR1Nxi4bd75pmkNuAi_idh5mFaVe0rgwOkKdqxl0cIP3_s9GnK2iP7IazlDWASLDdQVe9qtod-JDETXuypJTPXSglH-p1Z3J9s-7kEmvkdZkewe6zbLIzs1DN3B4A6CT7d0ZPKtPF97_MKfVCtUZ-VhqZSummLvKZR7NQFI5KwGMNrhIvF_qGp6_RF9G4-k8ZgyKz2l64O1gDtWbSyB2fuvogGtgw3Bs2Ap8Ua80Fv8YXYdnKMByGc3mpkjZKnCzqufKi0WogDsGFx4a90wwWv-tIvDvk2CTRME1d_vsyqaSQ0ot1iNMGcgbTsBi-KjeKCtjPS4zN6284IGZMh0IO0FKqhj825liAxas3y1bhQrzC--wQBART33B5Ri_RPpIixZ6jeXZ211rnxZyl-9nRYhPzKFQyF6MTi0OxAN6a3VD31m4VHaJYreL10wytkBeLkAIempsUPI2st1mM4ejkAg_Ex58Rqh3RG9x8gkzAd9QI7Ztzi-rhIt4lTpoFYknimXNMk7E3MPI-xZWvl6iXbO3DXi_EMlgAyGNblCg0pZT1WrtpbrKigoJkYBiIOCgy5Bw27fiDgO5uLCuM.%26ch%3DAQEAEAABABSOEQIKuuTe_eQeXL7j6pyVuF4Fi7iISbs.%26v%3D3',
              domain: '.netflix.com',
              path: '/',
              secure: true,
              httpOnly: true,
              sameSite: 'lax',
              expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
            },
            {
              name: 'SecureNetflixId',
              value: 'v%3D3%26mac%3DAQEAEQABABSdeXWyxcmjA9ORFlm8qGBhxy_vkrTpPMw.%26dt%3D1745610340903',
              domain: '.netflix.com',
              path: '/',
              secure: true,
              httpOnly: true,
              sameSite: 'lax',
              expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
            }
          ];

          // Inject the mock cookies
          const injectionSuccess = await cookieManager.injectCookies(domain, mockCookies);
          const injectionMessage = injectionSuccess ? 'Mock cookies injected successfully' : 'Failed to inject mock cookies';

          sendResponse({
            success: true,
            tabId: tab.id,
            hasAccess: true,
            injected: injectionSuccess,
            message: injectionMessage
          });
          return;
        }

        // Try to fetch cookies from the backend
        try {
          console.log('Fetching cookies from backend for tool ID:', data.toolId);

          // Make a request to the backend to get the cookies
          const response = await fetch(`${API_URL}/api/tools/${data.toolId}/cookies`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch cookies: ${response.status} ${response.statusText}`);
          }

          const cookiesData = await response.json();

          if (!cookiesData.cookies || !Array.isArray(cookiesData.cookies) || cookiesData.cookies.length === 0) {
            throw new Error('No cookies returned from backend');
          }

          console.log(`Received ${cookiesData.cookies.length} cookies from backend`);

          // Inject the cookies
          const injectionSuccess = await cookieManager.injectCookies(domain, cookiesData.cookies);
          const injectionMessage = injectionSuccess ? 'Cookies injected successfully' : 'Failed to inject cookies';

          sendResponse({
            success: true,
            tabId: tab.id,
            hasAccess: true,
            injected: injectionSuccess,
            message: injectionMessage
          });
        } catch (fetchError) {
          console.error('Error fetching cookies from backend:', fetchError);

          // Fallback to mock cookies
          console.warn('Falling back to mock cookies');
          const mockCookies = [
            {
              name: 'NetflixId',
              value: 'ct%3DBgjHlOvcAxLJAzqif7OkpE3euD5ThAzE5aVbG6QWShqWParCLVjeZWCR1Nxi4bd75pmkNuAi_idh5mFaVe0rgwOkKdqxl0cIP3_s9GnK2iP7IazlDWASLDdQVe9qtod-JDETXuypJTPXSglH-p1Z3J9s-7kEmvkdZkewe6zbLIzs1DN3B4A6CT7d0ZPKtPF97_MKfVCtUZ-VhqZSummLvKZR7NQFI5KwGMNrhIvF_qGp6_RF9G4-k8ZgyKz2l64O1gDtWbSyB2fuvogGtgw3Bs2Ap8Ua80Fv8YXYdnKMByGc3mpkjZKnCzqufKi0WogDsGFx4a90wwWv-tIvDvk2CTRME1d_vsyqaSQ0ot1iNMGcgbTsBi-KjeKCtjPS4zN6284IGZMh0IO0FKqhj825liAxas3y1bhQrzC--wQBART33B5Ri_RPpIixZ6jeXZ211rnxZyl-9nRYhPzKFQyF6MTi0OxAN6a3VD31m4VHaJYreL10wytkBeLkAIempsUPI2st1mM4ejkAg_Ex58Rqh3RG9x8gkzAd9QI7Ztzi-rhIt4lTpoFYknimXNMk7E3MPI-xZWvl6iXbO3DXi_EMlgAyGNblCg0pZT1WrtpbrKigoJkYBiIOCgy5Bw27fiDgO5uLCuM.%26ch%3DAQEAEAABABSOEQIKuuTe_eQeXL7j6pyVuF4Fi7iISbs.%26v%3D3',
              domain: '.netflix.com',
              path: '/',
              secure: true,
              httpOnly: true,
              sameSite: 'lax',
              expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
            },
            {
              name: 'SecureNetflixId',
              value: 'v%3D3%26mac%3DAQEAEQABABSdeXWyxcmjA9ORFlm8qGBhxy_vkrTpPMw.%26dt%3D1745610340903',
              domain: '.netflix.com',
              path: '/',
              secure: true,
              httpOnly: true,
              sameSite: 'lax',
              expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
            }
          ];

          // Inject the mock cookies
          const injectionSuccess = await cookieManager.injectCookies(domain, mockCookies);

          sendResponse({
            success: true,
            tabId: tab.id,
            hasAccess: true,
            injected: injectionSuccess,
            message: 'Using mock cookies (backend fetch failed): ' + fetchError.message
          });
        }
      } catch (cookieError) {
        console.error('Error in cookie injection process:', cookieError);
        sendResponse({
          success: true,
          tabId: tab.id,
          hasAccess: true,
          injected: false,
          message: 'Error in cookie injection process: ' + cookieError.message
        });
      }
    } else {
      // For other tools, just return success
      sendResponse({
        success: true,
        tabId: tab.id,
        hasAccess: true,
        message: 'Tab opened successfully'
      });
    }
  } catch (error) {
    console.error('Error opening tool tab:', error);
    sendResponse({ success: false, error: error.message });
  }
}

/**
 * Handle checking if cookies are injected for a domain
 * @param {Object} message - The message from the content script
 * @param {Function} sendResponse - Function to send response back to caller
 */
async function handleCheckCookies(message, sendResponse) {
  try {
    const domain = message.domain;

    if (!domain) {
      throw new Error('No domain specified');
    }

    // Check if cookies API is available
    if (!chrome.cookies) {
      throw new Error('Cookies API not available');
    }

    // Get all cookies for the domain
    const cookies = await chrome.cookies.getAll({ domain });

    const cookiesInjected = cookies.length > 0;
    console.log(`Cookies for ${domain}:`, cookiesInjected ? 'Injected' : 'Not injected');

    sendResponse({
      success: true,
      cookiesInjected,
      count: cookies.length
    });
  } catch (error) {
    console.error('Error checking cookies:', error);
    sendResponse({
      success: false,
      cookiesInjected: false,
      error: error.message
    });
  }
}

/**
 * Handle injecting cookies for a domain
 * @param {Object} message - The message from the content script
 * @param {Function} sendResponse - Function to send response back to caller
 */
async function handleInjectCookies(message, sendResponse) {
  try {
    const domain = message.domain;
    const forceRefresh = message.forceRefresh || false;

    if (!domain) {
      throw new Error('No domain specified');
    }

    console.log(`Attempting to inject cookies for ${domain}`);

    // For Netflix, inject cookies
    if (domain.includes('netflix.com')) {
      // Get the current tab ID
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });

      if (!tabs || tabs.length === 0) {
        throw new Error('No active tab found');
      }

      const tab = tabs[0];

      // Mock cookies for development
      const mockCookies = [
        {
          name: 'NetflixId',
          value: 'ct%3DBgjHlOvcAxLJAzqif7OkpE3euD5ThAzE5aVbG6QWShqWParCLVjeZWCR1Nxi4bd75pmkNuAi_idh5mFaVe0rgwOkKdqxl0cIP3_s9GnK2iP7IazlDWASLDdQVe9qtod-JDETXuypJTPXSglH-p1Z3J9s-7kEmvkdZkewe6zbLIzs1DN3B4A6CT7d0ZPKtPF97_MKfVCtUZ-VhqZSummLvKZR7NQFI5KwGMNrhIvF_qGp6_RF9G4-k8ZgyKz2l64O1gDtWbSyB2fuvogGtgw3Bs2Ap8Ua80Fv8YXYdnKMByGc3mpkjZKnCzqufKi0WogDsGFx4a90wwWv-tIvDvk2CTRME1d_vsyqaSQ0ot1iNMGcgbTsBi-KjeKCtjPS4zN6284IGZMh0IO0FKqhj825liAxas3y1bhQrzC--wQBART33B5Ri_RPpIixZ6jeXZ211rnxZyl-9nRYhPzKFQyF6MTi0OxAN6a3VD31m4VHaJYreL10wytkBeLkAIempsUPI2st1mM4ejkAg_Ex58Rqh3RG9x8gkzAd9QI7Ztzi-rhIt4lTpoFYknimXNMk7E3MPI-xZWvl6iXbO3DXi_EMlgAyGNblCg0pZT1WrtpbrKigoJkYBiIOCgy5Bw27fiDgO5uLCuM.%26ch%3DAQEAEAABABSOEQIKuuTe_eQeXL7j6pyVuF4Fi7iISbs.%26v%3D3',
          domain: '.netflix.com',
          path: '/',
          secure: true,
          httpOnly: true,
          sameSite: 'lax',
          expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
        },
        {
          name: 'SecureNetflixId',
          value: 'v%3D3%26mac%3DAQEAEQABABSdeXWyxcmjA9ORFlm8qGBhxy_vkrTpPMw.%26dt%3D1745610340903',
          domain: '.netflix.com',
          path: '/',
          secure: true,
          httpOnly: true,
          sameSite: 'lax',
          expiresAt: new Date(Date.now() + 86400000 * 30).toISOString()
        }
      ];

      // If force refresh is requested, clear existing cookies first
      if (forceRefresh) {
        console.log('Force refresh requested, clearing existing cookies');

        const existingCookies = await chrome.cookies.getAll({ domain });

        for (const cookie of existingCookies) {
          await chrome.cookies.remove({
            url: `https://${domain}`,
            name: cookie.name
          });
        }

        console.log(`Cleared ${existingCookies.length} existing cookies`);
      }

      // Inject the cookies
      const injectionSuccess = await cookieManager.injectCookies(domain, mockCookies);

      if (injectionSuccess) {
        console.log('Cookies injected successfully for Netflix');

        // Notify the content script
        try {
          await chrome.tabs.sendMessage(tab.id, {
            action: 'cookiesInjected',
            success: true
          });
        } catch (sendError) {
          console.warn('Error sending message to content script:', sendError);
        }

        sendResponse({
          success: true,
          injected: true,
          message: 'Cookies injected successfully'
        });
      } else {
        console.error('Failed to inject cookies for Netflix');
        sendResponse({
          success: false,
          injected: false,
          message: 'Failed to inject cookies'
        });
      }
    } else {
      // For other domains
      sendResponse({
        success: false,
        injected: false,
        message: `Cookie injection not implemented for ${domain}`
      });
    }
  } catch (error) {
    console.error('Error injecting cookies:', error);
    sendResponse({
      success: false,
      injected: false,
      message: `Error injecting cookies: ${error.message}`
    });
  }
}

// Add an alarm to periodically check for cookie updates (if alarms API is available)
try {
  if (chrome.alarms) {
    chrome.alarms.create('checkCookieUpdates', { periodInMinutes: 60 });

    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'checkCookieUpdates') {
        // Check for cookie updates
        console.log('Checking for cookie updates...');
        // This would typically check if cookies need to be refreshed
      }
    });

    console.log('Cookie update alarm created successfully');
  } else {
    console.warn('Alarms API not available, skipping cookie update alarm');
  }
} catch (error) {
  console.error('Error setting up alarms:', error);
}