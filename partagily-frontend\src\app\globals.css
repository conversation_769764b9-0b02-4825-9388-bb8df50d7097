@import "tailwindcss";

:root {
  /* Base colors */
  --background: #F9FAFB;
  --foreground: #1A1A1A;

  /* Card and section backgrounds with depth */
  --card-bg: #FFFFFF;
  --card-bg-alt: #F5F7FA;
  --card-bg-elevated: #FFFFFF;
  --card-bg-glass: rgba(255, 255, 255, 0.8);

  /* Text colors with better contrast */
  --card-text: #111111;
  --card-text-secondary: #333333;
  --text-heading: #111111;
  --text-body: #333333;

  /* Section backgrounds with layering */
  --section-bg: #F9FAFB;
  --section-bg-alt: #F2F4F7;
  --section-bg-gradient-1: linear-gradient(135deg, #FFFFFF, #F5F7FA);
  --section-bg-gradient-2: linear-gradient(135deg, #F9FAFB, #F2F4F7);

  /* Border and shadow colors for depth */
  --border-color: #E5E7EB;
  --border-color-accent: rgba(255, 173, 0, 0.3);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --shadow-color-accent: rgba(255, 173, 0, 0.1);

  /* Vibrant accent colors */
  --highlight-yellow: #FF9800; /* Darker orange-yellow for better contrast */
  --highlight-yellow-alt: #DAA520; /* Alternative gold color */
  --highlight-pink: #DB2777; /* Darker pink for better contrast */
  --highlight-blue: #3B82F6; /* Darker blue for better contrast */
  --highlight-green: #22C55E; /* Darker green for better contrast */
  --highlight-red: #EF4444; /* Darker red for better contrast */

  /* Highlight background colors */
  --highlight-yellow-bg: #FFF9E5; /* Subtle yellow background */
  --highlight-pink-bg: #FDF2F8; /* Subtle pink background */
  --highlight-blue-bg: #EFF6FF; /* Subtle blue background */
  --highlight-green-bg: #ECFDF5; /* Subtle green background */

  /* Original accent colors for dark mode */
  --highlight-yellow-dark: #FFAD00;
  --highlight-pink-dark: #e94a9c;
  --highlight-blue-dark: #4f8eff;
  --highlight-green-dark: #30d158;
  --highlight-red-dark: #ff453a;

  /* Gradient colors */
  --gradient-accent: linear-gradient(135deg, #FFAD00, #e94a9c);
  --gradient-subtle: linear-gradient(135deg, rgba(255, 173, 0, 0.05), rgba(233, 74, 156, 0.05));

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);

  color-scheme: light;
}

.dark, html.dark, body.dark-mode {
  /* Base colors */
  --background: #121212;
  --foreground: #f5f5f7;

  /* Card and section backgrounds with depth */
  --card-bg: #1a1a1a;
  --card-bg-alt: #222222;
  --card-bg-elevated: #252525;
  --card-bg-glass: rgba(255, 255, 255, 0.05);

  /* Text colors with better contrast */
  --card-text: #F3F4F6;
  --card-text-secondary: #E5E7EB;
  --text-heading: #F9FAFB;
  --text-body: #E5E7EB;

  /* Section backgrounds with layering */
  --section-bg: #121212;
  --section-bg-alt: #1a1a1a;
  --section-bg-gradient-1: linear-gradient(135deg, #1a1a1a, #252525);
  --section-bg-gradient-2: linear-gradient(135deg, #191919, #232323);

  /* Border and shadow colors for depth */
  --border-color: #2a2a2a;
  --border-color-accent: rgba(255, 173, 0, 0.3);
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-color-accent: rgba(255, 173, 0, 0.1);

  /* Vibrant accent colors */
  --highlight-yellow: #FFAD00;
  --highlight-pink: #e94a9c;
  --highlight-blue: #4f8eff;
  --highlight-green: #30d158;
  --highlight-red: #ff453a;
  --highlight-purple: #bf5af2;
  --highlight-teal: #64d2ff;

  /* Gradient colors */
  --gradient-start: #1a1a1a;
  --gradient-end: #252525;
  --gradient-accent: linear-gradient(135deg, #FFAD00, #e94a9c);
  --gradient-subtle: linear-gradient(135deg, rgba(255, 173, 0, 0.1), rgba(233, 74, 156, 0.1));

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.05);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

  color-scheme: dark;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  letter-spacing: -0.01em;
  line-height: 1.6;
}

/* Light mode specific styles */
section {
  background-color: var(--section-bg) !important;
  color: var(--foreground) !important;
  position: relative;
  overflow: hidden;
  z-index: 1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

section:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.bg-white {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
}

/* Text color overrides for light mode */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-heading) !important;
}

p, span, li, a:not(.btn) {
  color: var(--text-body) !important;
}

.text-gray-700, .text-gray-600, .text-gray-500, .text-gray-400 {
  color: var(--text-body) !important;
}

.bg-gradient-to-r,
.bg-gradient-to-b {
  background: var(--section-bg-gradient-1) !important;
}

/* Card styles for light mode */
.card {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.glass-card {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
}

.glass-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.gradient-card {
  background: var(--section-bg-gradient-1) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
}

.gradient-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.accent-card {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  position: relative;
}

.accent-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.accent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  border-radius: 4px 4px 0 0;
}

/* Colored cards for light mode */
.bg-pink-50 {
  background-color: rgba(219, 39, 119, 0.05) !important;
  border: 1px solid rgba(219, 39, 119, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.bg-pink-50:hover {
  box-shadow: 0 4px 12px rgba(219, 39, 119, 0.08);
  transform: translateY(-2px);
}

.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.05) !important;
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.bg-blue-50:hover {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08);
  transform: translateY(-2px);
}

.bg-gray-50 {
  background-color: rgba(0, 0, 0, 0.02) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.bg-gray-50:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.bg-yellow-50 {
  background-color: rgba(255, 152, 0, 0.05) !important;
  border: 1px solid rgba(255, 152, 0, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.bg-yellow-50:hover {
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.08);
  transform: translateY(-2px);
}

/* Card hover effects for light mode */
.hover-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

/* Icon badge styles for light mode */
.icon-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 2.5rem;
  height: 2.5rem;
}

.icon-badge:hover {
  transform: scale(1.05);
}

.icon-badge-yellow {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--highlight-yellow);
}

.icon-badge-yellow:hover {
  background-color: rgba(255, 152, 0, 0.15);
  box-shadow: 0 0 12px rgba(255, 152, 0, 0.2);
}

.icon-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--highlight-blue);
}

.icon-badge-blue:hover {
  background-color: rgba(59, 130, 246, 0.15);
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.2);
}

.icon-badge-pink {
  background-color: rgba(219, 39, 119, 0.1);
  color: var(--highlight-pink);
}

.icon-badge-pink:hover {
  background-color: rgba(219, 39, 119, 0.15);
  box-shadow: 0 0 12px rgba(219, 39, 119, 0.2);
}

.icon-badge-green {
  background-color: rgba(34, 197, 94, 0.1);
  color: var(--highlight-green);
}

.icon-badge-green:hover {
  background-color: rgba(34, 197, 94, 0.15);
  box-shadow: 0 0 12px rgba(34, 197, 94, 0.2);
}

.icon-badge-red {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--highlight-red);
}

.icon-badge-red:hover {
  background-color: rgba(239, 68, 68, 0.15);
  box-shadow: 0 0 12px rgba(239, 68, 68, 0.2);
}

/* Label badge styles */
.label-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.label-badge:hover {
  transform: translateY(-1px);
}

.label-badge-yellow {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--highlight-yellow);
}

.label-badge-yellow:hover {
  background-color: rgba(255, 152, 0, 0.15);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
}

.label-badge-blue {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--highlight-blue);
}

.label-badge-blue:hover {
  background-color: rgba(59, 130, 246, 0.15);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.label-badge-pink {
  background-color: rgba(219, 39, 119, 0.1);
  color: var(--highlight-pink);
}

.label-badge-pink:hover {
  background-color: rgba(219, 39, 119, 0.15);
  box-shadow: 0 2px 8px rgba(219, 39, 119, 0.15);
}

.label-badge-green {
  background-color: rgba(34, 197, 94, 0.1);
  color: var(--highlight-green);
}

.label-badge-green:hover {
  background-color: rgba(34, 197, 94, 0.15);
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.15);
}

.dark .icon-badge-yellow, body.dark-mode .icon-badge-yellow,
.dark .label-badge-yellow, body.dark-mode .label-badge-yellow {
  background-color: rgba(255, 173, 0, 0.1);
  color: var(--highlight-yellow-dark);
}

.dark .icon-badge-blue, body.dark-mode .icon-badge-blue,
.dark .label-badge-blue, body.dark-mode .label-badge-blue {
  background-color: rgba(79, 142, 255, 0.1);
  color: var(--highlight-blue-dark);
}

.dark .icon-badge-pink, body.dark-mode .icon-badge-pink,
.dark .label-badge-pink, body.dark-mode .label-badge-pink {
  background-color: rgba(233, 74, 156, 0.1);
  color: var(--highlight-pink-dark);
}

.dark .icon-badge-green, body.dark-mode .icon-badge-green,
.dark .label-badge-green, body.dark-mode .label-badge-green {
  background-color: rgba(48, 209, 88, 0.1);
  color: var(--highlight-green-dark);
}

/* Blob decorations for light mode */
.blob-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.05;
  z-index: -1;
}

.blob-yellow {
  background: var(--highlight-yellow);
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.blob-pink {
  background: var(--highlight-pink);
  width: 250px;
  height: 250px;
  bottom: -50px;
  left: -50px;
}

.blob-blue {
  background: var(--highlight-blue);
  width: 200px;
  height: 200px;
  top: 30%;
  left: -50px;
}

/* Curved shape decorations for light mode */
.curved-shape-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: -1;
}

.curved-shape-top svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.curved-shape-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: -1;
}

.curved-shape-bottom svg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(180deg);
}

/* Force dark mode for specific sections */
.dark section, body.dark-mode section {
  background-color: var(--section-bg) !important;
  color: var(--foreground) !important;
}

.dark .bg-white, body.dark-mode .bg-white {
  background-color: var(--card-bg) !important;
}

.dark .text-gray-700, body.dark-mode .text-gray-700{
  color: var(--card-text-secondary) !important;
}

.dark .bg-gradient-to-r, body.dark-mode .bg-gradient-to-r,
.dark .bg-gradient-to-b, body.dark-mode .bg-gradient-to-b {
  background: var(--section-bg) !important;
}

/* Modern section styling with curved shapes and layered backgrounds */

/* Base section styling */
.dark section, body.dark-mode section {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

/* Features section with layered gradient and curved shapes */
.dark #features, body.dark-mode #features {
  background: var(--section-bg-gradient-1) !important;
  position: relative;
}

.dark #features::before, body.dark-mode #features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 173, 0, 0.3), transparent);
}

.dark #features::after, body.dark-mode #features::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(233, 74, 156, 0.3), transparent);
}

/* Curved shape decorations */
.dark .curved-shape-top, body.dark-mode .curved-shape-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: -1;
}

.dark .curved-shape-top svg, body.dark-mode .curved-shape-top svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.dark .curved-shape-bottom, body.dark-mode .curved-shape-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: -1;
}

.dark .curved-shape-bottom svg, body.dark-mode .curved-shape-bottom svg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: rotate(180deg);
}

/* Blob decorations */
.dark .blob-decoration, body.dark-mode .blob-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.15;
  z-index: -1;
}

.dark .blob-yellow, body.dark-mode .blob-yellow {
  background: var(--highlight-yellow);
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.dark .blob-pink, body.dark-mode .blob-pink {
  background: var(--highlight-pink);
  width: 250px;
  height: 250px;
  bottom: -50px;
  left: -50px;
}

.dark .blob-blue, body.dark-mode .blob-blue {
  background: var(--highlight-blue);
  width: 200px;
  height: 200px;
  top: 30%;
  left: -50px;
}

/* Stats section with vibrant gradient */
.dark section.py-20.bg-gradient-to-r, body.dark-mode section.py-20.bg-gradient-to-r {
  background: linear-gradient(135deg, rgba(255, 173, 0, 0.15), rgba(233, 74, 156, 0.15)) !important;
  position: relative;
}

.dark section.py-20.bg-gradient-to-r::before, body.dark-mode section.py-20.bg-gradient-to-r::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 30%, rgba(255, 173, 0, 0.15), transparent 70%),
              radial-gradient(circle at 80% 70%, rgba(233, 74, 156, 0.15), transparent 70%);
  pointer-events: none;
}

/* Problem section styling with depth */
.dark #problem, body.dark-mode #problem {
  background: var(--section-bg-gradient-2) !important;
  position: relative;
}

.dark #problem::before, body.dark-mode #problem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(79, 142, 255, 0.2), transparent);
}

/* How it works section styling with depth */
.dark #how-it-works, body.dark-mode #how-it-works {
  background: linear-gradient(135deg, #121212, #1a1a1a) !important;
  position: relative;
}

/* Testimonials section styling with depth */
.dark #testimonials, body.dark-mode #testimonials {
  background: linear-gradient(to bottom, #141414, #1a1a1a) !important;
  position: relative;
}

/* Pricing section styling with depth */
.dark #pricing, body.dark-mode #pricing {
  background: linear-gradient(135deg, #141414, #1a1a1a) !important;
  position: relative;
}

/* Section dividers */
.dark .section-divider, body.dark-mode .section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  margin: 4rem 0;
  width: 100%;
}

/* Force dark text colors with better contrast */
.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6,
body.dark-mode h1, body.dark-mode h2, body.dark-mode h3,
body.dark-mode h4, body.dark-mode h5, body.dark-mode h6 {
  color: var(--text-heading) !important;
}

.dark p, .dark span, .dark li, .dark a:not(.btn),
body.dark-mode p, body.dark-mode span, body.dark-mode li, body.dark-mode a:not(.btn) {
  color: var(--text-body) !important;
}

.dark .text-gray-900, .dark .text-gray-800,
body.dark-mode .text-gray-900, body.dark-mode .text-gray-800 {
  color: var(--text-heading) !important;
}

.dark .text-gray-700, .dark .text-gray-600, .dark .text-gray-500, .dark .text-gray-400,
body.dark-mode .text-gray-700, body.dark-mode .text-gray-600,
body.dark-mode .text-gray-500, body.dark-mode .text-gray-400 {
  color: var(--text-body) !important;
}

/* Pricing section */
.dark section#pricing, body.dark-mode section#pricing {
  background-color: #111111 !important;
}

/* Testimonials section */
.dark section#testimonials, body.dark-mode section#testimonials {
  background-color: #111111 !important;
}

/* Local payment section */
.dark section#local-payment, body.dark-mode section#local-payment {
  background-color: #111111 !important;
}

/* Override all section backgrounds in dark mode */
.dark section, body.dark-mode section {
  background-color: #111111 !important;
}

/* Modern card styles with depth and glassmorphism */
.dark .card, body.dark-mode .card,
.dark .bg-white, body.dark-mode .bg-white {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Glass card style */
.dark .glass-card, body.dark-mode .glass-card {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 16px;
}

/* Gradient card style */
.dark .gradient-card, body.dark-mode .gradient-card {
  background: var(--section-bg-gradient-1) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
}

/* Accent card style */
.dark .accent-card, body.dark-mode .accent-card {
  background-color: var(--card-bg) !important;
  border: 1px solid var(--border-color-accent);
  box-shadow: 0 8px 24px var(--shadow-color-accent);
  border-radius: 16px;
  position: relative;
}

.dark .accent-card::before, body.dark-mode .accent-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-accent);
  border-radius: 4px 4px 0 0;
}

/* Colored cards with proper styling */
.dark .bg-pink-50, body.dark-mode .bg-pink-50 {
  background-color: rgba(233, 74, 156, 0.08) !important;
  border: 1px solid rgba(233, 74, 156, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
}

.dark .bg-blue-50, body.dark-mode .bg-blue-50 {
  background-color: rgba(79, 142, 255, 0.08) !important;
  border: 1px solid rgba(79, 142, 255, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
}

.dark .bg-gray-50, body.dark-mode .bg-gray-50 {
  background-color: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
}

.dark .bg-yellow-50, body.dark-mode .bg-yellow-50 {
  background-color: rgba(255, 173, 0, 0.08) !important;
  border: 1px solid rgba(255, 173, 0, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
}

/* Card hover effects */
.dark .hover-card, body.dark-mode .hover-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .hover-card:hover, body.dark-mode .hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

/* Staggered card layout */
.dark .staggered-cards, body.dark-mode .staggered-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.dark .staggered-cards > div:nth-child(even), body.dark-mode .staggered-cards > div:nth-child(even) {
  margin-top: 2rem;
}

/* Horizontal scroll for mobile */
@media (max-width: 768px) {
  .dark .horizontal-scroll, body.dark-mode .horizontal-scroll {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    padding-bottom: 1rem;
  }

  .dark .horizontal-scroll::-webkit-scrollbar, body.dark-mode .horizontal-scroll::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .dark .horizontal-scroll > div, body.dark-mode .horizontal-scroll > div {
    flex: 0 0 auto;
    width: 85%;
    scroll-snap-align: start;
    margin-right: 1rem;
  }
}

/* Stats section with gradient */
.dark section.py-20.bg-gradient-to-r, body.dark-mode section.py-20.bg-gradient-to-r {
  background: linear-gradient(to right, #7e5304, #4b1d38) !important;
}

/* Modern button styles with dark mode support */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-weight: 600;
  font-family: var(--font-inter), sans-serif;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  padding: 0.875rem 1.75rem;
  position: relative;
  overflow: hidden;
  letter-spacing: -0.01em;
}

/* Primary button - solid background with gradient */
.btn-primary {
  background: var(--highlight-yellow);
  color: #111111;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  border: none;
  transform: translateY(0) scale(1);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.3), 0 0 15px rgba(255, 152, 0, 0.2);
  background: #FFB300; /* Slightly brighter on hover */
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-primary:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
  background: #E69500; /* Darker on press */
  transition: all 0.1s ease;
}

.dark .btn-primary, body.dark-mode .btn-primary {
  background: var(--highlight-yellow-dark);
  box-shadow: 0 4px 16px rgba(255, 173, 0, 0.3);
}

.dark .btn-primary:hover, body.dark-mode .btn-primary:hover {
  box-shadow: 0 8px 24px rgba(255, 173, 0, 0.4), 0 0 20px rgba(255, 173, 0, 0.2);
  background: #FFAD00;
}

.dark .btn-primary:active, body.dark-mode .btn-primary:active {
  box-shadow: 0 2px 8px rgba(255, 173, 0, 0.3);
  background: #E69D00; /* Darker on press */
}

/* Secondary button - glass style */
.btn-outline {
  background-color: transparent;
  border: 1px solid rgba(255, 152, 0, 0.5);
  color: var(--highlight-yellow);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.1);
  transform: translateY(0) scale(1);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: 12px;
}

.dark .btn-outline, body.dark-mode .btn-outline {
  background-color: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 173, 0, 0.3);
  color: var(--highlight-yellow-dark);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.btn-outline:hover {
  background-color: rgba(255, 152, 0, 0.1);
  border-color: var(--highlight-yellow);
  color: var(--highlight-yellow);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.15), 0 0 10px rgba(255, 152, 0, 0.1);
}

.dark .btn-outline:hover, body.dark-mode .btn-outline:hover {
  background-color: rgba(255, 173, 0, 0.1);
  border-color: var(--highlight-yellow-dark);
  color: var(--highlight-yellow-dark);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.btn-outline:active {
  transform: translateY(1px) scale(0.98);
  background-color: rgba(255, 152, 0, 0.15);
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);
  transition: all 0.1s ease;
}

.dark .btn-outline:active, body.dark-mode .btn-outline:active {
  transform: translateY(1px) scale(0.98);
  background-color: rgba(255, 173, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}

/* Tertiary button - ghost style */
.btn-ghost {
  background-color: transparent;
  border: none;
  color: var(--foreground);
  padding: 0.75rem 1.25rem;
}

.btn-ghost:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--highlight-yellow);
}

/* Button with icon */
.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.btn-icon svg {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.btn-icon:hover svg:not(:last-child) {
  transform: translateX(2px);
}

.btn-icon:hover svg:last-child {
  transform: translateY(2px);
}

/* Highlighted keywords in headers */
.highlight {
  color: #FF9800; /* Darker orange for better contrast */
  position: relative;
  z-index: 1;
}

.highlight-alt {
  color: #DAA520; /* Gold color alternative */
  position: relative;
  z-index: 1;
}

.highlight-bg {
  color: #111111;
  background-color: var(--highlight-yellow-bg);
  padding: 0.1em 0.4em;
  border-radius: 4px;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

.highlight-underline {
  color: inherit;
  position: relative;
  display: inline-block;
}

.highlight-underline::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--highlight-yellow);
  border-radius: 3px;
}

.dark .highlight, body.dark-mode .highlight {
  color: var(--highlight-yellow-dark);
}

.dark .highlight-alt, body.dark-mode .highlight-alt {
  color: var(--highlight-yellow-dark);
}

.dark .highlight-bg, body.dark-mode .highlight-bg {
  color: #ffffff;
  background-color: rgba(255, 173, 0, 0.15);
}

.dark .highlight-underline::after, body.dark-mode .highlight-underline::after {
  background-color: var(--highlight-yellow-dark);
}

/* Hover effects */
.hover-glow:hover {
  box-shadow: 0 0 30px rgba(255, 173, 0, 0.3);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-bounce {
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-bounce:hover {
  transform: translateY(-6px);
}

/* Button sizes */
.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 8px;
}

/* Terminal text styling */
.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 500;
}

/* Dark mode specific component styling */
.dark .terminal-container, body.dark-mode .terminal-container {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 0.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.dark .terminal-header, body.dark-mode .terminal-header {
  background-color: #252525;
  border-bottom: 1px solid #333;
  padding: 0.5rem;
  display: flex;
  align-items: center;
}

.dark .terminal-content, body.dark-mode .terminal-content {
  background-color: #121212;
  color: #f5f5f7;
  padding: 1rem;
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

/* Testimonial card styling */
.dark .testimonial-card, body.dark-mode .testimonial-card {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .testimonial-card:hover, body.dark-mode .testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3);
}

/* Pricing card styling */
.dark .pricing-card, body.dark-mode .pricing-card {
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .pricing-card.featured, body.dark-mode .pricing-card.featured {
  background-color: rgba(255, 173, 0, 0.1);
  border: 1px solid rgba(255, 173, 0, 0.3);
  position: relative;
}

.dark .pricing-card.featured::before, body.dark-mode .pricing-card.featured::before {
  content: '';
  position: absolute;
  top: -3px;
  left: 10%;
  right: 10%;
  height: 3px;
  background: linear-gradient(90deg, transparent, var(--highlight-yellow), transparent);
  border-radius: 3px 3px 0 0;
}

/* Icon styling */
.dark .icon, body.dark-mode .icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--highlight-yellow);
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

/* Dividers */
.dark .section-divider, body.dark-mode .section-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  margin: 3rem 0;
}

/* Improved form elements */
.dark input, body.dark-mode input,
.dark textarea, body.dark-mode textarea,
.dark select, body.dark-mode select {
  background-color: #1a1a1a;
  border: 1px solid #333;
  color: #f5f5f7;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.dark input:focus, body.dark-mode input:focus,
.dark textarea:focus, body.dark-mode textarea:focus,
.dark select:focus, body.dark-mode select:focus {
  border-color: var(--highlight-yellow);
  box-shadow: 0 0 0 2px rgba(255, 173, 0, 0.2);
  outline: none;
}
