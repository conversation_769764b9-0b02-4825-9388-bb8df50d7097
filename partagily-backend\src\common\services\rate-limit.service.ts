import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * Rate Limit Service
 * 
 * This service provides methods to manage rate limiting and block lists.
 */
@Injectable()
export class RateLimitService {
  private blockList: Map<string, { expires: number }> = new Map();
  private readonly cleanupInterval = 60 * 60 * 1000; // 1 hour
  
  constructor(private configService: ConfigService) {
    // Start cleanup interval
    setInterval(() => this.cleanup(), this.cleanupInterval);
  }
  
  /**
   * Block an IP address
   * @param ip The IP address to block
   * @param duration The duration in milliseconds (default: 24 hours)
   * @returns True if the IP was blocked
   */
  blockIp(ip: string, duration: number = 24 * 60 * 60 * 1000): boolean {
    this.blockList.set(ip, {
      expires: Date.now() + duration,
    });
    
    console.warn(`IP ${ip} has been blocked for ${duration / (60 * 60 * 1000)} hours`);
    return true;
  }
  
  /**
   * Unblock an IP address
   * @param ip The IP address to unblock
   * @returns True if the IP was unblocked, false if it wasn't blocked
   */
  unblockIp(ip: string): boolean {
    if (this.blockList.has(ip)) {
      this.blockList.delete(ip);
      console.info(`IP ${ip} has been unblocked`);
      return true;
    }
    
    return false;
  }
  
  /**
   * Check if an IP address is blocked
   * @param ip The IP address to check
   * @returns True if the IP is blocked
   */
  isBlocked(ip: string): boolean {
    if (!this.blockList.has(ip)) {
      return false;
    }
    
    const block = this.blockList.get(ip);
    
    // Check if block has expired
    if (block.expires < Date.now()) {
      this.blockList.delete(ip);
      return false;
    }
    
    return true;
  }
  
  /**
   * Get the list of blocked IPs
   * @returns An array of blocked IPs with their expiration times
   */
  getBlockList(): { ip: string; expiresAt: Date }[] {
    return Array.from(this.blockList.entries()).map(([ip, data]) => ({
      ip,
      expiresAt: new Date(data.expires),
    }));
  }
  
  /**
   * Clean up expired blocks
   */
  private cleanup(): void {
    const now = Date.now();
    
    for (const [ip, data] of this.blockList.entries()) {
      if (data.expires < now) {
        this.blockList.delete(ip);
        console.info(`IP ${ip} block has expired and been removed`);
      }
    }
  }
}
