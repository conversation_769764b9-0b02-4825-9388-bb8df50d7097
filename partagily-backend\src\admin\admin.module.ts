import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

import { AdminSecurityController } from './controllers/admin-security.controller';
import { AdminAnalyticsController } from './controllers/admin-analytics.controller';
import { AdminToolsController } from './controllers/admin-tools.controller';
import { AdminUsersController } from './controllers/admin-users.controller';
import { AdminSubscriptionsController } from './controllers/admin-subscriptions.controller';
import { AdminPaymentsController } from './controllers/admin-payments.controller';
import { AdminAuditController } from './controllers/admin-audit.controller';
import { AdminSettingsController } from './controllers/admin-settings.controller';

import { SecurityModule } from '../common/security.module';
import { AuthModule } from '../auth/auth.module';
import { PrismaModule } from '../prisma/prisma.module';
import { AuditModule } from '../audit/audit.module';
import { AuditService } from '../audit/audit.service';

/**
 * Admin Module
 *
 * This module provides admin functionality for the Partagily platform.
 */
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1h'),
        },
      }),
    }),
    SecurityModule,
    AuthModule,
    PrismaModule,
    AuditModule,
  ],
  controllers: [
    AdminSecurityController,
    AdminAnalyticsController,
    AdminToolsController,
    AdminUsersController,
    AdminSubscriptionsController,
    AdminPaymentsController,
    AdminAuditController,
    AdminSettingsController,
  ],
  providers: [AuditService],
  exports: [],
})
export class AdminModule {}
