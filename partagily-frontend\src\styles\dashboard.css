/* Dashboard layout styles */

/* Ensure the dashboard page doesn't shift when cart is open */
html {
  overflow-y: scroll !important; /* Always show scrollbar */
}

/* Ensure the dashboard root doesn't shift */
#dashboard-root {
  width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
}

/* Ensure the main content container doesn't shift when cart is open */
#dashboard-main-content {
  transition: none !important;
  transform: none !important;
  width: auto !important;
  margin-right: 0 !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}

/* Also target by class for redundancy */
.flex.flex-col.flex-1.pl-0.lg\:pl-64 {
  transition: none !important;
  transform: none !important;
  width: auto !important;
  margin-right: 0 !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}

/* Prevent layout shifts when scrollbar appears/disappears */
html {
  scrollbar-gutter: stable;
  overflow-y: scroll !important; /* Always show scrollbar */
}

/* Ensure the sidebar stays in place */
.sidebar {
  position: fixed;
  z-index: 40;
}

/* Ensure proper spacing for the main content */
@media (min-width: 1024px) {
  .lg\:pl-64 {
    padding-left: 16rem;
  }
}

/* Mobile-specific styles */
@media (max-width: 1023px) {
  #dashboard-main-content {
    padding-left: 0 !important;
    width: 100% !important;
  }

  /* Add some spacing for the mobile menu button */
  main {
    padding-top: 1rem !important;
  }
}
