'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import ReviewCard, { ReviewProps } from './ReviewCard';
import './review-styles.css';

interface ReviewsSliderProps {
  reviews: ReviewProps[];
}

const ReviewsSlider: React.FC<ReviewsSliderProps> = ({ reviews }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [autoSlide, setAutoSlide] = useState(true);
  const [itemsPerView, setItemsPerView] = useState(3);

  // Create a looping array by duplicating reviews
  const loopedReviews = [...reviews, ...reviews];

  // Handle responsive display
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setItemsPerView(1);
      } else if (width < 1024) {
        setItemsPerView(2);
      } else {
        setItemsPerView(3);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Auto-rotate reviews
  useEffect(() => {
    if (!autoSlide) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % reviews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [reviews.length, autoSlide]);

  // Pause auto-rotation when user interacts
  const pauseAutoSlide = () => {
    setAutoSlide(false);
    setTimeout(() => setAutoSlide(true), 10000); // Resume after 10 seconds
  };

  const handleNext = () => {
    pauseAutoSlide();
    setCurrentIndex((prevIndex) => (prevIndex + 1) % reviews.length);
  };

  const handlePrev = () => {
    pauseAutoSlide();
    setCurrentIndex((prevIndex) => (prevIndex - 1 + reviews.length) % reviews.length);
  };

  const goToSlide = (index: number) => {
    pauseAutoSlide();
    setCurrentIndex(index);
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      <div className="relative px-4">
        {/* Reviews container */}
        <div className="overflow-hidden">
          <motion.div
            className="flex"
            initial={{ x: 0 }}
            animate={{ x: `-${currentIndex * (100 / itemsPerView)}%` }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            {loopedReviews.map((review, index) => (
              <div
                key={index}
                className="flex-shrink-0 px-2"
                style={{ width: `calc(100% / ${itemsPerView})` }}
              >
                <ReviewCard
                  rating={review.rating}
                  name={review.name}
                  text={review.text}
                />
              </div>
            ))}
          </motion.div>
        </div>

        {/* Navigation buttons */}
        <button
          onClick={handlePrev}
          className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-50 transition-colors"
          aria-label="Previous review"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          onClick={handleNext}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2 bg-white rounded-full p-2 shadow-md z-10 hover:bg-gray-50 transition-colors"
          aria-label="Next review"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>

        {/* Dots indicator */}
        <div className="flex justify-center mt-8">
          {reviews.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`h-2 w-2 mx-1 rounded-full transition-colors ${
                index === currentIndex ? 'bg-purple-500' : 'bg-gray-300'
              }`}
              aria-label={`Go to review ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReviewsSlider;
