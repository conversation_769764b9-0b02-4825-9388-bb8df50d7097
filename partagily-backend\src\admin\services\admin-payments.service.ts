import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between, MoreThan } from 'typeorm';

import { Order, OrderStatus } from '../../payments/entities/order.entity';
import { UpdatePaymentDto, RefundPaymentDto, PaymentFilterDto, PaymentStatus } from '../dto/admin-payment.dto';

@Injectable()
export class AdminPaymentsService {
  constructor(
    @InjectRepository(Order)
    private ordersRepository: Repository<Order>,
  ) {}

  async findAll(filterDto: PaymentFilterDto): Promise<{ payments: Order[]; total: number }> {
    const { userId, toolId, status, paymentMethod, startDate, endDate, sortBy, sortOrder, page, limit } = filterDto;
    const skip = (page - 1) * limit;

    const where: FindOptionsWhere<Order> = {};

    if (userId) {
      where.userId = userId;
    }

    if (toolId) {
      where.toolId = toolId;
    }

    if (status) {
      // Convert PaymentStatus to OrderStatus
      let orderStatus: OrderStatus;
      switch (status) {
        case PaymentStatus.COMPLETED:
          orderStatus = OrderStatus.COMPLETED;
          break;
        case PaymentStatus.FAILED:
          orderStatus = OrderStatus.FAILED;
          break;
        case PaymentStatus.PENDING:
          orderStatus = OrderStatus.PENDING;
          break;
        case PaymentStatus.REFUNDED:
          orderStatus = OrderStatus.REFUNDED;
          break;
        default:
          orderStatus = OrderStatus.PENDING;
      }
      where.status = orderStatus;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (startDate && endDate) {
      where.createdAt = Between(startDate, endDate);
    } else if (startDate) {
      where.createdAt = MoreThan(startDate);
    }

    const order = {};
    if (sortBy) {
      order[sortBy] = sortOrder;
    } else {
      order['createdAt'] = 'DESC';
    }

    const [payments, total] = await this.ordersRepository.findAndCount({
      where,
      order,
      skip,
      take: limit,
    });

    return { payments, total };
  }

  async findOne(id: string): Promise<Order> {
    const order = await this.ordersRepository.findOne({ where: { id } });

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return order;
  }

  async update(id: string, updatePaymentDto: UpdatePaymentDto): Promise<Order> {
    const order = await this.findOne(id);

    // Store previous status for audit logging
    const previousStatus = order.status;

    // Update the order
    Object.assign(order, updatePaymentDto);

    // Add the previous status to the response
    const updatedOrder = await this.ordersRepository.save(order);
    return { ...updatedOrder, previousStatus };
  }

  async processRefund(refundDto: RefundPaymentDto): Promise<Order> {
    const { paymentId, amount, reason } = refundDto;

    const order = await this.findOne(paymentId);

    // Check if the order can be refunded
    if (order.status !== OrderStatus.COMPLETED) {
      throw new BadRequestException('Only completed payments can be refunded');
    }

    // Check if the refund amount is valid
    if (amount <= 0 || amount > order.amount) {
      throw new BadRequestException('Invalid refund amount');
    }

    // Process the refund (in a real application, this would call a payment gateway)
    order.status = OrderStatus.REFUNDED;
    order.refundAmount = amount;
    order.refundReason = reason;
    order.refundedAt = new Date();

    return this.ordersRepository.save(order);
  }

  async getPaymentStats(): Promise<any> {
    const totalPayments = await this.ordersRepository.count();
    const completedPayments = await this.ordersRepository.count({
      where: { status: OrderStatus.COMPLETED }
    });
    const failedPayments = await this.ordersRepository.count({
      where: { status: OrderStatus.FAILED }
    });
    const pendingPayments = await this.ordersRepository.count({
      where: { status: OrderStatus.PENDING }
    });
    const refundedPayments = await this.ordersRepository.count({
      where: { status: OrderStatus.REFUNDED }
    });

    // Get total revenue
    const revenueResult = await this.ordersRepository
      .createQueryBuilder('order')
      .select('SUM(order.amount)', 'totalRevenue')
      .where('order.status = :status', { status: OrderStatus.COMPLETED })
      .getRawOne();

    const totalRevenue = revenueResult.totalRevenue || 0;

    // Get revenue by month for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const revenueByMonth = await this.ordersRepository
      .createQueryBuilder('order')
      .select('DATE_TRUNC(\'month\', order.createdAt) as month, SUM(order.amount) as revenue')
      .where('order.status = :status', { status: OrderStatus.COMPLETED })
      .andWhere('order.createdAt >= :date', { date: sixMonthsAgo })
      .groupBy('month')
      .orderBy('month', 'ASC')
      .getRawMany();

    // Get payments by method
    const paymentsByMethod = await this.ordersRepository
      .createQueryBuilder('order')
      .select('order.paymentMethod, COUNT(order.id) as count, SUM(order.amount) as amount')
      .where('order.status = :status', { status: OrderStatus.COMPLETED })
      .groupBy('order.paymentMethod')
      .getRawMany();

    return {
      totalPayments,
      completedPayments,
      failedPayments,
      pendingPayments,
      refundedPayments,
      totalRevenue,
      revenueByMonth,
      paymentsByMethod,
    };
  }
}
