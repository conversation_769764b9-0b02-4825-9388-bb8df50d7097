import { CorsOptions } from "@nestjs/common/interfaces/external/cors-options.interface";

/**
 * CORS Configuration
 *
 * This configuration defines the Cross-Origin Resource Sharing settings
 * for the Partagily API.
 */
export const corsConfig = (env: NodeJS.ProcessEnv): CorsOptions => {
  // Define allowed origins
  const allowedOrigins = [
    // Frontend URLs
    env.FRONTEND_URL,
    "https://partagily.com",
    "https://www.partagily.com",
    "https://app.partagily.com",

    // Development environments
    ...(env.NODE_ENV !== "production" ? [env.FRONTEND_URL] : []),
  ];

  // Chrome extension pattern for matching extension origins
  const chromeExtensionPattern = /^chrome-extension:\/\//;

  return {
    // Origin can be a string, array of strings, or a function
    origin: (origin, callback) => {
      // In development mode, allow all origins for easier testing
      if (env.NODE_ENV !== "production") {
        console.log("CORS: Development mode - allowing all origins");
        return callback(null, true);
      }

      // Allow requests with no origin (like mobile apps, curl, Postman)
      if (!origin) {
        console.log("CORS: No origin - allowing request");
        return callback(null, true);
      }

      // Check if the origin is in the allowed list
      if (allowedOrigins.includes(origin)) {
        console.log(`CORS: Origin ${origin} is in allowed list`);
        return callback(null, true);
      }

      // Allow Chrome extension origins
      if (chromeExtensionPattern.test(origin)) {
        console.log(`CORS: Chrome extension origin ${origin} allowed`);
        return callback(null, true);
      }

      // Optional: Allow subdomains of partagily.com in production
      if (env.NODE_ENV === "production" && origin.endsWith("partagily.com")) {
        console.log(`CORS: Subdomain of partagily.com ${origin} allowed`);
        return callback(null, true);
      }

      // Reject all other origins
      console.log(`CORS: Origin ${origin} not allowed`);
      callback(new Error(`Origin ${origin} not allowed by CORS`));
    },

    // Allow credentials (cookies, authorization headers)
    credentials: true,

    // Allowed HTTP methods
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],

    // Allowed headers
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-CSRF-Token",
      "X-Requested-With",
      "Accept",
      "Origin",
      "X-Auth-Token",
      "x-api-key",
    ],

    // Headers exposed to the client
    exposedHeaders: [
      "X-RateLimit-Limit",
      "X-RateLimit-Remaining",
      "X-RateLimit-Reset",
      "Retry-After",
    ],

    // Preflight request cache time in seconds
    preflightContinue: false,
    optionsSuccessStatus: 204,
    maxAge: 86400, // 24 hours
  };
};
