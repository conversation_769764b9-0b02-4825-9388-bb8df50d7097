document.addEventListener('DOMContentLoaded', () => {
  // DOM elements
  const loginContainer = document.getElementById('login-container');
  const dashboardContainer = document.getElementById('dashboard-container');
  const loginForm = document.getElementById('login-form');
  const emailInput = document.getElementById('email');
  const passwordInput = document.getElementById('password');
  const userName = document.getElementById('user-name');
  const userPlan = document.getElementById('user-plan');
  const toolsList = document.getElementById('tools-list');
  const logoutBtn = document.getElementById('logout-btn');
  
  // Sample tools data (in a real extension, this would come from your API)
  const tools = [
    { id: 1, name: 'Adobe CC', icon: '🎨' },
    { id: 2, name: 'Office 365', icon: '📊' },
    { id: 3, name: '<PERSON><PERSON> <PERSON>', icon: '🖌️' },
    { id: 4, name: 'Grammarly', icon: '✍️' },
    { id: 5, name: 'ChatGPT+', icon: '🤖' },
    { id: 6, name: 'Notion', icon: '📝' }
  ];
  
  // Check login status when popup opens
  checkLoginStatus();
  
  // Event listeners
  loginForm.addEventListener('submit', handleLogin);
  logoutBtn.addEventListener('click', handleLogout);
  
  // Check if user is logged in
  async function checkLoginStatus() {
    chrome.runtime.sendMessage({ action: 'getStatus' }, (response) => {
      if (response.isLoggedIn && response.user) {
        showDashboard(response.user);
      } else {
        showLogin();
      }
    });
  }
  
  // Handle login form submission
  function handleLogin(e) {
    e.preventDefault();
    
    const credentials = {
      email: emailInput.value,
      password: passwordInput.value
    };
    
    chrome.runtime.sendMessage(
      { action: 'login', data: credentials },
      (response) => {
        if (response.success) {
          showDashboard(response.user);
        } else {
          alert('Login failed. Please check your credentials.');
        }
      }
    );
  }
  
  // Handle logout
  function handleLogout() {
    chrome.runtime.sendMessage({ action: 'logout' }, (response) => {
      if (response.success) {
        showLogin();
      }
    });
  }
  
  // Show login form
  function showLogin() {
    loginContainer.style.display = 'block';
    dashboardContainer.style.display = 'none';
    emailInput.value = '';
    passwordInput.value = '';
  }
  
  // Show dashboard
  function showDashboard(user) {
    loginContainer.style.display = 'none';
    dashboardContainer.style.display = 'block';
    
    // Update user info
    userName.textContent = user.name;
    userPlan.textContent = user.subscription;
    
    // Populate tools
    populateTools();
  }
  
  // Populate tools list
  function populateTools() {
    toolsList.innerHTML = '';
    
    tools.forEach(tool => {
      const toolCard = document.createElement('div');
      toolCard.className = 'tool-card';
      toolCard.innerHTML = `
        <div class="tool-icon">${tool.icon}</div>
        <div class="tool-name">${tool.name}</div>
      `;
      
      toolCard.addEventListener('click', () => {
        activateTool(tool);
      });
      
      toolsList.appendChild(toolCard);
    });
  }
  
  // Activate a tool (inject cookies)
  function activateTool(tool) {
    // In a real extension, this would get the appropriate cookies from your backend
    // and inject them for the tool's domain
    
    let domain = '';
    let cookies = [];
    
    switch(tool.name) {
      case 'Adobe CC':
        domain = 'adobe.com';
        cookies = [{ name: 'session', value: 'sample-cookie-value' }];
        break;
      case 'Office 365':
        domain = 'office.com';
        cookies = [{ name: 'session', value: 'sample-cookie-value' }];
        break;
      // Add other tools as needed
      default:
        alert('Tool not yet supported');
        return;
    }
    
    chrome.runtime.sendMessage(
      { action: 'injectCookies', data: { domain, cookies } },
      (response) => {
        if (response.success) {
          alert(`${tool.name} activated! You can now use it.`);
        } else {
          alert(`Failed to activate ${tool.name}. Please try again.`);
        }
      }
    );
  }
});
