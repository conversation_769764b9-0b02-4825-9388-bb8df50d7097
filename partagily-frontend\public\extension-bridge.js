/**
 * Extension Bridge Script
 * This script creates a bridge between the Partagily web platform and the Chrome extension.
 * It injects a global object that the web platform can use to communicate with the extension.
 */

// Create the extension bridge when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Initializing Partagily extension bridge...');
  
  // Check if the extension is installed by looking for the content script
  function checkExtensionInstalled() {
    return new Promise((resolve) => {
      // Try to send a message to the extension
      if (window.chrome && chrome.runtime) {
        try {
          chrome.runtime.sendMessage(
            { action: 'checkExtensionStatus' },
            function(response) {
              // If we get a response, the extension is installed
              if (response && response.installed) {
                resolve(true);
              } else {
                resolve(false);
              }
            }
          );
          
          // Set a timeout in case the extension doesn't respond
          setTimeout(() => {
            resolve(false);
          }, 500);
        } catch (error) {
          // If there's an error, the extension is not installed or not accessible
          console.warn('Error checking extension status:', error);
          resolve(false);
        }
      } else {
        // Chrome runtime not available
        resolve(false);
      }
    });
  }
  
  // Initialize the extension bridge
  async function initExtensionBridge() {
    // Check if the extension is installed
    const isInstalled = await checkExtensionInstalled();
    
    // Create the global object
    window.partagilyExtension = {
      isInstalled: () => isInstalled,
      
      // Function to get access to a tool
      getAccess: (toolId, toolUrl) => {
        if (!isInstalled) {
          console.warn('Partagily extension is not installed');
          return false;
        }
        
        if (!toolId || !toolUrl) {
          console.error('Invalid tool data');
          return false;
        }
        
        // Get the access token from localStorage or sessionStorage
        const token = localStorage.getItem('accessToken') || sessionStorage.getItem('accessToken');
        
        // Send a message to the extension
        try {
          chrome.runtime.sendMessage({
            action: 'openToolTab',
            data: {
              toolId,
              url: toolUrl,
              token
            }
          }, function(response) {
            console.log('Extension response:', response);
            return response && response.success;
          });
          
          return true;
        } catch (error) {
          console.error('Error sending message to extension:', error);
          return false;
        }
      }
    };
    
    // Dispatch an event to notify the application that the extension bridge is ready
    const event = new CustomEvent('partagilyExtensionLoaded', { 
      detail: { installed: isInstalled } 
    });
    window.dispatchEvent(event);
    
    console.log('Partagily extension bridge initialized:', isInstalled ? 'Extension installed' : 'Extension not installed');
  }
  
  // Initialize the extension bridge
  initExtensionBridge();
});
