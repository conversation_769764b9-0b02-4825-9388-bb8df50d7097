import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SubscriptionsService } from '../subscriptions/subscriptions.service';

@ApiTags('plans')
@Controller('plans')
export class PlansController {
  constructor(private readonly subscriptionsService: SubscriptionsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all subscription plans' })
  @ApiResponse({ status: 200, description: 'Return all plans' })
  async findAllPlans() {
    const plans = await this.subscriptionsService.findAllPlans();
    return plans;
  }

  @Get(':name')
  @ApiOperation({ summary: 'Get plan by name' })
  @ApiResponse({ status: 200, description: 'Return plan by name' })
  async findPlanByName(@Param('name') name: string) {
    return await this.subscriptionsService.findPlanByName(name);
  }
}
