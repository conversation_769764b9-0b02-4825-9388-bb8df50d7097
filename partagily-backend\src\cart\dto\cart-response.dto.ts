import { ApiProperty } from '@nestjs/swagger';

export class CartItemDto {
  @ApiProperty({
    description: 'Cart item ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Type of item (TOOL or PLAN)',
    example: 'TOOL',
  })
  type: string;

  @ApiProperty({
    description: 'ID of the item (tool or plan)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  itemId: string;

  @ApiProperty({
    description: 'Name of the item',
    example: 'Netflix Premium',
  })
  name: string;

  @ApiProperty({
    description: 'Price of the item',
    example: 19.99,
  })
  price: number;

  @ApiProperty({
    description: 'Icon URL of the item',
    example: '/images/netflix-icon.png',
    required: false,
  })
  icon?: string;
}

export class CartResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Cart retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Cart data',
    type: 'object',
    properties: {
      id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
      userId: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
      status: { type: 'string', example: 'OPEN' },
      items: {
        type: 'array',
        items: {
          $ref: '#/components/schemas/CartItemDto'
        }
      },
      totalAmount: { type: 'number', example: 39.98 },
      paymentUrl: { type: 'string', example: 'https://app.konnect.network/payment/checkout/123456789' }
    },
    required: true
  })
  data?: {
    id: string;
    userId: string;
    status: string;
    items: CartItemDto[];
    totalAmount: number;
    paymentUrl?: string;
  };
}
