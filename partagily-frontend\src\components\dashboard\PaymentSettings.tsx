'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface NotificationSetting {
  id: string;
  label: string;
  description: string;
  enabled: boolean;
}

const PaymentSettings: React.FC = () => {
  const [currency, setCurrency] = useState('TND');
  const [autoRenew, setAutoRenew] = useState(true);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSetting[]>([
    {
      id: 'payment_success',
      label: 'Payment Success',
      description: 'Receive notifications when your payments are successful',
      enabled: true,
    },
    {
      id: 'payment_failed',
      label: 'Payment Failed',
      description: 'Receive notifications when your payments fail',
      enabled: true,
    },
    {
      id: 'subscription_renewal',
      label: 'Subscription Renewal',
      description: 'Receive notifications before your subscriptions renew',
      enabled: true,
    },
    {
      id: 'price_changes',
      label: 'Price Changes',
      description: 'Receive notifications about price changes for your subscriptions',
      enabled: false,
    },
  ]);

  const handleNotificationToggle = (id: string) => {
    setNotificationSettings(
      notificationSettings.map((setting) =>
        setting.id === id ? { ...setting, enabled: !setting.enabled } : setting
      )
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold font-mono mb-6">Payment Settings</h2>

        <div className="space-y-8">
          {/* Currency Settings */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Currency Preferences</h3>
            <div className="max-w-md">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Currency
              </label>
              <select
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
              >
                <option value="TND">Tunisian Dinar (TND)</option>
                <option value="USD">US Dollar (USD)</option>
                <option value="EUR">Euro (EUR)</option>
              </select>
              <p className="mt-2 text-sm text-gray-500">
                This setting only affects how prices are displayed. All charges will be processed in TND.
              </p>
            </div>
          </div>

          {/* Auto-Renewal Settings */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Subscription Renewal</h3>
            <div className="flex items-center">
              <div className="form-control">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={autoRenew}
                    onChange={() => setAutoRenew(!autoRenew)}
                    className="sr-only"
                  />
                  <div className={`relative w-10 h-5 transition-colors duration-200 ease-in-out rounded-full ${
                    autoRenew ? 'bg-yellow-400' : 'bg-gray-200'
                  }`}>
                    <div className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform duration-200 ease-in-out ${
                      autoRenew ? 'transform translate-x-5' : ''
                    }`}></div>
                  </div>
                  <span className="ml-3 text-sm font-medium text-gray-900">
                    Auto-renew subscriptions
                  </span>
                </label>
              </div>
            </div>
            <p className="mt-2 text-sm text-gray-500">
              When enabled, your subscriptions will automatically renew before they expire.
              You can cancel individual subscriptions at any time.
            </p>
          </div>

          {/* Notification Settings */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Payment Notifications</h3>
            <div className="space-y-4">
              {notificationSettings.map((setting) => (
                <div key={setting.id} className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id={setting.id}
                      type="checkbox"
                      checked={setting.enabled}
                      onChange={() => handleNotificationToggle(setting.id)}
                      className="focus:ring-yellow-500 h-4 w-4 text-yellow-500 border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor={setting.id} className="font-medium text-gray-700">
                      {setting.label}
                    </label>
                    <p className="text-gray-500">{setting.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Save Button */}
          <div className="pt-4 border-t border-gray-200">
            <button className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium py-2 px-6 rounded-lg transition-colors duration-200">
              Save Settings
            </button>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <h3 className="text-lg font-semibold mb-4">Payment Security</h3>
        <div className="space-y-4 text-sm text-gray-600">
          <p>
            Partagily uses industry-standard encryption to protect your payment information.
            Your payment details are never stored on our servers.
          </p>
          <p>
            All transactions are processed securely through our payment partners.
          </p>
          <div className="flex items-center space-x-4 mt-4">
            <span className="text-2xl">🔒</span>
            <span>Your payment information is secure with us</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSettings;
