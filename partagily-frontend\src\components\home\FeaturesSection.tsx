'use client';

import { motion } from 'framer-motion';

const FeaturesSection = () => {
  const features = [
    {
      title: "Shared Premium Accounts",
      description: "Access premium tools without individual subscriptions.",
      icon: "🔑"
    },
    {
      title: "Local Payment Methods",
      description: "Pay with e-Dinar, postal payment, or local bank cards.",
      icon: "💰"
    },
    {
      title: "Chrome Extension",
      description: "One-click access to all your premium tools.",
      icon: "🧩"
    },
    {
      title: "120+ Premium Tools",
      description: "From design to productivity to AI tools.",
      icon: "🛠️"
    },
    {
      title: "Instant Access",
      description: "No waiting - get access immediately after payment.",
      icon: "⚡"
    },
    {
      title: "Tunisian Support",
      description: "Local support team that understands your needs.",
      icon: "🇹🇳"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-b from-white to-pink-50 dark:from-gray-900 dark:to-pink-900/20" id="features">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            Awesome <span className="text-[#FFAD00]">Features</span> ✨
          </h2>
          <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            Partagily is packed with features designed specifically for Tunisian users
            who want access to global tools without the payment hassle.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white dark:bg-card-bg p-6 rounded-xl border border-transparent dark:border-gray-700/30 shadow-md hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover-card"
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-2 terminal-text">{feature.title}</h3>
              <p className="text-gray-700 dark:text-gray-300">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="mt-16 text-center"
        >
          <a
            href="#pricing"
            className="btn btn-primary px-8 py-3 text-lg inline-flex items-center gap-2"
          >
            See our plans 🚀
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturesSection;
