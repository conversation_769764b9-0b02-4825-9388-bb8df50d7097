import { Controller, Get, Post, Put, Patch, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Admin Tools Controller
 *
 * This controller provides endpoints to manage tools.
 */
@ApiTags('admin-tools')
@Controller('admin/tools')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminToolsController {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Get all tools with filtering and pagination
   */
  @Get()
  @ApiOperation({ summary: 'Get all tools' })
  @ApiResponse({ status: 200, description: 'List of tools' })
  @ApiQuery({ name: 'search', required: false })
  @ApiQuery({ name: 'category', required: false })
  @ApiQuery({ name: 'status', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async getAllTools(
    @Query('search') search?: string,
    @Query('category') category?: string,
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
  ) {
    try {
      // Build filter conditions
      const where: any = {};

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (category) {
        where.category = category;
      }

      if (status) {
        where.status = status;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build sort options
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      // Get tools with pagination
      const tools = await this.prisma.tool.findMany({
        where,
        skip,
        take: limit,
        orderBy,
      });

      // Get total count for pagination
      const total = await this.prisma.tool.count({ where });

      return {
        data: tools,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching tools:', error);
      throw error;
    }
  }

  /**
   * Get a tool by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get a tool by ID' })
  @ApiResponse({ status: 200, description: 'Tool details' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async getToolById(@Param('id') id: string) {
    try {
      const tool = await this.prisma.tool.findUnique({
        where: { id },
      });

      if (!tool) {
        return {
          success: false,
          message: 'Tool not found',
        };
      }

      return {
        success: true,
        data: tool,
      };
    } catch (error) {
      console.error('Error fetching tool:', error);
      throw error;
    }
  }

  /**
   * Create a new tool
   */
  @Post()
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  async createTool(@Body() toolData: any) {
    try {
      const { name, description, icon, price, category, requiredPlan, status } = toolData;

      const newTool = await this.prisma.tool.create({
        data: {
          name,
          description,
          icon,
          price,
          category,
          requiredPlan,
          status: status || 'AVAILABLE',
        },
      });

      return {
        success: true,
        message: 'Tool created successfully',
        data: newTool,
      };
    } catch (error) {
      console.error('Error creating tool:', error);
      throw error;
    }
  }

  /**
   * Update a tool
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update a tool' })
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async updateTool(@Param('id') id: string, @Body() toolData: any) {
    try {
      // Check if tool exists
      const existingTool = await this.prisma.tool.findUnique({
        where: { id },
      });

      if (!existingTool) {
        return {
          success: false,
          message: 'Tool not found',
        };
      }

      // Update tool
      const updatedTool = await this.prisma.tool.update({
        where: { id },
        data: toolData,
      });

      return {
        success: true,
        message: 'Tool updated successfully',
        data: updatedTool,
      };
    } catch (error) {
      console.error('Error updating tool:', error);
      throw error;
    }
  }

  /**
   * Delete a tool
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tool' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async deleteTool(@Param('id') id: string) {
    try {
      // Check if tool exists
      const existingTool = await this.prisma.tool.findUnique({
        where: { id },
      });

      if (!existingTool) {
        return {
          success: false,
          message: 'Tool not found',
        };
      }

      // Delete tool
      await this.prisma.tool.delete({
        where: { id },
      });

      return {
        success: true,
        message: 'Tool deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting tool:', error);
      throw error;
    }
  }

  /**
   * Update tool status
   */
  @Patch(':id/status')
  @ApiOperation({ summary: 'Update tool status' })
  @ApiResponse({ status: 200, description: 'Tool status updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  async updateToolStatus(@Param('id') id: string, @Body() data: { status: string }) {
    try {
      // Check if tool exists
      const existingTool = await this.prisma.tool.findUnique({
        where: { id },
      });

      if (!existingTool) {
        return {
          success: false,
          message: 'Tool not found',
        };
      }

      // Update tool status
      const updatedTool = await this.prisma.tool.update({
        where: { id },
        data: {
          status: data.status as any, // Cast to any to avoid TypeScript error
        },
      });

      return {
        success: true,
        message: 'Tool status updated successfully',
        data: updatedTool,
      };
    } catch (error) {
      console.error('Error updating tool status:', error);
      throw error;
    }
  }

  /**
   * Get tool statistics
   */
  @Get('stats/overview')
  @ApiOperation({ summary: 'Get tool statistics' })
  @ApiResponse({ status: 200, description: 'Tool statistics' })
  async getToolStats() {
    try {
      // Get total tools
      const totalTools = await this.prisma.tool.count();

      // Get active tools
      const activeTools = await this.prisma.tool.count({
        where: {
          status: 'AVAILABLE',
        },
      });

      // Get tools by category
      const categories = ['DESIGN', 'PRODUCTIVITY', 'WRITING', 'AI', 'DEVELOPMENT', 'MARKETING', 'EDUCATION', 'ENTERTAINMENT', 'OTHER'];

      const toolsByCategory = await Promise.all(
        categories.map(async (category) => {
          const count = await this.prisma.tool.count({
            where: {
              category: category as any, // Cast to any to avoid TypeScript error
            },
          });

          return {
            category,
            count,
          };
        })
      );

      // Get tools by status
      const statuses = ['AVAILABLE', 'UNAVAILABLE', 'MAINTENANCE', 'COMING_SOON'];

      const toolsByStatus = await Promise.all(
        statuses.map(async (status) => {
          const count = await this.prisma.tool.count({
            where: {
              status: status as any, // Cast to any to avoid TypeScript error
            },
          });

          return {
            status,
            count,
          };
        })
      );

      // Get tools by required plan
      const plans = ['STANDARD', 'PREMIUM', 'GOLD'];

      const toolsByPlan = await Promise.all(
        plans.map(async (plan) => {
          const count = await this.prisma.tool.count({
            where: {
              requiredPlan: plan as any, // Cast to any to avoid TypeScript error
            },
          });

          return {
            plan,
            count,
          };
        })
      );

      // Get most popular tools
      const popularTools = await this.prisma.tool.findMany({
        take: 5,
        orderBy: {
          orders: {
            _count: 'desc',
          },
        },
        include: {
          _count: {
            select: {
              orders: true,
            },
          },
        },
      });

      return {
        totalTools,
        activeTools,
        toolsByCategory,
        toolsByStatus,
        toolsByPlan,
        popularTools,
      };
    } catch (error) {
      console.error('Error fetching tool stats:', error);
      throw error;
    }
  }
}
