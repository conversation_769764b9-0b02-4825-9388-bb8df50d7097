import { useState, useCallback } from 'react';
import { ValidationResult } from '@/utils/validation';

interface FormValidationOptions<T> {
  initialValues: T;
  validators: Partial<Record<keyof T, (value: any) => boolean>>;
  errorMessages: Partial<Record<keyof T, string>>;
}

/**
 * Custom hook for form validation
 */
export function useFormValidation<T extends Record<string, any>>({
  initialValues,
  validators,
  errorMessages,
}: FormValidationOptions<T>) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = useState<Partial<Record<keyof T, boolean>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle input change
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setValues(prev => ({ ...prev, [name]: value }));
    
    // Validate field if it's been touched
    if (touched[name as keyof T]) {
      validateField(name as keyof T, value);
    }
  }, [touched, validators]);

  // Handle blur event
  const handleBlur = useCallback((e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setTouched(prev => ({ ...prev, [name]: true }));
    validateField(name as keyof T, value);
  }, [validators]);

  // Validate a single field
  const validateField = useCallback((field: keyof T, value: any) => {
    const validator = validators[field];
    if (!validator) return true;
    
    const isValid = validator(value);
    if (!isValid) {
      setErrors(prev => ({ ...prev, [field]: errorMessages[field] || 'Invalid value' }));
      return false;
    } else {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
      return true;
    }
  }, [validators, errorMessages]);

  // Validate all fields
  const validateForm = useCallback((): ValidationResult => {
    let isValid = true;
    const newErrors: Partial<Record<keyof T, string>> = {};
    
    // Mark all fields as touched
    const allTouched: Partial<Record<keyof T, boolean>> = {};
    
    for (const field in validators) {
      if (Object.prototype.hasOwnProperty.call(validators, field)) {
        allTouched[field as keyof T] = true;
        const validator = validators[field as keyof T];
        if (validator && !validator(values[field as keyof T])) {
          newErrors[field as keyof T] = errorMessages[field as keyof T] || 'Invalid value';
          isValid = false;
        }
      }
    }
    
    setTouched(allTouched);
    setErrors(newErrors);
    
    return { isValid, errors: newErrors as Record<string, string> };
  }, [values, validators, errorMessages]);

  // Reset form
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);

  // Set a specific field value
  const setFieldValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    if (touched[field]) {
      validateField(field, value);
    }
  }, [touched, validateField]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setIsSubmitting,
    handleChange,
    handleBlur,
    validateForm,
    resetForm,
    setFieldValue,
  };
}
