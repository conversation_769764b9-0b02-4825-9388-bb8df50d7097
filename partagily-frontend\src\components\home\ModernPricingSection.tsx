'use client';

import { motion } from 'framer-motion';
import { Check, Star } from 'lucide-react';
import Link from 'next/link';
import CurvedShape from '../ui/CurvedShape';

const ModernPricingSection = () => {
  const plans = [
    {
      name: "Starter",
      price: "29",
      period: "month",
      description: "Perfect for beginners who need access to essential tools.",
      features: [
        "Access to 20+ tools",
        "Basic support",
        "1 active tool at a time",
        "7-day tool switching"
      ],
      isPopular: false,
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]",
      buttonClass: "btn-outline"
    },
    {
      name: "Pro",
      price: "59",
      period: "month",
      description: "Our most popular plan for professionals and growing teams.",
      features: [
        "Access to 80+ tools",
        "Priority support",
        "3 active tools at a time",
        "3-day tool switching",
        "Premium tool categories"
      ],
      isPopular: true,
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]",
      buttonClass: "btn-primary"
    },
    {
      name: "Enterprise",
      price: "99",
      period: "month",
      description: "For teams and businesses that need unlimited access.",
      features: [
        "Access to 120+ tools",
        "24/7 dedicated support",
        "Unlimited active tools",
        "1-day tool switching",
        "All premium categories",
        "Custom tool requests"
      ],
      isPopular: false,
      color: "bg-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.2)]",
      buttonClass: "btn-outline"
    }
  ];

  return (
    <section className="py-24 relative" id="pricing">
      {/* Blob decorations */}
      <div className="blob-decoration blob-yellow" style={{ left: '-100px', top: '30%' }}></div>
      <div className="blob-decoration blob-pink" style={{ right: '-100px', bottom: '20%' }}></div>

      {/* Curved shapes */}
      <CurvedShape position="top" color="#4f8eff" />
      <CurvedShape position="bottom" color="#e94a9c" />

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Simple <span className="text-[#FFAD00]">Pricing</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose the plan that works best for you. All plans include access to our Chrome extension.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className={`${plan.isPopular ? 'accent-card' : 'glass-card'} p-8 relative hover-card`}
            >
              {plan.isPopular && (
                <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
                  <div className="bg-[#FFAD00] text-gray-900 text-xs font-bold px-3 py-1 rounded-full flex items-center gap-1">
                    <Star size={12} />
                    Most Popular
                  </div>
                </div>
              )}

              <div className="mb-6">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <p className="text-gray-300 mb-4">{plan.description}</p>
                <div className="flex items-baseline mb-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-xl ml-1 text-gray-300">DT</span>
                  <span className="text-gray-400 ml-2">/ {plan.period}</span>
                </div>
              </div>

              <div className="space-y-3 mb-8">
                {plan.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <div className={`w-5 h-5 rounded-full ${plan.color} flex items-center justify-center`}>
                      <Check size={12} className="text-white" />
                    </div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <Link
                href="/signup"
                className={`btn ${plan.buttonClass} w-full justify-center ${plan.isPopular ? 'hover-glow' : ''}`}
              >
                Get Started
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-center"
        >
          <div className="glass-card p-6 max-w-3xl mx-auto">
            <p className="text-gray-300">
              All plans include a 7-day money-back guarantee. No questions asked.
              <br />
              Need help choosing? <a href="#" className="text-[#FFAD00] hover:underline">Contact our team</a> for personalized assistance.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernPricingSection;
