'use client';

import { motion } from 'framer-motion';
import { Check, Star } from 'lucide-react';
import Link from 'next/link';
import CurvedShape from '../ui/CurvedShape';

const ModernPricingSection = () => {
  const plans = [
    {
      name: "Individuel",
      price: "29",
      period: "mois",
      description: "Parfait pour les débutants qui ont besoin d'outils essentiels.",
      features: [
        "Accès à 20+ outils",
        "Support de base",
        "1 outil actif à la fois",
        "Changement d'outil en 7 jours"
      ],
      isPopular: false,
      color: "bg-[rgba(79,142,255,0.1)]",
      borderColor: "border-[rgba(79,142,255,0.2)]",
      buttonClass: "btn-outline"
    },
    {
      name: "Freelance & Agences",
      price: "59",
      period: "mois",
      description: "Notre plan le plus populaire pour les professionnels et équipes.",
      features: [
        "Accès à 80+ outils",
        "Support prioritaire",
        "3 outils actifs simultanément",
        "Changement d'outil en 3 jours",
        "Catégories d'outils premium"
      ],
      isPopular: true,
      color: "bg-[rgba(255,173,0,0.1)]",
      borderColor: "border-[rgba(255,173,0,0.2)]",
      buttonClass: "btn-primary"
    },
    {
      name: "Équipes & Entreprises",
      price: "99",
      period: "mois",
      description: "Pour les équipes et entreprises qui ont besoin d'un accès illimité.",
      features: [
        "Accès à 120+ outils",
        "Support dédié 24/7",
        "Outils actifs illimités",
        "Changement d'outil en 1 jour",
        "Toutes les catégories premium",
        "Demandes d'outils personnalisées"
      ],
      isPopular: false,
      color: "bg-[rgba(233,74,156,0.1)]",
      borderColor: "border-[rgba(233,74,156,0.2)]",
      buttonClass: "btn-outline"
    }
  ];

  return (
    <section className="py-24 relative" id="pricing">
      {/* Blob decorations */}
      <div className="blob-decoration blob-yellow" style={{ left: '-100px', top: '30%' }}></div>
      <div className="blob-decoration blob-pink" style={{ right: '-100px', bottom: '20%' }}></div>

      {/* Curved shapes */}
      <CurvedShape position="top" color="#4f8eff" />
      <CurvedShape position="bottom" color="#e94a9c" />

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Tarifs <span className="text-[#FFAD00]">Simples</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choisissez le plan qui vous convient le mieux. Tous les plans incluent l'accès à notre extension Chrome.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16 relative pt-6">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * index, duration: 0.5 }}
              className={`${plan.isPopular ? 'accent-card scale-105 ring-2 ring-[#FFAD00] ring-opacity-50' : 'glass-card'} p-8 relative hover-card h-full flex flex-col`}
            >
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-[#E70013] to-[#c41e3a] !text-white text-xs font-bold px-4 py-2 rounded-full flex items-center gap-2 shadow-xl animate-pulse border-2 border-white whitespace-nowrap">
                    <img src="/tunisia-flag.svg" alt="Tunisia" className="w-3 h-3 flex-shrink-0" />
                    ⭐ PLUS POPULAIRE ⭐
                    <img src="/tunisia-flag.svg" alt="Tunisia" className="w-3 h-3 flex-shrink-0" />
                  </div>
                </div>
              )}

              <div className="mb-6">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <p className="text-gray-300 mb-4">{plan.description}</p>
                <div className="flex items-baseline mb-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-xl ml-1 text-gray-300">DT</span>
                  <span className="text-gray-400 ml-2">/ {plan.period}</span>
                </div>
              </div>

              <div className="space-y-3 mb-8 flex-grow">
                {plan.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <div className={`w-5 h-5 rounded-full ${plan.color} flex items-center justify-center`}>
                      <Check size={12} className="text-white" />
                    </div>
                    <span className="text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>

              <Link
                href="/signup"
                className={`btn ${plan.buttonClass} w-full justify-center ${plan.isPopular ? 'hover-glow' : ''} mt-auto`}
              >
                Commencer
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-center"
        >
          <div className="glass-card p-6 max-w-3xl mx-auto">
            <p className="text-gray-300">
              Tous les plans incluent une garantie de remboursement de 7 jours. Sans questions.
              <br />
              Besoin d'aide pour choisir ? <a href="#" className="text-[#FFAD00] hover:underline">Contactez notre équipe</a> pour une assistance personnalisée.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernPricingSection;
