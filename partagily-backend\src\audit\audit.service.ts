import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class AuditService {
  constructor(private readonly prisma: PrismaService) {}

  async createAuditLog(data: {
    action: string;
    entityType: string;
    entityId: string;
    description: string;
    metadata?: any;
  }) {
    try {
      // Convert metadata to string if it exists
      const metadataString = data.metadata ? JSON.stringify(data.metadata) : '{}';

      // Create audit log entry
      await this.prisma.auditLog.create({
        data: {
          eventType: data.action,
          userId: null, // In a real implementation, this would come from the request context
          ipAddress: null, // In a real implementation, this would come from the request
          userAgent: null, // In a real implementation, this would come from the request
          severity: 'INFO',
          details: JSON.stringify({
            entityType: data.entityType,
            entityId: data.entityId,
            description: data.description,
            metadata: data.metadata,
          }),
        },
      });
    } catch (error) {
      console.error('Error creating audit log:', error);
      // Don't throw the error to prevent disrupting the main operation
    }
  }
}
