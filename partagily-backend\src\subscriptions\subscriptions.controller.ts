import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { SubscriptionsService } from './subscriptions.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';

@ApiTags('subscriptions')
@Controller('subscriptions')
export class SubscriptionsController {
  constructor(private readonly subscriptionsService: SubscriptionsService) {}

  @Get('plans')
  @ApiOperation({ summary: 'Get all subscription plans' })
  @ApiResponse({ status: 200, description: 'Return all plans' })
  async findAllPlans() {
    return await this.subscriptionsService.findAllPlans();
  }

  @Get('plans/:name')
  @ApiOperation({ summary: 'Get plan by name' })
  @ApiResponse({ status: 200, description: 'Return plan by name' })
  async findPlanByName(@Param('name') name: string) {
    return await this.subscriptionsService.findPlanByName(name);
  }

  @UseGuards(JwtAuthGuard)
  @Get('user/:userId')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user subscriptions' })
  @ApiResponse({ status: 200, description: 'Return user subscriptions' })
  async findUserSubscriptions(@Param('userId') userId: string) {
    return await this.subscriptionsService.findUserSubscriptions(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('user/:userId/active')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get active user subscription' })
  @ApiResponse({ status: 200, description: 'Return active user subscription' })
  async findActiveUserSubscription(@Param('userId') userId: string) {
    return await this.subscriptionsService.findActiveUserSubscription(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post()
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new subscription' })
  @ApiResponse({ status: 201, description: 'Subscription created successfully' })
  async create(@Body() createSubscriptionDto: CreateSubscriptionDto) {
    return await this.subscriptionsService.create(createSubscriptionDto);
  }
}
