import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.NEXT_PUBLIC_API_URL;

    console.log(
      "API route /api/cart/items/[id] - Proxying DELETE request to backend:",
      `${backendUrl}/cart/items/${params.id}`
    );

    // Extract auth token from request headers
    const authToken = request.headers.get("authorization");

    if (!authToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/cart/items/${params.id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: authToken,
      },
      // Add a timeout
      signal: AbortSignal.timeout(5000),
    });

    // If the response is not OK, throw an error
    if (!response.ok) {
      console.error(
        "Backend API returned error:",
        response.status,
        response.statusText
      );

      // Return a more helpful error response
      return NextResponse.json(
        {
          error: "Backend API error",
          status: response.status,
          message: response.statusText || "Failed to remove item from cart",
        },
        { status: response.status }
      );
    }

    // Parse the response as JSON
    const data = await response.json();

    console.log("Backend API response received, forwarding to client");

    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Error in /api/cart/items/[id] route:", error.message);

    // Return a helpful error response
    return NextResponse.json(
      {
        error: "API proxy error",
        message: error.message || "Failed to proxy request to backend",
      },
      { status: 500 }
    );
  }
}
