'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

const StickyCTA = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show sticky CTA after scrolling down 500px
      const scrollY = window.scrollY;
      setIsVisible(scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div 
      className={`fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 p-4 shadow-lg border-t border-gray-200 dark:border-gray-700 transition-transform duration-300 md:hidden z-50 ${
        isVisible ? 'translate-y-0' : 'translate-y-full'
      }`}
    >
      <div className="flex justify-between items-center">
        <div className="text-sm font-medium">
          <p className="text-gray-700 dark:text-gray-300">Ready to get started?</p>
          <p className="text-[#FFAD00] font-bold">No international payments needed!</p>
        </div>
        <Link
          href="/signup"
          className="btn btn-primary px-4 py-2 text-sm hover-glow whitespace-nowrap"
        >
          Try it now ⚡️
        </Link>
      </div>
    </div>
  );
};

export default StickyCTA;
