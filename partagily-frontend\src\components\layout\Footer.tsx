'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import './footer-styles.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="footer">
      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl font-bold mb-4 terminal-text text-gray-800 dark:text-white">
              Parta<span style={{ color: '#FFAD00' }}>gily</span>
            </h2>

            <div className="flex flex-col md:flex-row justify-center items-center gap-6 mb-8">
              <p className="text-gray-500">
                &copy; {currentYear} Partagily. Tous droits réservés.
              </p>
              <span className="text-gray-500 hidden md:block">|</span>
              <span className="text-gray-500">Made with ❤️ in Tunisia 🇹🇳</span>
            </div>
          </motion.div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
