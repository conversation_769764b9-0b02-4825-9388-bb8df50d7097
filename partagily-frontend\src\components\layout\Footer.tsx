'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import './footer-styles.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    {
      title: "Product",
      links: [
        { name: "Features", href: "#features" },
        { name: "Pricing", href: "#pricing" },
        { name: "Tools", href: "/tools" },
        { name: "FAQ", href: "/faq" }
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About Us", href: "/about" },
        { name: "Contact", href: "/contact" },
        { name: "Careers", href: "/careers" },
        { name: "Blog", href: "/blog" }
      ]
    },
    {
      title: "Legal",
      links: [
        { name: "Terms of Service", href: "/terms" },
        { name: "Privacy Policy", href: "/privacy" },
        { name: "Cookie Policy", href: "/cookies" },
        { name: "Refund Policy", href: "/refunds" }
      ]
    }
  ];

  const socialLinks = [
    { name: "WhatsApp", icon: "💬", href: "https://wa.me/21612345678", priority: true },
    { name: "Facebook", icon: "👥", href: "https://facebook.com" },
    { name: "Instagram", icon: "📸", href: "https://instagram.com" },
    { name: "LinkedIn", icon: "💼", href: "https://linkedin.com" }
  ];

  return (
    <footer className="footer">
      {/* Subtle background decorations */}
      <div className="absolute top-20 left-10 w-64 h-64 bg-blue-100 dark:bg-blue-900 rounded-full opacity-5 blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-64 h-64 bg-yellow-100 dark:bg-yellow-900 rounded-full opacity-5 blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl font-bold mb-4 terminal-text text-gray-800 dark:text-white">
                Parta<span style={{ color: '#FFAD00' }}>gily</span>
              </h2>
              <p className="text-gray-400 mb-4 max-w-md">
                The Tunisian-friendly shared account platform that makes premium tools accessible with local payment methods.
              </p>
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-icon"
                    aria-label={social.name}
                  >
                    <span>{social.icon}</span>
                  </a>
                ))}
              </div>
            </motion.div>
          </div>

          {footerLinks.map((column, columnIndex) => (
            <motion.div
              key={columnIndex}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 * columnIndex, duration: 0.5 }}
            >
              <h3 className="text-lg font-bold mb-4 terminal-text">{column.title}</h3>
              <ul className="space-y-2">
                {column.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="footer-link"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* WhatsApp Support Section */}
        <div className="pt-8 border-t border-gray-200 dark:border-gray-800">
          <div className="bg-gradient-to-r from-[rgba(233,74,156,0.1)] to-[rgba(255,173,0,0.1)] p-6 rounded-2xl text-center">
            <h3 className="text-xl font-bold mb-2">Besoin d'aide ? Contactez-nous sur WhatsApp</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Support rapide en français et en arabe 🇹🇳
            </p>
            <a
              href="https://wa.me/21612345678"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 bg-[#25D366] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#20b358] transition-colors duration-200"
            >
              <span className="text-xl">💬</span>
              Ouvrir WhatsApp
            </a>
          </div>
        </div>

        <div className="pt-8 border-t border-gray-200 dark:border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 mb-4 md:mb-0">
            &copy; {currentYear} Partagily. Tous droits réservés.
          </p>

          <div className="flex flex-wrap justify-center gap-4">
            <span className="text-gray-500">Made with ❤️ in Tunisia 🇹🇳</span>
            <span className="text-gray-500">|</span>
            <Link href="/sitemap" className="footer-link">
              Plan du site
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
