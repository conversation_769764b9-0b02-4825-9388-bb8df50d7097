// Enhanced content script for Netflix with anti-inspection features
// This script runs on netflix.com pages to verify the cookies are working and prevent inspection

console.log('Partagily: Netflix content script loaded');

// Anti-inspection measures for Netflix
function applyAntiInspectionMeasures() {
  // Disable right-click
  document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
    return false;
  }, { capture: true });

  // Disable keyboard shortcuts
  document.addEventListener('keydown', function(e) {
    // Prevent F12
    if (e.key === 'F12' || e.keyCode === 123) {
      e.preventDefault();
      return false;
    }

    // Prevent Ctrl+Shift+I / Cmd+Option+I
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'I' || e.key === 'i' || e.keyCode === 73)) {
      e.preventDefault();
      return false;
    }

    // Prevent Ctrl+Shift+J / Cmd+Option+J
    if ((e.ctrlKey || e.meta<PERSON>ey) && e.shiftKey && (e.key === 'J' || e.key === 'j' || e.keyCode === 74)) {
      e.preventDefault();
      return false;
    }

    // Prevent Ctrl+Shift+C / Cmd+Option+C
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && (e.key === 'C' || e.key === 'c' || e.keyCode === 67)) {
      e.preventDefault();
      return false;
    }

    // Prevent Ctrl+U / Cmd+U (view source)
    if ((e.ctrlKey || e.metaKey) && (e.key === 'U' || e.key === 'u' || e.keyCode === 85)) {
      e.preventDefault();
      return false;
    }
  }, { capture: true });

  // Inject anti-debugging script
  const script = document.createElement('script');
  script.textContent = `
    // Detect DevTools
    function detectDevTools() {
      const widthThreshold = window.outerWidth - window.innerWidth > 160;
      const heightThreshold = window.outerHeight - window.innerHeight > 160;
      return widthThreshold || heightThreshold;
    }

    // Periodically check for DevTools
    setInterval(() => {
      if (detectDevTools()) {
        console.clear();
        // Uncomment to redirect when DevTools is opened
        // window.location.href = 'https://www.netflix.com/';
      }
    }, 1000);

    // Prevent debugging
    setInterval(() => {
      debugger;
    }, 100);
  `;

  // Add the script to the page
  (document.head || document.documentElement).appendChild(script);

  // Remove the script element after it has loaded
  script.onload = function() {
    script.remove();
  };

  console.log('Anti-inspection measures applied for Netflix');
}

// Apply anti-inspection measures immediately
applyAntiInspectionMeasures();

// Check if the user is logged in
function checkNetflixLogin() {
  // Look for elements that indicate a logged-in state
  const profileSelector = document.querySelector('.profile-gate-label');
  const accountMenuButton = document.querySelector('.account-menu-item');
  const loginButton = document.querySelector('.login-button');
  const profileIcon = document.querySelector('.profile-icon');
  const loginForm = document.querySelector('form.login-form');

  if (profileSelector || accountMenuButton || profileIcon) {
    console.log('Partagily: Netflix login successful!');

    // Send message to background script
    chrome.runtime.sendMessage({
      action: 'loginStatus',
      site: 'netflix',
      status: 'success',
      details: {
        timestamp: new Date().toISOString(),
        url: window.location.href
      }
    });

    // Show success notification
    showNotification('success', 'Netflix login successful! Enjoy your content.');

    // If on profile selection page, select the first profile
    if (profileSelector) {
      selectFirstProfile();
    }
  } else if (loginButton || loginForm) {
    console.log('Partagily: Netflix login page detected, attempting to inject cookies');

    // Send message to background script to inject cookies
    chrome.runtime.sendMessage({
      action: 'injectCookies',
      domain: 'netflix.com',
      forceRefresh: true
    }, (response) => {
      if (response && response.injected) {
        console.log('Cookies injected successfully, refreshing page');
        showNotification('success', 'Cookies injected successfully. Refreshing page...');

        // Refresh the page after a short delay
        setTimeout(() => {
          window.location.reload();
        }, 1500);
      } else {
        console.log('Cookie injection failed or not confirmed');
        showNotification('error', 'Netflix login failed. Please try injecting cookies again.');

        // Send failed status to background script
        chrome.runtime.sendMessage({
          action: 'loginStatus',
          site: 'netflix',
          status: 'failed',
          details: {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            error: response?.message || 'Cookie injection failed'
          }
        });
      }
    });
  }
}

// Function to select the first profile
function selectFirstProfile() {
  console.log('Attempting to select first profile');

  // Wait for profiles to load
  setTimeout(() => {
    // Try different selectors for profiles
    const profiles = document.querySelectorAll('.profile-link') ||
                    document.querySelectorAll('.profile-icon') ||
                    document.querySelectorAll('.profile');

    if (profiles && profiles.length > 0) {
      console.log(`Found ${profiles.length} profiles, selecting first one`);
      profiles[0].click();

      // Show notification
      showNotification('success', 'Profile selected automatically.');
    } else {
      console.log('No profiles found to select');
    }
  }, 1500);
}

// Show a notification
function showNotification(type, message) {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = `partagily-notification ${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span class="notification-icon">
        ${type === 'success' ? '✓' : '✗'}
      </span>
      <span class="notification-message">${message}</span>
    </div>
    <button class="notification-close">×</button>
  `;

  // Add styles
  const style = document.createElement('style');
  style.textContent = `
    .partagily-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 16px;
      border-radius: 8px;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      font-family: Arial, sans-serif;
      max-width: 320px;
      animation: slideIn 0.3s ease-out;
    }

    .partagily-notification.success {
      background-color: #06d6a0;
      color: white;
    }

    .partagily-notification.error {
      background-color: #ef476f;
      color: white;
    }

    .notification-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .notification-icon {
      font-size: 18px;
      font-weight: bold;
    }

    .notification-message {
      font-size: 14px;
    }

    .notification-close {
      background: none;
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      margin-left: 12px;
    }

    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `;

  // Add to document
  document.head.appendChild(style);
  document.body.appendChild(notification);

  // Add close button event listener
  notification.querySelector('.notification-close').addEventListener('click', () => {
    notification.remove();
  });

  // Auto-remove after 5 seconds
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease-in forwards';
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 5000);
}

// Wait for page to load completely
window.addEventListener('load', () => {
  // Wait a bit to ensure all elements are loaded
  setTimeout(checkNetflixLogin, 2000);
});

// Listen for messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'checkLoginStatus') {
    checkNetflixLogin();
    sendResponse({ received: true });
  }
});
