import { Module, forwardRef } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { WebhookController } from './webhook.controller';
import { PaymentCancelController } from './payment-cancel.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { OrderHistoryModule } from '../order-history/order-history.module';
import { SubscriptionsModule } from '../subscriptions/subscriptions.module';
import { ConfigModule } from '@nestjs/config';
import { CartModule } from '../cart/cart.module';

@Module({
  imports: [
    PrismaModule,
    OrderHistoryModule,
    SubscriptionsModule,
    ConfigModule,
    forwardRef(() => CartModule),
  ],
  controllers: [PaymentController, WebhookController, PaymentCancelController],
  providers: [PaymentService],
  exports: [PaymentService],
})
export class PaymentModule {}
