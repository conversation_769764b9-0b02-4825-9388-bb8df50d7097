import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { PrismaService } from '../../prisma/prisma.service';

/**
 * Admin Audit Controller
 * 
 * This controller provides endpoints to view audit logs.
 */
@ApiTags('admin-audit')
@Controller('admin/audit')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminAuditController {
  constructor(private readonly prisma: PrismaService) {}
  
  /**
   * Get all audit logs with filtering and pagination
   */
  @Get()
  @ApiOperation({ summary: 'Get all audit logs' })
  @ApiResponse({ status: 200, description: 'List of audit logs' })
  @ApiQuery({ name: 'eventType', required: false })
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'severity', required: false })
  @ApiQuery({ name: 'startDate', required: false })
  @ApiQuery({ name: 'endDate', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  @ApiQuery({ name: 'sortBy', required: false })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'] })
  async getAllAuditLogs(
    @Query('eventType') eventType?: string,
    @Query('userId') userId?: string,
    @Query('severity') severity?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('sortBy') sortBy: string = 'createdAt',
    @Query('sortOrder') sortOrder: 'asc' | 'desc' = 'desc',
  ) {
    try {
      // Build filter conditions
      const where: any = {};
      
      if (eventType) {
        where.eventType = eventType;
      }
      
      if (userId) {
        where.userId = userId;
      }
      
      if (severity) {
        where.severity = severity;
      }
      
      if (startDate && endDate) {
        where.createdAt = {
          gte: new Date(startDate),
          lte: new Date(endDate),
        };
      } else if (startDate) {
        where.createdAt = {
          gte: new Date(startDate),
        };
      } else if (endDate) {
        where.createdAt = {
          lte: new Date(endDate),
        };
      }
      
      // Calculate pagination
      const skip = (page - 1) * limit;
      
      // Build sort options
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;
      
      // Get audit logs with pagination
      const auditLogs = await this.prisma.auditLog.findMany({
        where,
        skip,
        take: limit,
        orderBy,
      });
      
      // Parse details JSON for each audit log
      const parsedAuditLogs = auditLogs.map(log => ({
        ...log,
        details: JSON.parse(log.details),
      }));
      
      // Get total count for pagination
      const total = await this.prisma.auditLog.count({ where });
      
      return {
        data: parsedAuditLogs,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }
  }
  
  /**
   * Get an audit log by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get an audit log by ID' })
  @ApiResponse({ status: 200, description: 'Audit log details' })
  @ApiResponse({ status: 404, description: 'Audit log not found' })
  async getAuditLogById(@Param('id') id: string) {
    try {
      const auditLog = await this.prisma.auditLog.findUnique({
        where: { id },
      });
      
      if (!auditLog) {
        return {
          success: false,
          message: 'Audit log not found',
        };
      }
      
      // Parse details JSON
      const parsedAuditLog = {
        ...auditLog,
        details: JSON.parse(auditLog.details),
      };
      
      return {
        success: true,
        data: parsedAuditLog,
      };
    } catch (error) {
      console.error('Error fetching audit log:', error);
      throw error;
    }
  }
  
  /**
   * Get audit log statistics
   */
  @Get('stats/overview')
  @ApiOperation({ summary: 'Get audit log statistics' })
  @ApiResponse({ status: 200, description: 'Audit log statistics' })
  async getAuditLogStats() {
    try {
      // Get total audit logs
      const totalAuditLogs = await this.prisma.auditLog.count();
      
      // Get audit logs by event type
      const eventTypes = await this.prisma.auditLog.groupBy({
        by: ['eventType'],
        _count: {
          id: true,
        },
      });
      
      const auditLogsByEventType = eventTypes.map((type) => ({
        eventType: type.eventType,
        count: type._count.id,
      }));
      
      // Get audit logs by severity
      const severities = await this.prisma.auditLog.groupBy({
        by: ['severity'],
        _count: {
          id: true,
        },
      });
      
      const auditLogsBySeverity = severities.map((severity) => ({
        severity: severity.severity,
        count: severity._count.id,
      }));
      
      // Get recent audit logs (last 24 hours)
      const recentAuditLogs = await this.prisma.auditLog.count({
        where: {
          createdAt: {
            gte: new Date(new Date().setHours(new Date().getHours() - 24)),
          },
        },
      });
      
      // Get audit logs by user (top 5)
      const userAuditLogs = await this.prisma.auditLog.groupBy({
        by: ['userId'],
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 5,
        where: {
          userId: {
            not: null,
          },
        },
      });
      
      const auditLogsByUser = await Promise.all(
        userAuditLogs.map(async (log) => {
          const user = await this.prisma.user.findUnique({
            where: { id: log.userId },
            select: {
              id: true,
              name: true,
              email: true,
            },
          });
          
          return {
            userId: log.userId,
            userName: user ? user.name : 'Unknown',
            userEmail: user ? user.email : 'Unknown',
            count: log._count.id,
          };
        })
      );
      
      return {
        totalAuditLogs,
        recentAuditLogs,
        auditLogsByEventType,
        auditLogsBySeverity,
        auditLogsByUser,
      };
    } catch (error) {
      console.error('Error fetching audit log stats:', error);
      throw error;
    }
  }
}
