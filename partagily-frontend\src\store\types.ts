export type ToolType = {
  id: string;
  name: string;
  description: string;
  icon: string;
  price: number;
  category: string;
  status: string;
  requiredPlan: string;
  createdAt: string;
  updatedAt: string;
  domain: string;
  originalPrice: number;
  plans: [];
};

export type ToolsStoreType = {
  tools: ToolType[];
  initTools: () => Promise<void>;
};

export type CartStoreType = {
  cartItems: any[];
  isCartOpen: boolean;
  totalAmount: number;
  addItemCart: (newItems: any[]) => void;
  handleToggleCart: (value: boolean) => void;
  removeItemCart: (id: string) => void;
  calculateAmount: () => void;
};
