import { PrismaClient, ToolCategory, Tool<PERSON>tatus, PlanTier } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import { parse } from 'csv-parse/sync';

const prisma = new PrismaClient();

interface ToolCSVInput {
  id: string;
  name: string;
  description: string;
  icon: string;
  price: string;
  category: string;
  status: string;
  requiredPlan: string;
  createdAt: string;
  updatedAt: string;
  domain: string;
  originalPrice: string;
}

/**
 * Validates and maps the category string to a valid ToolCategory enum value
 */
function mapCategory(category: string): ToolCategory {
  // Special case for AI
  if (category.toLowerCase() === 'ai') {
    return ToolCategory.ai;
  }

  // Convert to title case for consistency
  const formattedCategory = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();

  // Check if it's a valid category
  if (Object.values(ToolCategory).includes(formattedCategory as ToolCategory)) {
    return formattedCategory as Tool<PERSON>ategory;
  }

  throw new Error(`Invalid category: ${category}. Valid categories are: ${Object.values(ToolCategory).join(', ')}`);
}

/**
 * Validates and maps the status string to a valid ToolStatus enum value
 */
function mapStatus(status: string = 'AVAILABLE'): ToolStatus {
  const upperStatus = status.toUpperCase();

  if (Object.values(ToolStatus).includes(upperStatus as ToolStatus)) {
    return upperStatus as ToolStatus;
  }

  throw new Error(`Invalid status: ${status}. Valid statuses are: ${Object.values(ToolStatus).join(', ')}`);
}

/**
 * Validates and maps the plan tier string to a valid PlanTier enum value
 */
function mapPlanTier(tier: string): PlanTier {
  const upperTier = tier.toUpperCase();

  if (Object.values(PlanTier).includes(upperTier as PlanTier)) {
    return upperTier as PlanTier;
  }

  throw new Error(`Invalid plan tier: ${tier}. Valid tiers are: ${Object.values(PlanTier).join(', ')}`);
}

/**
 * Imports tools from a CSV file into the database
 */
async function importToolsFromCSV(filePath: string) {
  try {
    // Read the CSV file
    const fileContent = fs.readFileSync(path.resolve(filePath), 'utf8');

    // Parse the CSV content
    const records = parse(fileContent, {
      columns: false,
      skip_empty_lines: true
    });

    console.log(`Found ${records.length} tools to import`);

    // Process each tool
    for (const record of records) {
      try {
        // Map CSV columns to tool properties
        const [
          id,
          name,
          description,
          icon,
          price,
          category,
          status,
          requiredPlan,
          createdAt,
          updatedAt,
          domain,
          originalPrice
        ] = record;

        // Skip if any required field is missing
        if (!name || !description || !price || !category || !requiredPlan) {
          console.error(`Skipping record with incomplete data: ${record}`);
          continue;
        }

        // Map the input data to the correct types
        const toolData = {
          name,
          description,
          icon,
          price: parseFloat(price),
          category: mapCategory(category),
          status: mapStatus(status),
          requiredPlan: mapPlanTier(requiredPlan),
          domain,
          originalPrice: originalPrice ? parseFloat(originalPrice) : undefined,
        };

        // Check if the tool already exists
        const existingTool = await prisma.tool.findFirst({
          where: {
            OR: [
              { id },
              { name }
            ]
          },
        });

        if (existingTool) {
          // Update existing tool
          const updatedTool = await prisma.tool.update({
            where: {
              id: existingTool.id,
            },
            data: toolData,
          });
          console.log(`Updated tool: ${updatedTool.name}`);
        } else {
          // Create new tool with the provided ID
          const newTool = await prisma.tool.create({
            data: {
              id,
              ...toolData,
            },
          });
          console.log(`Created tool: ${newTool.name}`);
        }
      } catch (error) {
        console.error(`Error processing tool record:`, error);
      }
    }

    console.log('Tool import completed');
  } catch (error) {
    console.error('Error importing tools:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Check if a file path was provided
if (process.argv.length < 3) {
  console.error('Please provide a path to the CSV file containing tools data');
  console.error('Usage: npx ts-node scripts/import-tools-csv.ts <path-to-csv-file>');
  process.exit(1);
}

// Run the import
importToolsFromCSV(process.argv[2]);
