-- AlterTable
ALTER TABLE "tools" ADD COLUMN     "domain" TEXT;

-- CreateTable
CREATE TABLE "cookies" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "domain" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "path" TEXT NOT NULL DEFAULT '/',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "isSecure" BOOLEAN NOT NULL DEFAULT true,
    "isHttpOnly" BOOLEAN NOT NULL DEFAULT false,
    "sameSite" TEXT NOT NULL DEFAULT 'lax',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "cookies_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "cookies_toolId_idx" ON "cookies"("toolId");

-- AddForeignKey
ALTER TABLE "cookies" ADD CONSTRAINT "cookies_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "tools"("id") ON DELETE CASCADE ON UPDATE CASCADE;
