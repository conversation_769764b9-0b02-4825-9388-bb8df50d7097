/* Cart overlay styles */

/* Force scrollbar to always be visible to prevent layout shifts */
html {
  overflow-y: scroll !important;
}

/* When cart is open, lock scrolling but maintain layout */
html.cart-open {
  /* Keep the scrollbar visible but disable scrolling */
  overflow-y: scroll !important;
  overflow-x: hidden !important;
}

/* Keep body completely normal to prevent layout shifts */
body.cart-open {
  /* Don't change any properties that would affect layout */
  overflow: auto !important;
  position: static !important;
  width: 100% !important;
  height: auto !important;
  /* No margin or padding changes */
  margin: 0 !important;
  padding: 0 !important;
  /* Ensure no transform or transition */
  transform: none !important;
  transition: none !important;
}

/* Ensure the sidebar remains visible and interactive */
@media (min-width: 1024px) {
  body.cart-open .sidebar {
    z-index: 9000;
    position: relative;
  }
}

/* Improve backdrop blur performance */
.cart-backdrop {
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: all;
  /* Ensure the backdrop doesn't affect layout */
  margin: 0 !important;
  padding: 0 !important;
  transform: none !important;
  transition: opacity 0.2s ease !important;
}

/* Ensure cart drawer has proper stacking context */
.cart-drawer {
  isolation: isolate;
  z-index: 10000 !important;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.15) !important;
  position: fixed !important;
  pointer-events: all !important;
  /* Ensure the drawer doesn't affect layout */
  transform-origin: right center !important;
  will-change: transform !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Mobile cart drawer styles */
@media (max-width: 768px) {
  .cart-drawer {
    height: 80vh !important;
    border-radius: 16px 16px 0 0 !important;
    box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.15) !important;
  }
}

/* Prevent duplicate cart headers */
.cart-drawer .cart-header {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: inherit;
}

/* Prevent the main content container from shifting when cart is open */
body.cart-open .flex-1.lg\:pl-64,
body.cart-open div[class*="flex flex-col flex-1 lg:pl-64"],
body.cart-open > div > div.flex.flex-col.flex-1.lg\:pl-64,
body.cart-open #dashboard-main-content,
body.cart-open #dashboard-root .flex-1.lg\:pl-64 {
  /*padding-left: 16rem !important;  64px = 16rem */
  margin-right: 0 !important;
  margin-left: 0 !important;
  width: auto !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  transform: none !important;
  transition: none !important;
  max-width: 100% !important;
}

/* Fix for dashboard main content shifting when cart opens */
#dashboard-main-content {
  transition: none !important;
  transform: none !important;
  width: auto !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
  padding-left: 16rem !important;/*  64px = 16rem */
  margin-right: 0 !important;
  margin-left: 0 !important;
  max-width: 100% !important;
}

/* Ensure the cart drawer doesn't push content */
.cart-drawer {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 10000 !important;
  width: 360px !important;
  max-width: 100% !important;
  height: 100% !important;
  transform: none !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
}

/* Mobile cart drawer styles - override the default */
@media (max-width: 768px) {
  .cart-drawer {
    top: auto !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 80vh !important;
    transform: none !important;
    border-radius: 16px 16px 0 0 !important;
    box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.15) !important;
  }
}

/* Animations for cart drawer and backdrop */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideRightIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideUpIn {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
