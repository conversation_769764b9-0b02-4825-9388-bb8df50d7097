import { ApiProperty } from '@nestjs/swagger';

export class CookieDto {
  @ApiProperty({ description: 'Cookie name' })
  name: string;

  @ApiProperty({ description: 'Cookie value' })
  value: string;

  @ApiProperty({ description: 'Cookie domain', required: false })
  domain?: string;

  @ApiProperty({ description: 'Cookie path', required: false, default: '/' })
  path?: string;

  @ApiProperty({ description: 'Whether the cookie is secure', required: false, default: true })
  secure?: boolean;

  @ApiProperty({ description: 'Whether the cookie is HTTP only', required: false, default: false })
  httpOnly?: boolean;

  @ApiProperty({ description: 'SameSite attribute', required: false, enum: ['strict', 'lax', 'none'], default: 'lax' })
  sameSite?: 'strict' | 'lax' | 'none';

  @ApiProperty({ description: 'Cookie expiration date', required: false })
  expiresAt?: string;
}

export class ToolCookiesResponseDto {
  @ApiProperty({ description: 'Tool ID' })
  toolId: string;

  @ApiProperty({ description: 'Tool name' })
  name: string;

  @ApiProperty({ description: 'Tool URL' })
  url: string;

  @ApiProperty({ description: 'Cookies for the tool', type: [CookieDto] })
  cookies: CookieDto[];
}
