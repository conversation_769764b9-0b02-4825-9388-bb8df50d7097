import { ApiProperty } from '@nestjs/swagger';

export class PaymentResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'A message describing the result',
    example: 'Payment initiated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'The response data',
    example: {
      orderId: '123e4567-e89b-12d3-a456-426614174000',
      orderNumber: 'ORD-123456',
      paymentUrl: 'https://payment-gateway.com/pay/123456',
      status: 'PROCESSING',
    },
    required: false,
  })
  data?: {
    orderId?: string;
    orderNumber?: string;
    paymentUrl?: string;
    status?: string;
  };

  @ApiProperty({
    description: 'Error message in case of failure',
    example: 'Payment gateway error',
    required: false,
  })
  error?: string;
}
