import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Order } from '../../payments/entities/order.entity';

export enum ToolCategory {
  DESIGN = 'DESIGN',
  PRODUCTIVITY = 'PRODUCTIVITY',
  WRITING = 'WRITING',
  AI = 'AI',
  DEVELOPMENT = 'DEVELOPMENT',
  MARKETING = 'MARKETING',
  EDUCATION = 'EDUCATION',
  ENTERTAINMENT = 'ENTERTAINMENT',
  OTHER = 'OTHER',
}

export enum ToolStatus {
  AVAILABLE = 'AVAILABLE',
  UNAVAILABLE = 'UNAVAILABLE',
  MAINTENANCE = 'MAINTENANCE',
  COMING_SOON = 'COMING_SOON',
}

export enum PlanTier {
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  GOLD = 'GOLD',
}

@Entity('tools')
export class Tool {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  description: string;

  @Column({ nullable: true })
  icon: string;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column({
    type: 'enum',
    enum: ToolCategory,
  })
  category: ToolCategory;

  @Column({
    type: 'enum',
    enum: ToolStatus,
    default: ToolStatus.AVAILABLE,
  })
  status: ToolStatus;

  @Column({
    type: 'enum',
    enum: PlanTier,
  })
  requiredPlan: PlanTier;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Order, order => order.tool)
  orders: Order[];
}
