import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { clientScript } from './client';
import { tools } from './sync/route';

// Import the tools array from the sync route
// This allows us to share the same tools array across all API routes

export async function GET(request: NextRequest) {
  // Parse query parameters
  const url = new URL(request.url);
  const search = url.searchParams.get('search') || '';
  const category = url.searchParams.get('category') || '';
  const isActiveParam = url.searchParams.get('isActive');
  const isActive = isActiveParam ? isActiveParam === 'true' : undefined;
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '10');
  const sortBy = url.searchParams.get('sortBy') || 'createdAt';
  const sortOrder = url.searchParams.get('sortOrder') || 'DESC';

  // Filter tools based on query parameters
  let filteredTools = tools;

  if (search) {
    filteredTools = filteredTools.filter(tool =>
      tool.name.toLowerCase().includes(search.toLowerCase()) ||
      tool.description.toLowerCase().includes(search.toLowerCase())
    );
  }

  if (category) {
    filteredTools = filteredTools.filter(tool =>
      tool.category === category
    );
  }

  if (isActive !== undefined) {
    filteredTools = filteredTools.filter(tool =>
      tool.isActive === isActive
    );
  }

  // Sort tools
  filteredTools.sort((a, b) => {
    if (sortOrder === 'ASC') {
      return a[sortBy] > b[sortBy] ? 1 : -1;
    } else {
      return a[sortBy] < b[sortBy] ? 1 : -1;
    }
  });

  // Paginate tools
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedTools = filteredTools.slice(startIndex, endIndex);

  // Include the client script in the response to sync localStorage with the server
  const headers = new Headers();
  headers.set('Content-Type', 'application/json');

  // Return the tools as JSON
  return NextResponse.json({
    tools: paginatedTools,
    total: filteredTools.length
  });
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // Create a new tool with a unique ID
    const newTool = {
      id: uuidv4(),
      ...data,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to our mock database
    tools.push(newTool);

    // Return success response as JSON
    return NextResponse.json({
      message: 'Tool created successfully',
      tool: newTool
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating tool:', error);
    return NextResponse.json({
      message: 'Failed to create tool'
    }, { status: 500 });
  }
}
