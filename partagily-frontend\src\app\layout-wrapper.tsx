'use client';

import { usePathname } from 'next/navigation';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import CartDrawer from '@/components/dashboard/CartDrawer';
import NotificationBanner from '@/components/common/NotificationBanner';
import WhatsAppWidget from '@/components/common/WhatsAppWidget';

export default function LayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Check if the current path is a dashboard route
  const isDashboardRoute = pathname?.startsWith('/dashboard');

  return (
    <>
      {/* Notification banner appears on all routes */}
      <NotificationBanner />

      {/* Only render Navbar and Footer for non-dashboard routes */}
      {!isDashboardRoute && <Navbar />}
      {children}
      {!isDashboardRoute && <Footer />}

      {/* Cart drawer is available on all routes */}
      <CartDrawer />

      {/* WhatsApp widget appears on all routes */}
      <WhatsAppWidget />
    </>
  );
}
