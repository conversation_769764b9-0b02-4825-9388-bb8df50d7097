# Partagily Project Rules

## Cookie Management Patterns
- Use secure storage for cookie data
- Implement encryption for sensitive cookie information
- Handle cookie expiration and refresh
- Validate cookie integrity before injection
- Use declarativeNetRequest API where possible

## Authentication Flows
- JWT-based authentication
- Secure token storage
- Refresh token rotation
- Role-based access control
- Session management

## Component Communication Strategies
- REST API for frontend-backend communication
- Message passing for extension components
- Event-driven architecture for real-time updates
- Typed interfaces for all communication

## Code Style and Organization
- TypeScript for all components
- Consistent naming conventions
- Modular architecture
- Component-based UI design
- Domain-driven backend design

## Architecture Decisions
- Next.js for frontend (SSR capabilities)
- NestJS for backend (modular architecture)
- Manifest V3 for Chrome extension (future compatibility)
- PostgreSQL for data persistence
- Prisma for type-safe database access

## Known Challenges and Solutions
- Cookie security: Implement encryption and secure storage
- Cross-browser compatibility: Focus on Chrome first, expand later
- Performance: Optimize cookie injection process
- Security: Implement proper authentication and authorization

## Performance Optimization Techniques
- Lazy loading for frontend components
- Efficient database queries
- Caching strategies
- Minimizing extension resource usage

## Testing Strategies
- Unit testing for all components
- Integration testing for API endpoints
- E2E testing for critical user flows
- Manual testing for extension functionality
