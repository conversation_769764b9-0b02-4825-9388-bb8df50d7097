'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import toolService from '@/services/toolService';
import { useNotification } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';

export default function ToolDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { showNotification } = useNotification();
  const { user } = useAuth();
  
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [extensionInstalled, setExtensionInstalled] = useState(false);
  const [accessStatus, setAccessStatus] = useState<string | null>(null);

  // Check if the extension is installed
  useEffect(() => {
    const checkExtension = () => {
      if (window.partagilyExtension) {
        setExtensionInstalled(true);
      } else {
        setExtensionInstalled(false);
      }
    };

    // Check immediately
    checkExtension();

    // Set up a listener for the extension
    window.addEventListener('partagilyExtensionLoaded', checkExtension);

    return () => {
      window.removeEventListener('partagilyExtensionLoaded', checkExtension);
    };
  }, []);

  // Fetch tool details
  useEffect(() => {
    const fetchToolDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        const toolId = Array.isArray(id) ? id[0] : id;
        const data = await toolService.getToolById(toolId);
        
        setTool(data);
      } catch (error: any) {
        console.error('Error fetching tool details:', error);
        setError(error.message || 'Failed to load tool details');
        
        showNotification('error', error.message || 'Failed to load tool details', {
          autoClose: true
        });
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchToolDetails();
    }
  }, [id, showNotification]);

  // Function to handle "Get Access" button click
  const handleGetAccess = async () => {
    if (!tool) return;
    
    try {
      setAccessStatus('processing');
      
      // Check if user is authenticated
      if (!user) {
        router.push('/signin');
        return;
      }
      
      // Check if extension is installed
      if (!extensionInstalled) {
        setAccessStatus('extension-missing');
        
        showNotification('error', 'Partagily extension is not installed', {
          autoClose: false,
          action: {
            label: 'Install',
            onClick: () => window.open('https://chrome.google.com/webstore/detail/partagily/extension-id', '_blank')
          }
        });
        
        return;
      }
      
      // Use the tool service to access the tool
      await toolService.accessTool(tool.id, tool.websiteUrl);
      
      setAccessStatus('opening');
      
      // Show a notification
      showNotification('success', `Opening ${tool.name}...`, {
        autoClose: true
      });
      
      // Reset status after 3 seconds
      setTimeout(() => {
        setAccessStatus(null);
      }, 3000);
    } catch (error: any) {
      console.error('Error accessing tool:', error);
      
      setAccessStatus('error');
      
      // Show error notification
      showNotification('error', error.message || 'Failed to access tool', {
        autoClose: true
      });
      
      // Reset status after 3 seconds
      setTimeout(() => {
        setAccessStatus(null);
      }, 3000);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-red-500 mb-4">Error Loading Tool</h2>
        <p className="text-gray-600 mb-6">{error || 'Tool not found'}</p>
        <button
          onClick={() => router.back()}
          className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-xl shadow-lg overflow-hidden"
      >
        {/* Tool Header */}
        <div className="bg-gradient-to-r from-primary to-primary-light p-6 flex flex-col md:flex-row items-center justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="bg-white p-3 rounded-lg mr-4">
              <Image
                src={tool.logoUrl || '/tools/default-logo.png'}
                alt={tool.name}
                width={64}
                height={64}
                className="rounded"
              />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-white">{tool.name}</h1>
              <p className="text-white/80">{tool.category}</p>
            </div>
          </div>
          <button
            onClick={handleGetAccess}
            disabled={accessStatus === 'processing' || accessStatus === 'opening'}
            className={`px-6 py-3 rounded-lg font-semibold transition-all ${
              accessStatus === 'processing' || accessStatus === 'opening'
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-white text-primary hover:bg-gray-100'
            }`}
          >
            {accessStatus === 'processing' ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : accessStatus === 'opening' ? (
              'Opening...'
            ) : accessStatus === 'extension-missing' ? (
              'Install Extension'
            ) : (
              'Get Access'
            )}
          </button>
        </div>

        {/* Tool Content */}
        <div className="p-6">
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-3">Description</h2>
            <p className="text-gray-700">{tool.description}</p>
          </div>

          {tool.features && tool.features.length > 0 && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-3">Features</h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {tool.features.map((feature: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <svg className="h-5 w-5 text-primary mt-0.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Extension Status */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold mb-2">Extension Status</h3>
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${extensionInstalled ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-gray-700">
                {extensionInstalled ? 'Partagily Extension is installed' : 'Partagily Extension is not installed'}
              </span>
            </div>
            {!extensionInstalled && (
              <a
                href="https://chrome.google.com/webstore/detail/partagily/extension-id"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block mt-3 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Install Extension
              </a>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
