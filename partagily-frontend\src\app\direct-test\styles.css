/* Modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600;700&display=swap');

.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.mono-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

.btn {
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s;
  transform: scale(1);
  padding: 0.75rem 1rem;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.btn:hover {
  transform: scale(1.05);
}

.btn-primary {
  background-color: #facc15;
  color: #111111;
}

.btn-primary:hover {
  background-color: #eab308;
}

.btn-secondary {
  background-color: #3b82f6;
  color: white;
}

.btn-secondary:hover {
  background-color: #2563eb;
}

.btn-outline {
  border: 2px solid #facc15;
  color: #111111;
  background-color: transparent;
}

.btn-outline:hover {
  background-color: rgba(250, 204, 21, 0.1);
}

.hover-bounce {
  transition: transform 0.3s;
}

.hover-bounce:hover {
  transform: translateY(-4px);
}

.gradient-border {
  border-bottom: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(to right, #facc15, #f472b6, #3b82f6);
}
