'use client';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, Package, FileText, Globe, Image, Tag, Plus, Trash2, Star } from 'lucide-react';

interface ClientEditToolPageProps {
  params: { id: string };
}

interface ToolPlan {
  name: string;
  price: number;
  duration: string;
  features: string[];
  isPopular: boolean;
  isActive: boolean;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  websiteUrl?: string;
  logoUrl?: string;
  category?: string;
  isActive: boolean;
  plans: ToolPlan[];
  createdAt: string;
  updatedAt: string;
}

export default function ClientEditToolPage({ params }: ClientEditToolPageProps) {
  const [formData, setFormData] = useState<Tool | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchTool = () => {
      try {
        setIsLoading(true);
        const storedTools = localStorage.getItem('partagily-tools');
        const tools = storedTools ? JSON.parse(storedTools) : [];
        const tool = tools.find((t: any) => t.id === params.id);
        if (tool) {
          setFormData(tool);
          if (tool.logoUrl) setLogoPreview(tool.logoUrl);
        } else {
          setError('Tool not found');
        }
      } catch (err: any) {
        console.error('Error fetching tool:', err);
        setError(err.message || 'Failed to load tool');
      } finally {
        setIsLoading(false);
      }
    };
    fetchTool();
  }, [params.id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (!formData) return;
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!formData) return;
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setFormData({
        ...formData,
        logoUrl: URL.createObjectURL(file),
      });
    }
  };

  const handlePlanChange = (index: number, field: string, value: any) => {
    if (!formData) return;
    const updatedPlans = [...formData.plans];
    updatedPlans[index] = {
      ...updatedPlans[index],
      [field]: value,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const handlePlanFeatureChange = (planIndex: number, featureIndex: number, value: string) => {
    if (!formData) return;
    const updatedPlans = [...formData.plans];
    const features = [...updatedPlans[planIndex].features];
    features[featureIndex] = value;
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const addPlanFeature = (planIndex: number) => {
    if (!formData) return;
    const updatedPlans = [...formData.plans];
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features: [...updatedPlans[planIndex].features, ''],
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const removePlanFeature = (planIndex: number, featureIndex: number) => {
    if (!formData) return;
    const updatedPlans = [...formData.plans];
    const features = [...updatedPlans[planIndex].features];
    features.splice(featureIndex, 1);
    updatedPlans[planIndex] = {
      ...updatedPlans[planIndex],
      features,
    };
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const addPlan = () => {
    if (!formData) return;
    setFormData({
      ...formData,
      plans: [
        ...formData.plans,
        {
          name: '',
          price: 0,
          duration: '1 month',
          features: [''],
          isPopular: false,
          isActive: true,
        },
      ],
    });
  };

  const removePlan = (index: number) => {
    if (!formData) return;
    const updatedPlans = [...formData.plans];
    updatedPlans.splice(index, 1);
    setFormData({
      ...formData,
      plans: updatedPlans,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData) return;
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage(null);
      const storedTools = localStorage.getItem('partagily-tools');
      const tools = storedTools ? JSON.parse(storedTools) : [];
      const index = tools.findIndex((t: any) => t.id === params.id);
      if (index !== -1) {
        tools[index] = {
          ...formData,
          updatedAt: new Date().toISOString(),
        };
        localStorage.setItem('partagily-tools', JSON.stringify(tools));
        setSuccessMessage('Tool updated successfully!');
        setTimeout(() => {
          router.push('/admin/tools');
        }, 2000);
      } else {
        setError('Tool not found');
      }
    } catch (err: any) {
      console.error('Error updating tool:', err);
      setError(err.message || 'Failed to update tool');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Link href="/admin/tools" className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors">
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <h1 className="text-2xl font-bold">Edit Tool</h1>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">⚠️</span>
          <div>
            <p className="font-medium">Error</p>
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!formData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Link href="/admin/tools" className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors">
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <h1 className="text-2xl font-bold">Edit Tool</h1>
        </div>
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">⚠️</span>
          <div>
            <p className="font-medium">Error</p>
            <p>Tool not found</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link href="/admin/tools" className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors">
          <ArrowLeft className="w-5 h-5" />
        </Link>
        <h1 className="text-2xl font-bold">Edit Tool: {formData.name}</h1>
      </div>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">⚠️</span>
          <div>
            <p className="font-medium">Error</p>
            <p>{error}</p>
          </div>
        </div>
      )}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-center">
          <span className="text-xl mr-2">✅</span>
          <div>
            <p className="font-medium">Success</p>
            <p>{successMessage}</p>
          </div>
        </div>
      )}
      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* ...rest of your form code, unchanged... */}
            {/* Copy all the JSX from your previous file here */}
            {/* For brevity, not repeating the entire form here, but you can copy it as-is */}
          </form>
        </div>
      </div>
    </div>
  );
}