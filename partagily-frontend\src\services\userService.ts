import axios from "axios";
import authService from "./authService";
import { isValidUUID, convertToUUID } from "@/utils/validation";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Check if we have a refresh token before trying to refresh
        const refreshToken = authService.getRefreshToken();
        if (!refreshToken) {
          console.log("No refresh token available, skipping token refresh");
          // If we're on a page that requires authentication, redirect to login
          if (typeof window !== "undefined") {
            // Import dynamically to avoid circular dependencies
            import("@/services/userStorageService").then(
              ({ default: userStorageService }) => {
                userStorageService.clearAllUserData();
              }
            );

            // Check if we're not already on the login page
            if (
              !window.location.pathname.includes("/login") &&
              !window.location.pathname.includes("/signup")
            ) {
              window.location.href = "/login";
            }
          }
          return Promise.reject(new Error("Authentication required"));
        }

        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request with new token
        const token = authService.getAccessToken();
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return api(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);

        // Clear auth data on refresh failure
        if (typeof window !== "undefined") {
          // Import dynamically to avoid circular dependencies
          import("@/services/userStorageService").then(
            ({ default: userStorageService }) => {
              userStorageService.clearAllUserData();
            }
          );

          // Check if we're not already on the login page
          if (
            !window.location.pathname.includes("/login") &&
            !window.location.pathname.includes("/signup")
          ) {
            window.location.href = "/login";
          }
        }

        // Refresh failed, redirect to login
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Define types for profile update
interface UpdateProfileData {
  name?: string;
  email?: string;
}

// Define types for password change
interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const userService = {
  // Get available tools for the user
  async getAvailableTools(filter: any = {}): Promise<any> {
    // For production, try the API
    try {
      // Import toolService dynamically to avoid circular dependencies
      const toolService = (await import("./toolService")).default;

      try {
        // Use the toolService to get tools
        const toolsData = await toolService.getAllTools();
        console.log("toolService.getAllTools returned:", toolsData);
        return toolsData;
      } catch (apiError) {
        console.warn(
          "API getAvailableTools failed, using mock data as fallback:",
          apiError
        );

        // Use mock data as fallback
        console.log("Using mock data for tools");
        const mockTools = [
          {
            id: "1",
            name: "Netflix",
            description: "Stream TV shows and movies",
            icon: "/tools/netflix.png",
            price: 9.99,
            originalPrice: 12.99,
            category: "Streaming",
            status: "AVAILABLE",
            requiredPlan: "STANDARD",
          },
          {
            id: "2",
            name: "Adobe Creative Cloud",
            description:
              "Access Photoshop, Illustrator, and more with a shared account.",
            icon: "/tools/adobe.png",
            price: 19.99,
            originalPrice: 24.99,
            category: "Design",
            status: "AVAILABLE",
            requiredPlan: "PREMIUM",
          },
          {
            id: "3",
            name: "Microsoft Office 365",
            description:
              "Use Word, Excel, PowerPoint and other Office applications.",
            icon: "/tools/office.png",
            price: 9.99,
            originalPrice: 14.99,
            category: "Writing",
            status: "AVAILABLE",
            requiredPlan: "STANDARD",
          },
          {
            id: "4",
            name: "Spotify Premium",
            description: "Ad-free music streaming with offline listening.",
            icon: "/tools/spotify.png",
            price: 9.99,
            originalPrice: 11.99,
            category: "Music",
            status: "AVAILABLE",
            requiredPlan: "STANDARD",
          },
          {
            id: "5",
            name: "ChatGPT Plus",
            description: "Priority access to OpenAI's advanced AI assistant.",
            icon: "/tools/chatgpt.png",
            price: 19.99,
            originalPrice: 22.99,
            category: "AI",
            status: "AVAILABLE",
            requiredPlan: "PREMIUM",
          },
          {
            id: "6",
            name: "Disney+",
            description: "Stream Disney, Marvel, Star Wars, and more.",
            icon: "/tools/disney.png",
            price: 9.99,
            originalPrice: 12.99,
            category: "Streaming",
            status: "AVAILABLE",
            requiredPlan: "STANDARD",
          },
        ];

        return {
          tools: mockTools,
          pagination: {
            total: mockTools.length,
            page: 1,
            limit: 10,
            totalPages: 1,
          },
        };
      }
    } catch (error: any) {
      console.error("Error fetching available tools:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get available tools"
      );
    }
  },

  // Get all tools (for user dashboard)
  async getTools(filter: any = {}): Promise<any> {
    try {
      console.log("userService.getTools called with filter:", filter);

      // Import toolService dynamically to avoid circular dependencies
      const toolService = (await import("./toolService")).default;

      // Use the toolService to get tools from the database
      console.log("Calling toolService.getAllTools from userService");
      const toolsData = await toolService.getAllTools();
      console.log("toolService.getAllTools returned:", toolsData);

      // Apply filters if any
      let filteredTools = [...(toolsData.tools || [])];

      if (filter.category) {
        filteredTools = filteredTools.filter(
          (tool) =>
            tool.category &&
            tool.category.toLowerCase() === filter.category.toLowerCase()
        );
      }

      if (filter.status) {
        filteredTools = filteredTools.filter(
          (tool) =>
            tool.status &&
            tool.status.toLowerCase() === filter.status.toLowerCase()
        );
      }

      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        filteredTools = filteredTools.filter(
          (tool) =>
            (tool.name && tool.name.toLowerCase().includes(searchLower)) ||
            (tool.description &&
              tool.description.toLowerCase().includes(searchLower))
        );
      }

      return {
        tools: filteredTools,
        pagination: {
          total: filteredTools.length,
          page: filter.page || 1,
          limit: filter.limit || 10,
          totalPages: Math.ceil(filteredTools.length / (filter.limit || 10)),
        },
      };
    } catch (error: any) {
      console.error("Error fetching tools:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get tools from the database"
      );
    }
  },

  // Get tool details
  async getToolDetails(id: string): Promise<any> {
    try {
      // Import toolService dynamically to avoid circular dependencies
      const toolService = (await import("./toolService")).default;

      // Use the toolService to get tool details from the database
      console.log(`Fetching details for tool with ID: ${id}`);
      const tool = await toolService.getToolById(id);

      if (!tool) {
        throw new Error("Tool not found");
      }

      return { tool };
    } catch (error: any) {
      console.error("Error fetching tool details:", error);
      throw new Error(
        error.response?.data?.message ||
          "Failed to get tool details from the database"
      );
    }
  },

  // Subscribe to a tool
  async subscribeTool(toolId: string, planId: string): Promise<any> {
    try {
      try {
        // Try to call the API first
        const response = await api.post("/subscriptions", {
          toolId,
          planId,
        });
        return response.data;
      } catch (apiError) {
        console.warn(
          "API subscribeTool failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        const mockSubscription = {
          id: Math.random().toString(36).substring(2, 15),
          userId: "1",
          planId: planId,
          toolId: toolId,
          startDate: new Date().toISOString(),
          endDate: new Date(
            Date.now() + 30 * 24 * 60 * 60 * 1000
          ).toISOString(), // 30 days in future
          status: "ACTIVE",
          autoRenew: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        return {
          success: true,
          message: "Subscription created successfully",
          subscription: mockSubscription,
        };
      }
    } catch (error: any) {
      console.error("Error subscribing to tool:", error);
      throw new Error(
        error.response?.data?.message || "Failed to subscribe to tool"
      );
    }
  },

  // Get user subscriptions
  async getUserSubscriptions(): Promise<any> {
    try {
      try {
        // Try to call the API first
        const response = await api.get("/subscriptions/user");
        return response.data;
      } catch (apiError) {
        console.warn(
          "API getUserSubscriptions failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        const mockSubscriptions = [
          {
            id: "1",
            planId: "1",
            plan: {
              id: "1",
              name: "Premium",
              tier: "PREMIUM",
              price: 19.99,
              description: "Enhanced access with priority support",
              features: [
                "Access to 50+ premium tools",
                "Priority support",
                "2 concurrent logins",
                "High availability",
                "Premium tool selection",
              ],
            },
            toolId: null,
            startDate: new Date(
              Date.now() - 30 * 24 * 60 * 60 * 1000
            ).toISOString(), // 30 days ago
            endDate: new Date(
              Date.now() + 335 * 24 * 60 * 60 * 1000
            ).toISOString(), // 335 days in future
            status: "ACTIVE",
            autoRenew: true,
            createdAt: new Date(
              Date.now() - 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
            updatedAt: new Date(
              Date.now() - 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
          },
          {
            id: "2",
            planId: "2",
            plan: {
              id: "2",
              name: "Netflix Premium",
              tier: "STANDARD",
              price: 9.99,
              description: "Access to Netflix premium content",
              features: [
                "Full HD streaming",
                "Watch on 2 screens at once",
                "Download on 2 devices",
                "Unlimited movies and TV shows",
              ],
            },
            toolId: "1",
            tool: {
              id: "1",
              name: "Netflix",
              description: "Stream TV shows and movies",
              icon: "/tools/netflix.png",
              category: "streaming",
            },
            startDate: new Date(
              Date.now() - 15 * 24 * 60 * 60 * 1000
            ).toISOString(), // 15 days ago
            endDate: new Date(
              Date.now() + 15 * 24 * 60 * 60 * 1000
            ).toISOString(), // 15 days in future
            status: "ACTIVE",
            autoRenew: true,
            createdAt: new Date(
              Date.now() - 15 * 24 * 60 * 60 * 1000
            ).toISOString(),
            updatedAt: new Date(
              Date.now() - 15 * 24 * 60 * 60 * 1000
            ).toISOString(),
          },
        ];

        return { subscriptions: mockSubscriptions };
      }
    } catch (error: any) {
      console.error("Error fetching user subscriptions:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get user subscriptions"
      );
    }
  },

  // Get available plans
  async getPlans(): Promise<{ plans: any[] }> {
    try {
      // Import toolService dynamically to avoid circular dependencies
      const toolService = (await import("./toolService")).default;

      try {
        // Use the toolService to get plans
        const plansData = await toolService.getAllPlans();

        // Ensure we return the expected structure
        if (plansData && Array.isArray(plansData.plans)) {
          return plansData;
        } else if (plansData && Array.isArray(plansData)) {
          // If the API returns an array directly, wrap it
          return { plans: plansData };
        } else {
          console.warn("Unexpected plans data format:", plansData);
          throw new Error("Unexpected plans data format");
        }
      } catch (apiError) {
        console.warn(
          "API getPlans failed, using mock data for development:",
          apiError
        );

        // Mock data for development - directly match the expected format in the plans page
        const mockPlans = [
          {
            id: "1",
            name: "Streaming Plan",
            description:
              "Access Netflix, Disney+, Spotify, and more streaming services",
            price: 19.99,
            features: [
              "Access to all streaming services",
              "HD and 4K streaming",
              "Multiple device support",
              "No ads experience",
            ],
            includedTools: [
              { id: "1", name: "Netflix" },
              { id: "5", name: "Disney+" },
              { id: "4", name: "Spotify Premium" },
            ],
          },
          {
            id: "2",
            name: "AI Tools Plan",
            description:
              "Access premium AI tools for productivity and creativity",
            price: 29.99,
            features: [
              "Access to all AI tools",
              "Priority processing",
              "Advanced features",
              "Higher usage limits",
            ],
            includedTools: [{ id: "6", name: "ChatGPT Plus" }],
          },
          {
            id: "3",
            name: "eLearning Plan",
            description: "Access premium educational platforms and courses",
            price: 24.99,
            features: [
              "Access to all learning platforms",
              "Downloadable course materials",
              "Certificates of completion",
              "Community support",
            ],
          },
          {
            id: "4",
            name: "Design Plan",
            description: "Access premium design tools and assets",
            price: 29.99,
            features: [
              "Access to all design tools",
              "Premium asset libraries",
              "Cloud storage",
              "Collaboration features",
            ],
            includedTools: [{ id: "2", name: "Adobe Creative Cloud" }],
          },
          {
            id: "5",
            name: "Dev Plan",
            description: "Access premium development tools and services",
            price: 24.99,
            features: [
              "Access to all development tools",
              "Cloud hosting",
              "CI/CD pipelines",
              "Private repositories",
            ],
          },
          {
            id: "6",
            name: "Full Access Plan",
            description: "Access all premium tools and services",
            price: 49.99,
            features: [
              "Access to all tools and services",
              "Priority support",
              "Early access to new tools",
              "Exclusive discounts",
            ],
            includedTools: [
              { id: "1", name: "Netflix" },
              { id: "2", name: "Adobe Creative Cloud" },
              { id: "3", name: "Microsoft Office 365" },
              { id: "4", name: "Spotify Premium" },
              { id: "5", name: "Disney+" },
              { id: "6", name: "ChatGPT Plus" },
            ],
          },
        ];

        return { plans: mockPlans };
      }
    } catch (error: any) {
      console.error("Error fetching plans:", error);

      // Even in case of error, return a valid structure with default plans
      const defaultPlans = [
        {
          id: "1",
          name: "Streaming Plan",
          description:
            "Access Netflix, Disney+, Spotify, and more streaming services",
          price: 19.99,
          features: [
            "Access to all streaming services",
            "HD and 4K streaming",
            "Multiple device support",
            "No ads experience",
          ],
        },
        {
          id: "2",
          name: "AI Tools Plan",
          description:
            "Access premium AI tools for productivity and creativity",
          price: 29.99,
          features: [
            "Access to all AI tools",
            "Priority processing",
            "Advanced features",
            "Higher usage limits",
          ],
        },
      ];

      return { plans: defaultPlans };
    }
  },

  // Get cart
  async getCart(): Promise<any> {
    try {
      console.log("Getting cart from API");

      // Get auth token
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot get cart");
        throw new Error("Authentication required");
      }

      // Try to get cart from API
      console.log("Trying to get cart from API with token");
      const response = await api.get("/cart", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000, // Increased timeout to 10 seconds
      });

      console.log("Cart retrieved from API:", response.data);

      // Ensure the response has the expected structure
      if (response.data && typeof response.data === "object") {
        // If the response is already in the expected format
        if (response.data.items) {
          return {
            success: true,
            message: "Cart retrieved successfully",
            data: response.data,
          };
        }

        // If the response is just the cart object without the items property
        if (Array.isArray(response.data)) {
          return {
            success: true,
            message: "Cart retrieved successfully",
            data: { items: response.data },
          };
        }
      }

      // If the response doesn't have the expected structure, return an empty cart
      console.warn(
        "API response does not have the expected structure:",
        response.data
      );
      return {
        success: true,
        message: "Cart retrieved but has unexpected format",
        data: { id: "1", userId: "1", status: "OPEN", items: [] },
      };
    } catch (error: any) {
      console.error("Error fetching cart:", error);

      // Provide a more specific error message based on the error type
      if (error.response?.status === 401) {
        throw new Error("Authentication required. Please log in again.");
      } else if (error.code === "ECONNABORTED") {
        throw new Error("Request timed out. Please try again later.");
      } else {
        throw new Error(
          error.response?.data?.message ||
            "Failed to get cart. Please try again later."
        );
      }
    }
  },

  // Add to cart
  async addToCart(addToCartDto: {
    itemId: string;
    type: "TOOL" | "PLAN";
  }): Promise<any> {
    try {
      console.log("Adding to cart:", addToCartDto);

      // Get auth token
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot add to cart");
        throw new Error("Authentication required");
      }

      // Ensure itemId exists
      if (!addToCartDto.itemId) {
        console.error("Missing itemId in addToCartDto");
        throw new Error("Item ID is required.");
      }

      // Convert the itemId to a valid UUID format if it's not already
      const originalItemId = addToCartDto.itemId;
      addToCartDto.itemId = convertToUUID(addToCartDto.itemId);

      console.log(
        `Item ID converted from "${originalItemId}" to UUID format: "${addToCartDto.itemId}"`
      );

      // Double-check that the converted ID is a valid UUID
      if (!isValidUUID(addToCartDto.itemId)) {
        console.error(
          "Failed to convert itemId to valid UUID:",
          addToCartDto.itemId
        );
        throw new Error(
          "Failed to process item ID. Please try again with a different item."
        );
      }

      // Try to add item to cart using API
      const response = await api.post("/cart/items", addToCartDto, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000, // Increased timeout to 10 seconds
      });

      console.log("Add to cart API response:", response.data);

      return {
        success: true,
        message: "Item added to cart successfully",
        data: response.data,
      };
    } catch (error: any) {
      console.error("Error adding to cart:", error);

      // Provide a more specific error message based on the error type
      if (error.response?.status === 401) {
        throw new Error("Authentication required. Please log in again.");
      } else if (error.response?.status === 404) {
        throw new Error(
          "Item not found. Please try again with a different item."
        );
      } else if (error.response?.status === 400) {
        throw new Error(
          error.response?.data?.message ||
            "Invalid request. Please check your input."
        );
      } else if (error.code === "ECONNABORTED") {
        throw new Error("Request timed out. Please try again later.");
      } else {
        throw new Error(
          error.message || "Failed to add item to cart. Please try again later."
        );
      }
    }
  },

  // Remove from cart
  async removeFromCart(itemId: string): Promise<any> {
    try {
      console.log("Removing item from cart:", itemId);

      // Get auth token
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot remove from cart");
        throw new Error("Authentication required");
      }

      // Ensure itemId exists
      if (!itemId) {
        console.error("Missing itemId in removeFromCart");
        throw new Error("Item ID is required.");
      }

      // Convert the itemId to a valid UUID format if it's not already
      const originalItemId = itemId;
      itemId = convertToUUID(itemId);

      console.log(
        `Item ID converted from "${originalItemId}" to UUID format for removal: "${itemId}"`
      );

      // Double-check that the converted ID is a valid UUID
      if (!isValidUUID(itemId)) {
        console.error(
          "Failed to convert itemId to valid UUID for removal:",
          itemId
        );
        throw new Error(
          "Failed to process item ID. Please try again with a different item."
        );
      }

      // Try to remove item from cart using API
      const response = await api.delete(`/cart/items/${itemId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        timeout: 10000, // Increased timeout to 10 seconds
      });

      console.log("Remove from cart API response:", response.data);

      return {
        success: true,
        message: "Item removed from cart successfully",
        data: response.data,
      };
    } catch (error: any) {
      console.error("Error removing from cart:", error);

      // Provide a more specific error message based on the error type
      if (error.response?.status === 401) {
        throw new Error("Authentication required. Please log in again.");
      } else if (error.response?.status === 404) {
        throw new Error("Item not found in cart.");
      } else if (error.code === "ECONNABORTED") {
        throw new Error("Request timed out. Please try again later.");
      } else {
        throw new Error(
          error.message ||
            "Failed to remove item from cart. Please try again later."
        );
      }
    }
  },

  // Checkout
  async checkout(): Promise<any> {
    try {
      console.log("Starting checkout process in userService...");

      // Get auth token
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot checkout");
        throw new Error("Authentication required");
      }

      // Try to checkout using API
      const response = await api.post(
        "/cart/checkout",
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          timeout: 15000, // Longer timeout for checkout (15 seconds)
        }
      );

      console.log("Checkout API response:", response.data);

      // Store the order ID in session storage for the thank you page
      try {
        if (response.data?.id || response.data?.data?.id) {
          const orderId = response.data?.id || response.data?.data?.id;
          sessionStorage.setItem("lastOrderId", orderId);
        }
      } catch (sessionError) {
        console.warn(
          "Failed to store order ID in sessionStorage:",
          sessionError
        );
      }

      return {
        success: true,
        message: "Checkout completed successfully",
        data: response.data,
        paymentUrl:
          response.data?.paymentUrl || response.data?.data?.paymentUrl,
        cart: response.data?.cart || {
          id: "1",
          userId: "1",
          status: "OPEN",
          items: [],
        },
      };
    } catch (error: any) {
      console.error("Error during checkout:", error);

      // Provide a more specific error message based on the error type
      if (error.response?.status === 401) {
        throw new Error("Authentication required. Please log in again.");
      } else if (error.response?.status === 400) {
        throw new Error(
          error.response?.data?.message ||
            "Invalid checkout request. Please check your cart."
        );
      } else if (error.response?.status === 404) {
        throw new Error(
          "Cart not found or empty. Please add items to your cart before checkout."
        );
      } else if (error.code === "ECONNABORTED") {
        throw new Error("Checkout request timed out. Please try again later.");
      } else {
        throw new Error(
          error.response?.data?.message ||
            "Failed to complete checkout. Please try again later."
        );
      }
    }
  },

  // Update user profile
  async updateProfile(data: UpdateProfileData): Promise<any> {
    try {
      // Check if user is authenticated
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot update profile");
        throw new Error("Authentication required");
      }

      console.log("Updating user profile with data:", data);

      try {
        // Call the API to update the profile
        const response = await api.patch("/users/profile", data, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });

        console.log("Profile update response:", response.data);

        // Return the response data
        return {
          success: true,
          message: "Profile updated successfully",
          user: response.data.user,
        };
      } catch (apiError) {
        console.error("Error updating profile:", apiError);

        // If unauthorized, throw error
        if (apiError.response?.status === 401) {
          throw new Error("Authentication required");
        }

        // If email already in use
        if (apiError.response?.status === 409) {
          throw new Error("Email already in use");
        }

        // For other errors
        throw new Error(
          apiError.response?.data?.message || "Failed to update profile"
        );
      }
    } catch (error: any) {
      console.error("Error in updateProfile:", error);
      throw new Error(error.message || "Failed to update profile");
    }
  },

  // Change user password
  async changePassword(data: ChangePasswordData): Promise<any> {
    try {
      // Check if user is authenticated
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, cannot change password");
        throw new Error("Authentication required");
      }

      // Validate passwords match
      if (data.newPassword !== data.confirmPassword) {
        throw new Error("New passwords do not match");
      }

      console.log("Changing user password");

      try {
        // Call the API to change the password
        const response = await api.post("/users/change-password", data, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });

        console.log("Password change response:", response.data);

        // Return the response data
        return {
          success: true,
          message: "Password changed successfully",
        };
      } catch (apiError) {
        console.error("Error changing password:", apiError);

        // If unauthorized, throw error
        if (apiError.response?.status === 401) {
          throw new Error("Authentication required");
        }

        // If current password is incorrect
        if (apiError.response?.status === 400) {
          throw new Error(
            apiError.response?.data?.message || "Current password is incorrect"
          );
        }

        // For other errors
        throw new Error(
          apiError.response?.data?.message || "Failed to change password"
        );
      }
    } catch (error: any) {
      console.error("Error in changePassword:", error);
      throw new Error(error.message || "Failed to change password");
    }
  },

  // Get order history
  async getOrderHistory(): Promise<any> {
    try {
      // Check if user is authenticated
      const token = authService.getAccessToken();
      if (!token) {
        console.log("No access token available, returning empty order history");
        return {
          success: true,
          message: "No authentication, returning empty order history",
          data: [],
        };
      }

      try {
        // Call the API to get real order history data
        console.log("Fetching real order history data from the backend");
        const response = await api.get("/order-history", {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });

        console.log("Order history data received from backend:", response.data);

        // Check if the response has the expected structure
        if (
          response.data &&
          response.data.success &&
          Array.isArray(response.data.data)
        ) {
          return response.data;
        } else {
          // If the response doesn't have the expected structure, format it
          return {
            success: true,
            message: "Order history retrieved successfully",
            data: Array.isArray(response.data)
              ? response.data
              : response.data?.data || [],
          };
        }
      } catch (apiError) {
        console.error("Error fetching order history from backend:", apiError);

        // If unauthorized, return empty data
        if (apiError.response?.status === 401) {
          console.error("Unauthorized access to order history");
          return {
            success: false,
            message: "Authentication required to view order history",
            data: [],
          };
        }

        // For other errors, return empty data with error message
        return {
          success: false,
          message:
            apiError.response?.data?.message || "Failed to fetch order history",
          data: [],
        };
      }
    } catch (error: any) {
      console.error("Error in getOrderHistory:", error);

      // If it's an authentication error, return empty data instead of throwing
      if (
        error.message === "Authentication required" ||
        error.message === "No refresh token available" ||
        error.response?.status === 401
      ) {
        return {
          success: true,
          message: "Authentication required, returning empty order history",
          data: [],
        };
      }

      // For other errors, return empty data with error message
      return {
        success: false,
        message: error.message || "Failed to get order history",
        data: [],
      };
    }
  },
};

export default userService;
