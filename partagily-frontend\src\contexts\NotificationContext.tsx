'use client';

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import Notification from '@/components/ui/Notification';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface NotificationAction {
  label: string;
  onClick: () => void;
}

interface NotificationItem {
  id: string;
  type: NotificationType;
  message: string;
  isVisible: boolean;
  autoClose: boolean;
  action?: NotificationAction;
  createdAt: number; // timestamp for ordering
}

interface NotificationContextType {
  showNotification: (
    type: NotificationType,
    message: string,
    options?: {
      autoClose?: boolean;
      action?: NotificationAction;
    }
  ) => string; // returns notification id
  hideNotification: (id: string) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

// Generate a unique ID for notifications
const generateId = () => {
  return `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
};

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  // Clean up old notifications that might be stuck
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setNotifications(prev =>
        prev.filter(notification =>
          now - notification.createdAt < 6000 || !notification.autoClose
        )
      );
    }, 1000); // Check every 2 seconds

    return () => clearInterval(interval);
  }, []);

  const showNotification = useCallback((
    type: NotificationType,
    message: string,
    options?: {
      autoClose?: boolean;
      action?: NotificationAction;
    }
  ): string => {
    const id = generateId();
    const newNotification: NotificationItem = {
      id,
      type,
      message,
      isVisible: true,
      autoClose: options?.autoClose !== undefined ? options.autoClose : true,
      action: options?.action,
      createdAt: Date.now()
    };

    setNotifications(prev => [...prev, newNotification]);
    return id;
  }, []);

  const hideNotification = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, isVisible: false }
          : notification
      )
    );

    // Remove from array after animation completes
    setTimeout(() => {
      setNotifications(prev =>
        prev.filter(notification => notification.id !== id)
      );
    }, 1000); // Adjust this timeout to match your CSS transition duration
  }, []);

  return (
    <NotificationContext.Provider value={{ showNotification, hideNotification }}>
      {children}
      <div className="fixed bottom-6 right-6 z-50 flex flex-col-reverse gap-4 max-w-md">
        {notifications.map((notification) => (
          <Notification
            key={notification.id}
            type={notification.type}
            message={notification.message}
            isVisible={notification.isVisible}
            onClose={() => hideNotification(notification.id)}
            autoClose={notification.autoClose}
            action={notification.action}
          />
        ))}
      </div>
    </NotificationContext.Provider>
  );
};

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};
