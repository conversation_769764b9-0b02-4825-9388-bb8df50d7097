"use client";

import React, { useRef, useEffect, useState } from "react";
import { X, Shopping<PERSON>art, Trash2, CreditCard } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useMediaQuery } from "@/hooks/useMediaQuery";
// import { useCart } from "@/contexts/CartContext";
import "@/styles/cart.css";
import "@/styles/fix-layout-shift.css";
import useCart from "@/store/cart";
import { useRouter } from "next/navigation";

const CartDrawer: React.FC = () => {
  // const {
  //   cartItems,
  //   totalAmount,
  //   isCartOpen,
  //   isLoading,
  //   handleToggleCart,
  //   removeFromCart,
  //   checkout
  // } = useCart();

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const router = useRouter();

  const {
    cartItems,
    isCartOpen,
    handleToggleCart,
    removeItemCart,
    totalAmount,
    calculateAmount,
  } = useCart();

  const cartRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQuery("(max-width: 768px)");

  // Prevent background scrolling when cart is open
  useEffect(() => {
    // Get the html and body elements
    const html = document.documentElement;
    const body = document.body;

    console.log("CartDrawer useEffect - isCartOpen:", isCartOpen);
    calculateAmount();

    // Define event handlers outside the if block so they can be referenced in cleanup
    // Disable wheel events on the body
    const preventWheel = (e: WheelEvent) => {
      // Only prevent default if cartRef exists and target is not inside it
      if (
        e.target &&
        cartRef.current &&
        !(cartRef.current as Node).contains(e.target as Node)
      ) {
        e.preventDefault();
      }
    };

    // Disable touchmove events on the body
    const preventTouch = (e: TouchEvent) => {
      // Only prevent default if cartRef exists and target is not inside it
      if (
        e.target &&
        cartRef.current &&
        !(cartRef.current as Node).contains(e.target as Node)
      ) {
        e.preventDefault();
      }
    };

    if (isCartOpen) {
      console.log("Cart is open, applying styles and event listeners");
      // Store the current scroll position
      const scrollY = window.scrollY;
      body.dataset.scrollY = scrollY.toString();

      // Add cart-open class to html and body
      html.classList.add("cart-open");
      body.classList.add("cart-open");

      // Add event listeners to prevent scrolling
      window.addEventListener("wheel", preventWheel, { passive: false });
      window.addEventListener("touchmove", preventTouch, { passive: false });

      // We're now using CSS to handle all the layout fixes
      // No need to manipulate inline styles
    } else {
      // Remove classes
      html.classList.remove("cart-open");
      body.classList.remove("cart-open");

      // Restore scroll position
      const scrollY = body.dataset.scrollY || "0";
      window.scrollTo(0, parseInt(scrollY));

      // We're now using CSS to handle all the layout fixes
      // No need to reset inline styles
    }

    return () => {
      // Cleanup
      html.classList.remove("cart-open");
      body.classList.remove("cart-open");

      // Remove event listeners - need to use the same function references
      window.removeEventListener("wheel", preventWheel);
      window.removeEventListener("touchmove", preventTouch);

      // We're now using CSS to handle all the layout fixes
      // No need to reset inline styles
    };
  }, [isCartOpen]);

  // Close cart when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only close if cartRef exists and target is not inside it
      if (
        cartRef.current &&
        event.target &&
        !cartRef.current.contains(event.target as Node)
      ) {
        handleToggleCart(false);
      }
    };

    if (isCartOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCartOpen, handleToggleCart]);

  // Handle checkout
  const handleCheckout = () => {
    // Close the cart drawer
    handleToggleCart(false);
    // Navigate to the checkout page
    router.push("/checkout");
  };

  console.log("CartDrawer rendering - isCartOpen:", isCartOpen);

  return (
    <>
      {isCartOpen && (
        <>
          {/* Backdrop - using a div with fixed position that doesn't affect layout */}
          <div
            className="cart-backdrop bg-black/50 backdrop-blur-sm"
            onClick={() => handleToggleCart(false)}
            data-testid="cart-backdrop"
            style={{
              opacity: 0,
              animation: "fadeIn 0.2s forwards",
            }}
          />

          {/* Cart Drawer - using a div with fixed position that doesn't affect layout */}
          <div
            ref={cartRef}
            className={`cart-drawer ${
              isMobile
                ? "bottom-0 left-0 right-0 h-[80vh] rounded-t-xl"
                : "top-0 right-0 h-full w-[360px]"
            } bg-white dark:bg-slate-800 flex flex-col border-l border-gray-200 dark:border-gray-700`}
            data-testid="cart-drawer"
          >
            <div className="cart-header p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-slate-900">
              {isMobile && (
                <div className="absolute top-1.5 left-0 right-0 flex justify-center">
                  <div className="w-12 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full" />
                </div>
              )}
              <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <ShoppingCart size={20} className="mr-2" />
                Your Cart
              </h2>
              <button
                onClick={() => handleToggleCart(false)}
                className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                aria-label="Close cart"
              >
                <X size={20} />
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-slate-800">
              {cartItems.length === 0 ? (
                <div className="text-center py-12 bg-white dark:bg-slate-800/50 rounded-lg shadow-sm p-8">
                  <div className="mb-4 flex justify-center">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                      <ShoppingCart
                        size={32}
                        className="text-gray-400 dark:text-gray-500"
                      />
                    </div>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Your cart is empty
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Add tools or plans to get started
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {cartItems.map((item, index) => (
                    <div
                      key={item.id}
                      className="flex items-start justify-between bg-white dark:bg-slate-700/30 p-4 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200"
                      style={{
                        animation: `fadeInUp 0.2s forwards ${index * 0.05}s`,
                      }}
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 rounded-lg bg-gray-200 dark:bg-slate-600 flex items-center justify-center mr-3 overflow-hidden">
                          {item.icon ? (
                            <Image
                              src={item.icon}
                              alt={item.name}
                              width={40}
                              height={40}
                              className="object-contain"
                              onError={(e) => {
                                // If image fails to load, replace with fallback
                                (e.target as HTMLImageElement).style.display =
                                  "none";
                                const parent = (e.target as HTMLImageElement)
                                  .parentElement;
                                if (parent) {
                                  const fallback =
                                    document.createElement("div");
                                  fallback.className = "text-lg";
                                  fallback.textContent =
                                    item.type === "TOOL" ? "🔧" : "📦";
                                  parent.appendChild(fallback);
                                }
                              }}
                            />
                          ) : (
                            <div className="text-lg">
                              {item.type === "TOOL" ? "🔧" : "📦"}
                            </div>
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {item.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {item.type === "TOOL"
                              ? "Individual Tool"
                              : "Subscription Plan"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <span className="font-medium text-gray-900 dark:text-white mr-3">
                          $
                          {typeof item.price === "number"
                            ? item.price.toFixed(2)
                            : "0.00"}
                        </span>
                        <button
                          onClick={() => removeItemCart(item.id)}
                          className="p-1.5 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20 text-red-500 transition-colors"
                          aria-label="Remove item"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-900">
              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-700 dark:text-gray-300">Total</span>
                <span className="font-bold text-xl text-gray-900 dark:text-white">
                  $
                  {typeof totalAmount === "number"
                    ? totalAmount.toFixed(2)
                    : "0.00"}
                </span>
              </div>

              <div className="space-y-3">
                <button
                  onClick={handleCheckout}
                  disabled={cartItems.length === 0 || isLoading}
                  className="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-4 rounded-lg flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-md"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18} className="mr-2" />
                      Pay Securely with Konnect
                    </>
                  )}
                </button>

                <button
                  onClick={handleCheckout}
                  className="w-full bg-white dark:bg-slate-700 hover:bg-gray-100 dark:hover:bg-slate-600 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-all duration-200"
                >
                  View Full Cart
                </button>
              </div>

              <div className="mt-6 flex justify-center items-center">
                <div className="bg-white dark:bg-slate-800 p-2 rounded-lg shadow-sm">
                  <Image
                    src="https://s3.eu-west-3.amazonaws.com/konnect.network.public/logo_konnect_23a791d66b.svg"
                    alt="Konnect Payment"
                    width={120}
                    height={36}
                    className="h-8 w-auto"
                  />
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default CartDrawer;
