import axios from "axios";
import authService from "./authService";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request with new token
        const token = authService.getAccessToken();
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// User management
interface UserFilter {
  search?: string;
  role?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  page?: number;
  limit?: number;
}

interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role?: string;
  isActive?: boolean;
}

interface UpdateUserData {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
  isActive?: boolean;
}

// Tool management
interface ToolFilter {
  search?: string;
  category?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  page?: number;
  limit?: number;
}

interface ToolPlanData {
  name: string;
  price: number;
  duration: string;
  features?: string[];
  isPopular?: boolean;
  isActive?: boolean;
}

interface CreateToolData {
  name: string;
  description: string;
  websiteUrl?: string;
  logoUrl?: string;
  category?: string;
  isActive?: boolean;
  plans?: ToolPlanData[];
}

interface UpdateToolData {
  name?: string;
  description?: string;
  websiteUrl?: string;
  logoUrl?: string;
  category?: string;
  isActive?: boolean;
  plans?: ToolPlanData[];
}

// Subscription management
interface SubscriptionFilter {
  userId?: string;
  toolId?: string;
  status?: string;
  expiringSoon?: boolean;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  page?: number;
  limit?: number;
}

interface CreateSubscriptionData {
  userId: string;
  toolId: string;
  planId: string;
  status?: string;
  startDate?: Date | string;
  endDate: Date | string;
  notes?: string;
}

interface UpdateSubscriptionData {
  userId?: string;
  toolId?: string;
  planId?: string;
  status?: string;
  startDate?: Date | string;
  endDate?: Date | string;
  notes?: string;
}

// Payment management
interface PaymentFilter {
  userId?: string;
  toolId?: string;
  status?: string;
  paymentMethod?: string;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  page?: number;
  limit?: number;
}

interface UpdatePaymentData {
  status?: string;
  transactionId?: string;
  receiptUrl?: string;
  errorMessage?: string;
  notes?: string;
}

interface RefundPaymentData {
  orderId: string;
  amount?: number;
  reason: string;
}

// Analytics
interface AnalyticsQuery {
  metric?: string;
  timeframe?: string;
  startDate?: Date;
  endDate?: Date;
  dimension?: string;
}

const adminService = {
  // Dashboard
  async getDashboardOverview(): Promise<any> {
    try {
      const response = await api.get("/admin/dashboard/overview");
      return response.data;
    } catch (error: any) {
      console.error("Error fetching dashboard data:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get dashboard data"
      );
    }
  },

  // User management
  async getUsers(filter: UserFilter = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (filter.search) queryParams.append("search", filter.search);
      if (filter.role) queryParams.append("role", filter.role);
      if (filter.isActive !== undefined)
        queryParams.append("isActive", filter.isActive.toString());
      if (filter.sortBy) queryParams.append("sortBy", filter.sortBy);
      if (filter.sortOrder) queryParams.append("sortOrder", filter.sortOrder);
      if (filter.page) queryParams.append("page", filter.page.toString());
      if (filter.limit) queryParams.append("limit", filter.limit.toString());

      const response = await api.get(`/admin/users?${queryParams.toString()}`);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching users:", error);
      throw new Error(error.response?.data?.message || "Failed to get users");
    }
  },

  async getUserById(id: string): Promise<any> {
    try {
      const response = await api.get(`/admin/users/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to get user");
    }
  },

  async createUser(userData: CreateUserData): Promise<any> {
    try {
      const response = await api.post("/admin/users", userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to create user");
    }
  },

  async updateUser(id: string, userData: UpdateUserData): Promise<any> {
    try {
      const response = await api.patch(`/admin/users/${id}`, userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to update user");
    }
  },

  async deleteUser(id: string): Promise<void> {
    try {
      await api.delete(`/admin/users/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to delete user");
    }
  },

  async getUserStats(): Promise<any> {
    try {
      const response = await api.get("/admin/users/stats/overview");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get user stats"
      );
    }
  },

  // Tool management
  async getTools(filter: ToolFilter = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (filter.search) queryParams.append("search", filter.search);
      if (filter.category) queryParams.append("category", filter.category);
      if (filter.isActive !== undefined)
        queryParams.append("isActive", filter.isActive.toString());
      if (filter.sortBy) queryParams.append("sortBy", filter.sortBy);
      if (filter.sortOrder) queryParams.append("sortOrder", filter.sortOrder);
      if (filter.page) queryParams.append("page", filter.page.toString());
      if (filter.limit) queryParams.append("limit", filter.limit.toString());

      const response = await api.get(`/admin/tools?${queryParams.toString()}`);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching tools:", error);
      throw new Error(error.response?.data?.message || "Failed to get tools");
    }
  },

  async getToolById(id: string): Promise<any> {
    try {
      const response = await api.get(`/admin/tools/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to get tool");
    }
  },

  async createTool(toolData: CreateToolData): Promise<any> {
    try {
      const response = await api.post("/admin/tools", toolData);
      return response.data;
    } catch (error: any) {
      console.error("Error creating tool:", error);
      throw new Error(error.response?.data?.message || "Failed to create tool");
    }
  },

  async updateTool(id: string, toolData: UpdateToolData): Promise<any> {
    try {
      const response = await api.patch(`/admin/tools/${id}`, toolData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to update tool");
    }
  },

  async deleteTool(id: string): Promise<void> {
    try {
      await api.delete(`/admin/tools/${id}`);
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to delete tool");
    }
  },

  async toggleToolStatus(id: string, isActive: boolean): Promise<any> {
    try {
      const response = await api.patch(`/admin/tools/${id}/status`, {
        isActive,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to toggle tool status"
      );
    }
  },

  async getToolStats(): Promise<any> {
    try {
      const response = await api.get("/admin/tools/stats/overview");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get tool stats"
      );
    }
  },

  // Subscription management
  async getSubscriptions(filter: SubscriptionFilter = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (filter.userId) queryParams.append("userId", filter.userId);
      if (filter.toolId) queryParams.append("toolId", filter.toolId);
      if (filter.status) queryParams.append("status", filter.status);
      if (filter.expiringSoon !== undefined)
        queryParams.append("expiringSoon", filter.expiringSoon.toString());
      if (filter.sortBy) queryParams.append("sortBy", filter.sortBy);
      if (filter.sortOrder) queryParams.append("sortOrder", filter.sortOrder);
      if (filter.page) queryParams.append("page", filter.page.toString());
      if (filter.limit) queryParams.append("limit", filter.limit.toString());

      const response = await api.get(
        `/admin/subscriptions?${queryParams.toString()}`
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching subscriptions:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get subscriptions"
      );
    }
  },

  async getSubscriptionById(id: string): Promise<any> {
    try {
      const response = await api.get(`/admin/subscriptions/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get subscription"
      );
    }
  },

  async createSubscription(
    subscriptionData: CreateSubscriptionData
  ): Promise<any> {
    try {
      const response = await api.post("/admin/subscriptions", subscriptionData);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to create subscription"
      );
    }
  },

  async updateSubscription(
    id: string,
    subscriptionData: UpdateSubscriptionData
  ): Promise<any> {
    try {
      const response = await api.patch(
        `/admin/subscriptions/${id}`,
        subscriptionData
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to update subscription"
      );
    }
  },

  async deleteSubscription(id: string): Promise<void> {
    try {
      await api.delete(`/admin/subscriptions/${id}`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to delete subscription"
      );
    }
  },

  async getExpiringSoonSubscriptions(): Promise<any> {
    try {
      const response = await api.get("/admin/subscriptions/expiring/soon");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get expiring subscriptions"
      );
    }
  },

  async getSubscriptionStats(): Promise<any> {
    try {
      const response = await api.get("/admin/subscriptions/stats/overview");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get subscription stats"
      );
    }
  },

  // Payment management
  async getPayments(filter: PaymentFilter = {}): Promise<any> {
    try {
      // Return mock data for testing
      console.log("Using mock data for payments");
      return {
        payments: [
          {
            id: "1",
            orderId: "ORD-001",
            userId: "1",
            toolId: "1",
            planId: "1",
            amount: 19.99,
            currency: "USD",
            status: "COMPLETED",
            paymentMethod: "CREDIT_CARD",
            transactionId: "TXN-001",
            createdAt: new Date(
              Date.now() - 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
            user: {
              id: "1",
              name: "John Doe",
              email: "<EMAIL>",
            },
            tool: {
              id: "1",
              name: "ChatGPT Plus",
            },
            plan: {
              id: "1",
              name: "Monthly",
              price: 19.99,
              duration: "month",
            },
          },
          {
            id: "2",
            orderId: "ORD-002",
            userId: "2",
            toolId: "2",
            planId: "3",
            amount: 30,
            currency: "USD",
            status: "COMPLETED",
            paymentMethod: "PAYPAL",
            transactionId: "TXN-002",
            createdAt: new Date(
              Date.now() - 15 * 24 * 60 * 60 * 1000
            ).toISOString(),
            user: {
              id: "2",
              name: "Jane Smith",
              email: "<EMAIL>",
            },
            tool: {
              id: "2",
              name: "Midjourney",
            },
            plan: {
              id: "3",
              name: "Standard",
              price: 30,
              duration: "month",
            },
          },
        ],
        total: 2,
      };
    } catch (error: any) {
      console.error("Error fetching payments:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get payments"
      );
    }
  },

  async getPaymentById(id: string): Promise<any> {
    try {
      const response = await api.get(`/admin/payments/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || "Failed to get payment");
    }
  },

  async updatePayment(
    id: string,
    paymentData: UpdatePaymentData
  ): Promise<any> {
    try {
      const response = await api.patch(`/admin/payments/${id}`, paymentData);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to update payment"
      );
    }
  },

  async processRefund(refundData: RefundPaymentData): Promise<any> {
    try {
      const response = await api.post("/admin/payments/refund", refundData);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to process refund"
      );
    }
  },

  async getPaymentStats(): Promise<any> {
    try {
      const response = await api.get("/admin/payments/stats/overview");
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.message || "Failed to get payment stats"
      );
    }
  },

  // Analytics
  async getAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      // Return mock data for testing
      console.log("Using mock data for analytics");
      return {
        userStats: {
          totalUsers: 100,
          newUsers: 10,
          growthRate: 10,
        },
        revenueStats: {
          totalRevenue: 1000,
          periodRevenue: 100,
          growthRate: 10,
        },
        subscriptionStats: {
          totalActiveSubscriptions: 50,
          newSubscriptions: 5,
          growthRate: 10,
        },
        popularTools: [
          { name: "ChatGPT Plus", subscriptionCount: 20 },
          { name: "Midjourney", subscriptionCount: 15 },
          { name: "Notion Premium", subscriptionCount: 10 },
          { name: "Figma Pro", subscriptionCount: 5 },
          { name: "Adobe Creative Cloud", subscriptionCount: 3 },
        ],
        recentTransactions: [
          {
            id: "1",
            orderNumber: "ORD-001",
            amount: 19.99,
            status: "completed",
            createdAt: new Date().toISOString(),
            user: { name: "John Doe" },
          },
          {
            id: "2",
            orderNumber: "ORD-002",
            amount: 29.99,
            status: "completed",
            createdAt: new Date().toISOString(),
            user: { name: "Jane Smith" },
          },
        ],
        userGrowthData: [
          { date: "2023-01", users: 10 },
          { date: "2023-02", users: 20 },
          { date: "2023-03", users: 30 },
          { date: "2023-04", users: 40 },
          { date: "2023-05", users: 50 },
          { date: "2023-06", users: 60 },
          { date: "2023-07", users: 70 },
          { date: "2023-08", users: 80 },
          { date: "2023-09", users: 90 },
          { date: "2023-10", users: 100 },
        ],
      };
    } catch (error: any) {
      console.error("Error fetching analytics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get analytics data"
      );
    }
  },

  async getUserGrowthAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      // First try to get data from the API
      try {
        console.log("Fetching user growth analytics from API");
        const response = await api.get("/admin/analytics/user-growth", {
          params: query,
        });
        return response.data;
      } catch (apiError) {
        // If API call fails, use mock data
        console.log(
          "API call failed, using mock data for user growth analytics"
        );
        console.error("API Error:", apiError);

        // Return mock data for testing
        return {
          totalUsers: 100,
          newUsers: 10,
          growthRate: 10,
          userGrowthData: [
            { date: "2023-01", users: 10 },
            { date: "2023-02", users: 20 },
            { date: "2023-03", users: 30 },
            { date: "2023-04", users: 40 },
            { date: "2023-05", users: 50 },
            { date: "2023-06", users: 60 },
            { date: "2023-07", users: 70 },
            { date: "2023-08", users: 80 },
            { date: "2023-09", users: 90 },
            { date: "2023-10", users: 100 },
          ],
          userDistribution: [
            { role: "USER", count: 90 },
            { role: "ADMIN", count: 10 },
          ],
        };
      }
    } catch (error: any) {
      console.error("Error in getUserGrowthAnalytics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get user growth analytics"
      );
    }
  },

  async getRevenueAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (query.timeframe) queryParams.append("timeframe", query.timeframe);
      if (query.startDate)
        queryParams.append("startDate", query.startDate.toISOString());
      if (query.endDate)
        queryParams.append("endDate", query.endDate.toISOString());
      if (query.dimension) queryParams.append("dimension", query.dimension);

      const response = await api.get(
        `/admin/analytics/revenue?${queryParams.toString()}`
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching revenue analytics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get revenue analytics"
      );
    }
  },

  async getSubscriptionAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (query.timeframe) queryParams.append("timeframe", query.timeframe);
      if (query.startDate)
        queryParams.append("startDate", query.startDate.toISOString());
      if (query.endDate)
        queryParams.append("endDate", query.endDate.toISOString());
      if (query.dimension) queryParams.append("dimension", query.dimension);

      const response = await api.get(
        `/admin/analytics/subscriptions?${queryParams.toString()}`
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching subscription analytics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get subscription analytics"
      );
    }
  },

  async getToolUsageAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (query.timeframe) queryParams.append("timeframe", query.timeframe);
      if (query.startDate)
        queryParams.append("startDate", query.startDate.toISOString());
      if (query.endDate)
        queryParams.append("endDate", query.endDate.toISOString());
      if (query.dimension) queryParams.append("dimension", query.dimension);

      const response = await api.get(
        `/admin/analytics/tools?${queryParams.toString()}`
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching tool usage analytics:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get tool usage analytics"
      );
    }
  },

  async getConversionRateAnalytics(query: AnalyticsQuery = {}): Promise<any> {
    try {
      const queryParams = new URLSearchParams();

      if (query.timeframe) queryParams.append("timeframe", query.timeframe);
      if (query.startDate)
        queryParams.append("startDate", query.startDate.toISOString());
      if (query.endDate)
        queryParams.append("endDate", query.endDate.toISOString());
      if (query.dimension) queryParams.append("dimension", query.dimension);

      const response = await api.get(
        `/admin/analytics/conversion?${queryParams.toString()}`
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching conversion rate analytics:", error);
      throw new Error(
        error.response?.data?.message ||
          "Failed to get conversion rate analytics"
      );
    }
  },

  // Settings management
  async getSettings(): Promise<any> {
    try {
      const response = await api.get("/admin/settings");
      return response.data;
    } catch (error: any) {
      console.error("Error fetching settings:", error);
      throw new Error(
        error.response?.data?.message || "Failed to get settings"
      );
    }
  },

  async updateSettings(settingsData: any): Promise<any> {
    try {
      const response = await api.put("/admin/settings", settingsData);
      return response.data;
    } catch (error: any) {
      console.error("Error updating settings:", error);
      throw new Error(
        error.response?.data?.message || "Failed to update settings"
      );
    }
  },
};

export default adminService;
