// Background service worker for Partagily Chrome Extension
// This script handles cookie injection and management

// API configuration
const API_URL = 'http://localhost:3001';
const NETFLIX_TOOL_ID = '520e0f4b-1273-475e-b2f8-2de932eebfd8'; // Replace with actual tool ID

// Listen for installation
chrome.runtime.onInstalled.addListener(() => {
  console.log('Partagily Extension installed');
});

// Listen for messages from popup or content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'injectCookies') {
    injectCookiesForTool(message.toolId)
      .then(result => sendResponse({ success: true, message: result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Required for async sendResponse
  }
  
  if (message.action === 'checkAuthentication') {
    checkAuthentication()
      .then(result => sendResponse({ success: true, isAuthenticated: result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Required for async sendResponse
  }
});

/**
 * Inject cookies for a specific tool
 * @param {string} toolId - The ID of the tool
 * @returns {Promise<string>} - A message indicating success
 */
async function injectCookiesForTool(toolId) {
  try {
    // Fetch cookies from API
    const response = await fetch(`${API_URL}/tools/${toolId}/cookies`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch cookies: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const cookies = data.cookies;
    
    if (!cookies || cookies.length === 0) {
      throw new Error('No cookies available for this tool');
    }
    
    // Group cookies by domain
    const cookiesByDomain = {};
    cookies.forEach(cookie => {
      if (!cookiesByDomain[cookie.domain]) {
        cookiesByDomain[cookie.domain] = [];
      }
      cookiesByDomain[cookie.domain].push(cookie);
    });
    
    // Inject cookies for each domain
    let totalInjected = 0;
    
    for (const domain in cookiesByDomain) {
      const domainCookies = cookiesByDomain[domain];
      
      for (const cookie of domainCookies) {
        await chrome.cookies.set({
          url: `https://${domain.startsWith('.') ? domain.substring(1) : domain}`,
          domain: cookie.domain,
          name: cookie.name,
          value: cookie.value,
          path: cookie.path || '/',
          secure: cookie.secure !== false,
          httpOnly: cookie.httpOnly || false,
          sameSite: cookie.sameSite || 'lax',
          expirationDate: new Date(cookie.expirationDate).getTime() / 1000,
        });
        
        totalInjected++;
      }
    }
    
    // Log cookie injection event
    await logCookieEvent('cookie.injection', toolId, totalInjected);
    
    return `Successfully injected ${totalInjected} cookies`;
  } catch (error) {
    console.error('Error injecting cookies:', error);
    throw error;
  }
}

/**
 * Check if the user is authenticated
 * @returns {Promise<boolean>} - Whether the user is authenticated
 */
async function checkAuthentication() {
  try {
    // For testing purposes, always return true
    // In production, this would check for a valid token
    return true;
  } catch (error) {
    console.error('Error checking authentication:', error);
    throw error;
  }
}

/**
 * Log a cookie event to the API
 * @param {string} eventType - The type of event
 * @param {string} toolId - The ID of the tool
 * @param {number} cookieCount - The number of cookies affected
 * @returns {Promise<void>}
 */
async function logCookieEvent(eventType, toolId, cookieCount) {
  try {
    await fetch(`${API_URL}/audit/cookie-events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        eventType,
        toolId,
        details: {
          cookieCount,
          browser: navigator.userAgent,
          timestamp: new Date().toISOString(),
        },
      }),
    });
  } catch (error) {
    console.error('Error logging cookie event:', error);
  }
}

// Add an alarm to periodically check for cookie updates
chrome.alarms.create('checkCookieUpdates', { periodInMinutes: 60 });

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'checkCookieUpdates') {
    // Check for cookie updates
    console.log('Checking for cookie updates...');
    // This would typically check if cookies need to be refreshed
  }
});
