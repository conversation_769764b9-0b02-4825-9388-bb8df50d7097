import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum } from 'class-validator';

export enum PaymentMethod {
  KONNECT = 'KONNECT',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
}

export class CreatePaymentDto {
  @ApiProperty({
    description: 'The tool ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  toolId: string;

  @ApiProperty({
    description: 'The tool name',
    example: 'Premium Design Tool',
  })
  @IsString()
  toolName: string;

  @ApiProperty({
    description: 'The plan ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  planId: string;

  @ApiProperty({
    description: 'The plan name',
    example: 'Monthly Subscription',
  })
  @IsString()
  planName: string;

  @ApiProperty({
    description: 'The payment amount',
    example: 19.99,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'The payment currency',
    example: 'TND',
    default: 'TND',
    required: false,
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'The payment method',
    enum: PaymentMethod,
    example: PaymentMethod.KONNECT,
    default: PaymentMethod.KONNECT,
    required: false,
  })
  @IsEnum(PaymentMethod)
  @IsOptional()
  paymentMethod?: PaymentMethod;

  @ApiProperty({
    description: 'The URL to redirect to after successful payment',
    example: 'https://partagily.com/payment/success',
    required: false,
  })
  @IsString()
  @IsOptional()
  returnUrl?: string;

  @ApiProperty({
    description: 'The URL to redirect to after cancelled payment',
    example: 'https://partagily.com/payment/cancel',
    required: false,
  })
  @IsString()
  @IsOptional()
  cancelUrl?: string;
}
