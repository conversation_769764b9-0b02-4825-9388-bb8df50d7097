'use client';

import React from 'react';
import { motion } from 'framer-motion';
import ReviewsSlider from '@/components/ui/ReviewsSlider';

const TestimonialsSection = () => {
  const reviews = [
    {
      name: "<PERSON>",
      rating: 5,
      text: "Partagily m'a permis d'accéder à Adobe Creative Cloud sans carte de crédit internationale. Maintenant je peux me concentrer sur mon travail de design ! 🎨"
    },
    {
      name: "<PERSON><PERSON>rabels<PERSON>",
      rating: 5,
      text: "<PERSON>r<PERSON><PERSON> à Partagily, j'ai pu utiliser ChatGPT Plus et Grammarly pour mes études. Le paiement avec Konnect est très pratique ! 📚"
    },
    {
      name: "<PERSON><PERSON>",
      rating: 5,
      text: "Notre agence utilise plus de 15 outils premium via Partagily. Économique et fiable, je recommande vivement ! 💼"
    },
    {
      name: "<PERSON><PERSON>",
      rating: 4,
      text: "J'ai lutté pendant des années pour payer les outils premium. Avec Partagily, j'ai maintenant accès à Canva Pro, Notion Premium et plus encore !"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      rating: 5,
      text: "Mon équipe avait besoin d'accéder à plusieurs outils premium, mais les paiements internationaux étaient un cauchemar. Partagily nous a fait économiser de l'argent."
    },
    {
      name: "Sarra Khelifi",
      rating: 4,
      text: "En tant qu'étudiante, je ne pouvais pas me permettre des abonnements individuels. Partagily l'a rendu abordable et accessible. 🎓"
    }
  ];

  return (
    <section className="py-20 bg-white" id="testimonials">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 terminal-text text-center">
            Ce que disent nos <span className="text-yellow-400">utilisateurs tunisiens</span> ⭐️
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto text-center">
            Ne nous croyez pas sur parole. Voici ce que disent les utilisateurs de Partagily en Tunisie.
          </p>
        </motion.div>

        <div className="mb-20">
          <ReviewsSlider reviews={reviews} />
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          <div className="bg-gradient-to-r from-[#25D366] to-[#128C7E] rounded-3xl p-12 text-center text-white shadow-2xl relative overflow-hidden">
            <div className="relative z-10">
              <div className="text-6xl mb-6">💬</div>
              <h3 className="text-4xl md:text-5xl font-bold mb-4">Besoin d'aide ?</h3>
              <p className="text-xl md:text-2xl mb-8 opacity-90">
                Contactez-nous sur <span className="font-bold">WhatsApp</span> 🇹🇳
              </p>
              <a
                href="https://wa.me/21612345678?text=Bonjour%2C%20j%27ai%20besoin%20d%27aide%20avec%20Partagily"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-4 bg-white text-[#25D366] px-8 py-4 rounded-2xl font-bold text-xl shadow-xl hover:shadow-2xl transition-all duration-300"
              >
                <span className="text-3xl">📱</span>
                Démarrer la conversation
                <span>→</span>
              </a>
              <p className="text-sm mt-6 opacity-75">
                Support en français et arabe • Réponse rapide garantie
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
