'use client';

import React from 'react';
import { motion } from 'framer-motion';
import ReviewsSlider from '@/components/ui/ReviewsSlider';

const TestimonialsSection = () => {
  const reviews = [
    {
      name: "<PERSON>",
      rating: 5,
      text: "Partagily m'a permis d'accéder à Adobe Creative Cloud sans carte de crédit internationale. Maintenant je peux me concentrer sur mon travail de design ! 🎨"
    },
    {
      name: "<PERSON><PERSON>rabels<PERSON>",
      rating: 5,
      text: "<PERSON>r<PERSON><PERSON> à Partagily, j'ai pu utiliser ChatGPT Plus et Grammarly pour mes études. Le paiement avec Konnect est très pratique ! 📚"
    },
    {
      name: "<PERSON><PERSON>",
      rating: 5,
      text: "Notre agence utilise plus de 15 outils premium via Partagily. Économique et fiable, je recommande vivement ! 💼"
    },
    {
      name: "<PERSON><PERSON>",
      rating: 4,
      text: "J'ai lutté pendant des années pour payer les outils premium. Avec Partagily, j'ai maintenant accès à Canva Pro, Notion Premium et plus encore !"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      rating: 5,
      text: "Mon équipe avait besoin d'accéder à plusieurs outils premium, mais les paiements internationaux étaient un cauchemar. Partagily nous a fait économiser de l'argent."
    },
    {
      name: "Sarra Khelifi",
      rating: 4,
      text: "En tant qu'étudiante, je ne pouvais pas me permettre des abonnements individuels. Partagily l'a rendu abordable et accessible. 🎓"
    }
  ];

  return (
    <section className="py-20 bg-white" id="testimonials">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 terminal-text text-center">
            Ce que disent nos <span className="text-yellow-400">utilisateurs tunisiens</span> ⭐️
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto text-center">
            Ne nous croyez pas sur parole. Voici ce que disent les utilisateurs de Partagily en Tunisie.
          </p>
        </motion.div>

        <div className="mb-20">
          <ReviewsSlider reviews={reviews} />
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="relative max-w-4xl mx-auto"
        >
          {/* Modern WhatsApp CTA Card */}
          <div className="relative bg-gradient-to-br from-[#25D366] via-[#20b358] to-[#1a9e4a] rounded-3xl p-8 md:p-12 text-center text-white shadow-2xl overflow-hidden">
            {/* Animated Background Elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
              <div className="absolute bottom-10 right-10 w-16 h-16 bg-white rounded-full animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-white rounded-full animate-pulse delay-500"></div>
            </div>

            {/* Content */}
            <div className="relative z-10">
              {/* WhatsApp Icon with Animation */}
              <motion.div
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl mb-6"
              >
                <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516"/>
                </svg>
              </motion.div>

              {/* Heading */}
              <h3 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                Besoin d'aide ?
              </h3>

              {/* Subtitle */}
              <p className="text-lg md:text-xl mb-8 text-white/90 max-w-2xl mx-auto leading-relaxed">
                Contactez notre équipe sur <span className="font-bold text-white">WhatsApp</span> pour un support personnalisé 🇹🇳
              </p>

              {/* CTA Button */}
              <motion.a
                href="https://wa.me/21612345678?text=Bonjour%2C%20j%27ai%20besoin%20d%27aide%20avec%20Partagily"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                className="group inline-flex items-center gap-3 bg-white text-[#25D366] px-8 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 border-2 border-white/20 hover:border-white/40"
              >
                <div className="flex items-center justify-center w-8 h-8 bg-[#25D366] rounded-lg">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.516"/>
                  </svg>
                </div>
                Démarrer la conversation
                <motion.span
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                  className="text-xl"
                >
                  →
                </motion.span>
              </motion.a>

              {/* Support Info */}
              <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-white/80">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
                  <span>Support en français et arabe</span>
                </div>
                <div className="hidden sm:block w-1 h-1 bg-white/60 rounded-full"></div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-white/60 rounded-full animate-pulse delay-500"></div>
                  <span>Réponse rapide garantie</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
