'use client';

import { motion } from 'framer-motion';
import ReviewsSlider from '@/components/ui/ReviewsSlider';

const TestimonialsSection = () => {
  // New reviews data format
  const reviews = [
    {
      name: "<PERSON>",
      rating: 3,
      text: "I've gone on more dates in a month than I have in a year, thanks to this app!"
    },
    {
      name: "<PERSON>",
      rating: 5,
      text: "This platform helped me achieve my learning goals; they really care about helping users to grow! 📝 👍"
    },
    {
      name: "<PERSON>",
      rating: 5,
      text: "Partagily solved my biggest problem - accessing Adobe Creative Cloud without an international credit card. Now I can focus on my design work!"
    },
    {
      name: "<PERSON><PERSON>",
      rating: 4,
      text: "I've been struggling to pay for premium tools for years. With Partagily, I can now use Canva Pro, Notion Premium, and more!"
    },
    {
      name: "<PERSON>ssef T.",
      rating: 5,
      text: "My team needed access to multiple premium tools, but international payments were a nightmare. Partagily's shared account system saved us money."
    },
    {
      name: "Fatima K.",
      rating: 4,
      text: "As a student, I couldn't afford individual subscriptions to all the tools I needed. Partagily made it affordable and accessible."
    }
  ];

  // Original testimonials for reference
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Graphic Designer",
      content: "Partagily solved my biggest problem - accessing Adobe Creative Cloud without an international credit card. Now I can focus on my design work instead of payment issues!",
      avatar: "👨‍🎨",
      color: "bg-pink-100",
      rotation: 1 // Positive rotation
    },
    {
      name: "Leila M.",
      role: "Content Creator",
      content: "I've been struggling to pay for premium tools for years. With Partagily, I can now use Canva Pro, Notion Premium, and more - all with my local payment methods!",
      avatar: "👩‍💻",
      color: "bg-yellow-100",
      rotation: -1 // Negative rotation
    },
    {
      name: "Youssef T.",
      role: "Startup Founder",
      content: "My team needed access to multiple premium tools, but international payments were a nightmare. Partagily's shared account system saved us money and solved our payment issues.",
      avatar: "👨‍💼",
      color: "bg-blue-100",
      rotation: -1 // Negative rotation
    },
    {
      name: "Fatima K.",
      role: "Student",
      content: "As a student, I couldn't afford individual subscriptions to all the tools I needed. Partagily made it affordable and accessible with local payment options.",
      avatar: "👩‍🎓",
      color: "bg-green-100",
      rotation: 1 // Positive rotation
    }
  ];

  return (
    <section className="py-20 bg-white" id="testimonials">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            What Our <span className="text-yellow-400">Users</span> Say 💬
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what users are saying about Partagily.
          </p>
        </motion.div>

        {/* New Reviews Slider */}
        <div className="mb-16">
          <ReviewsSlider reviews={reviews} />
        </div>

        {/* Original Testimonials - keeping for reference */}
        <div className="hidden">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className={`${testimonial.color} p-6 rounded-xl shadow-md transform hover:rotate-0 transition-all duration-300`}
                style={{
                  boxShadow: "5px 5px 15px rgba(0,0,0,0.1)",
                  transform: `rotate(${testimonial.rotation}deg)`
                }}
              >
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-2xl shadow-md">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <h3 className="text-lg font-bold terminal-text">{testimonial.name}</h3>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
                <div className="mt-4">
                  <p className="text-gray-700">"{testimonial.content}"</p>
                </div>
                <div className="mt-4 flex">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className="text-yellow-400">★</span>
                  ))}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
