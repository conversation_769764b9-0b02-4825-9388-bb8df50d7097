/* Modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600;700&display=swap');

.terminal-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.mono-text {
  font-family: var(--font-geist-mono), 'Fira Code', monospace;
}

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, #fce7f3, #fef3c7, #dbeafe);
  padding: 2rem 1rem;
}

.dark .auth-container {
  background: linear-gradient(to right, #0f172a, #1e293b);
}

@media (min-width: 640px) {
  .auth-container {
    padding: 3rem 2rem;
  }
}

@media (min-width: 768px) {
  .auth-container {
    padding: 5rem 2rem;
  }
}

.auth-card {
  max-width: 28rem;
  width: 100%;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  margin-top: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.dark .auth-card {
  background-color: #0f172a;
  border: 1px solid #1e293b;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.auth-header {
  text-align: center;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

.auth-title {
  font-size: 1.875rem;
  font-weight: 700;
  font-family: var(--font-geist-sans), 'Inter', sans-serif;
  letter-spacing: -0.02em;
  color: #111111;
}

.dark .auth-title {
  color: #F3F4F6;
}

.auth-highlight {
  color: #FFAD00;
}

.dark .auth-highlight {
  color: #FFAD00;
}

.auth-input {
  appearance: none;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 0.5rem;
  background-color: white;
  color: #111111;
}

.auth-input::placeholder {
  color: #9CA3AF;
}

.dark .auth-input {
  background-color: #1e293b;
  color: #F3F4F6;
  border: 1px solid #334155;
}

.dark .auth-input::placeholder {
  color: #6B7280;
}

.auth-input:focus {
  outline: none;
  border-color: #FFAD00;
  box-shadow: 0 0 0 3px rgba(255, 173, 0, 0.3);
}

.dark .auth-input:focus {
  outline: none;
  border-color: #FFAD00;
  box-shadow: 0 0 0 3px rgba(255, 173, 0, 0.3);
  ring: 1px #FFAD00;
}

.auth-button {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 9999px;
  font-weight: 500;
  color: #111111;
  background-color: #FFAD00;
  transition: all 0.3s;
  cursor: pointer;
}

.auth-button:hover {
  background-color: #FFB300;
  box-shadow: 0 4px 12px rgba(255, 173, 0, 0.3);
  transform: translateY(-2px);
}

.auth-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(255, 173, 0, 0.2);
}

.dark .auth-button {
  color: #111111;
  background-color: #FFAD00;
}

.dark .auth-button:hover {
  background-color: #FFB300;
  box-shadow: 0 4px 12px rgba(255, 173, 0, 0.3);
}

.auth-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.auth-link {
  color: #FFAD00;
  font-weight: 500;
  transition: color 0.3s;
}

.auth-link:hover {
  color: #FFB300;
}

.dark .auth-link {
  color: #FFAD00;
}

.dark .auth-link:hover {
  color: #FFB300;
}

.auth-divider {
  position: relative;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.auth-divider-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  border-top: 1px solid #e5e7eb;
}

.dark .auth-divider-line {
  border-top: 1px solid #334155;
}

.auth-divider-text {
  position: relative;
  display: flex;
  justify-content: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.dark .auth-divider-text {
  color: #9CA3AF;
}

.auth-divider-text span {
  padding: 0 0.5rem;
  background-color: white;
}

.dark .auth-divider-text span {
  background-color: #0f172a;
  color: #E5E7EB;
}

.auth-footer {
  height: 0.5rem;
  background: linear-gradient(to right, #FFAD00, #e94a9c, #4f8eff);
}

/* Create account button */
.create-account-button {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0.75rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s;
  cursor: pointer;
  background-color: white;
  color: #111111;
  border: 2px solid #FFAD00;
}

.create-account-button:hover {
  background-color: #FFF9E5;
  box-shadow: 0 4px 12px rgba(255, 173, 0, 0.2);
  transform: translateY(-2px);
}

.create-account-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(255, 173, 0, 0.1);
}

.dark .create-account-button {
  background-color: #1e293b;
  color: #F3F4F6;
  border: 1px solid #334155;
}

.dark .create-account-button:hover {
  background-color: #334155;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
