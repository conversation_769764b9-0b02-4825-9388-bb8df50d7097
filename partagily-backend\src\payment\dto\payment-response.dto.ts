import { ApiProperty } from '@nestjs/swagger';

export class PaymentDataDto {
  @ApiProperty({
    description: 'Payment URL to redirect the user to',
    example: 'https://app.konnect.network/payment/checkout/123456789',
  })
  paymentUrl?: string;

  @ApiProperty({
    description: 'Client reference for the payment',
    example: 'partagily-123e4567-e89b-12d3-a456-426614174000-1620000000000',
  })
  clientReference?: string;
}

export class PaymentResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Payment initiated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Payment data',
    type: PaymentDataDto,
    required: false,
  })
  data?: PaymentDataDto;
}
