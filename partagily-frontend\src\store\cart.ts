import { create } from "zustand";
import { CartStoreType } from "./types";

const useCart = create<CartStoreType>((set, get) => ({
  cartItems: [],
  isCartOpen: false,
  totalAmount: 0,
  calculateAmount: () => {
    set((state) => ({
      totalAmount: Math.round(
        state.cartItems.reduce((total, item) => total + item.price, 0) * 3000
      ),
    }));
  },
  addItemCart: async (newItems) => {
    try {
      set((state) => ({ cartItems: [...state.cartItems, ...newItems] }));
    } catch (err) {
      console.error("Error Updating cart:", err);
      throw err;
    }
  },
  removeItemCart: async (id) => {
    try {
      set((state) => ({
        cartItems: state.cartItems.filter((item) => item.id !== id),
      }));
    } catch (err) {
      console.error("Error Updating cart:", err);
      throw err;
    }
  },
  handleToggleCart: (value) => {
    try {
      set((state) => ({ isCartOpen: value }));
    } catch (err) {
      console.error("Error Updating cart:", err);
      throw err;
    }
  },
}));

export default useCart;
