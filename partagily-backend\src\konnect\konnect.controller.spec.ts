import { Test, TestingModule } from '@nestjs/testing';
import { KonnectController } from './konnect.controller';
import { KonnectService } from './konnect.service';

describe('KonnectController', () => {
  let controller: KonnectController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [KonnectController],
      providers: [KonnectService],
    }).compile();

    controller = module.get<KonnectController>(KonnectController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
