import { PrismaClient, Tool<PERSON>ategory, ToolStatus, PlanTier } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

interface ToolInput {
  name: string;
  description: string;
  icon?: string;
  price: number;
  category: string;
  status?: string;
  requiredPlan: string;
  domain?: string;
  originalPrice?: number;
}

/**
 * Validates and maps the category string to a valid ToolCategory enum value
 */
function mapCategory(category: string): ToolCategory {
  // Convert to title case for consistency
  const formattedCategory = category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  
  // Check if it's a valid category
  if (Object.values(ToolCategory).includes(formattedCategory as ToolCategory)) {
    return formattedCategory as ToolCategory;
  }
  
  throw new Error(`Invalid category: ${category}. Valid categories are: ${Object.values(ToolCategory).join(', ')}`);
}

/**
 * Validates and maps the status string to a valid ToolStatus enum value
 */
function mapStatus(status: string = 'AVAILABLE'): ToolStatus {
  const upperStatus = status.toUpperCase();
  
  if (Object.values(ToolStatus).includes(upperStatus as ToolStatus)) {
    return upperStatus as ToolStatus;
  }
  
  throw new Error(`Invalid status: ${status}. Valid statuses are: ${Object.values(ToolStatus).join(', ')}`);
}

/**
 * Validates and maps the plan tier string to a valid PlanTier enum value
 */
function mapPlanTier(tier: string): PlanTier {
  const upperTier = tier.toUpperCase();
  
  if (Object.values(PlanTier).includes(upperTier as PlanTier)) {
    return upperTier as PlanTier;
  }
  
  throw new Error(`Invalid plan tier: ${tier}. Valid tiers are: ${Object.values(PlanTier).join(', ')}`);
}

/**
 * Imports tools from a JSON file into the database
 */
async function importTools(filePath: string) {
  try {
    // Read and parse the JSON file
    const fileContent = fs.readFileSync(path.resolve(filePath), 'utf8');
    const tools: ToolInput[] = JSON.parse(fileContent);
    
    console.log(`Found ${tools.length} tools to import`);
    
    // Process each tool
    for (const toolData of tools) {
      try {
        // Map the input data to the correct types
        const mappedData = {
          name: toolData.name,
          description: toolData.description,
          icon: toolData.icon,
          price: toolData.price,
          category: mapCategory(toolData.category),
          status: mapStatus(toolData.status),
          requiredPlan: mapPlanTier(toolData.requiredPlan),
          domain: toolData.domain,
          originalPrice: toolData.originalPrice,
        };
        
        // Check if the tool already exists
        const existingTool = await prisma.tool.findFirst({
          where: {
            name: toolData.name,
          },
        });
        
        if (existingTool) {
          // Update existing tool
          const updatedTool = await prisma.tool.update({
            where: {
              id: existingTool.id,
            },
            data: mappedData,
          });
          console.log(`Updated tool: ${updatedTool.name}`);
        } else {
          // Create new tool
          const newTool = await prisma.tool.create({
            data: mappedData,
          });
          console.log(`Created tool: ${newTool.name}`);
        }
      } catch (error) {
        console.error(`Error processing tool ${toolData.name}:`, error);
      }
    }
    
    console.log('Tool import completed');
  } catch (error) {
    console.error('Error importing tools:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Check if a file path was provided
if (process.argv.length < 3) {
  console.error('Please provide a path to the JSON file containing tools data');
  console.error('Usage: npx ts-node scripts/import-tools.ts <path-to-json-file>');
  process.exit(1);
}

// Run the import
importTools(process.argv[2]);
