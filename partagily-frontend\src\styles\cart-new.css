/* Cart overlay styles */

/* When cart is open, ensure the backdrop covers the entire viewport */
body.cart-open {
  /* This helps ensure the backdrop covers everything */
  width: 100vw !important;
  height: 100vh !important;
  overflow: hidden !important;
  position: fixed !important;
  overscroll-behavior: none;
  touch-action: none;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Hide scrollbar for Chrome, Safari and Opera */
body.cart-open::-webkit-scrollbar {
  display: none;
}

/* Ensure the sidebar remains visible and interactive */
@media (min-width: 1024px) {
  body.cart-open .sidebar {
    z-index: 9000;
    position: relative;
  }
}

/* Fix for backdrop coverage issues */
.fixed.inset-0 {
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}

/* Ensure pointer events work correctly */
.fixed.inset-0,
.fixed.inset-0 > * {
  pointer-events: auto !important;
}

/* Ensure cart drawer has proper stacking context */
[data-testid="cart-drawer"] {
  isolation: isolate;
  z-index: 10000 !important;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.15) !important;
  position: fixed !important;
  pointer-events: all !important;
}

/* Ensure backdrop has proper stacking context */
[data-testid="cart-backdrop"] {
  z-index: 9999 !important;
  position: fixed !important;
  pointer-events: all !important;
}
