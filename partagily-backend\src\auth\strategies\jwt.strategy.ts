import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private prisma: PrismaService
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    try {
      // Get the user from the database to ensure they exist and are active
      const user = await this.prisma.user.findUnique({
        where: { id: payload.sub },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          isActive: true,
          emailVerified: true
        }
      });

      // If user not found or not active, throw an error
      if (!user || !user.isActive) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Return the user data
      return {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role.toLowerCase(), // Ensure role is lowercase for consistency
        isActive: user.isActive,
        emailVerified: user.emailVerified
      };
    } catch (error) {
      // If there's a database error, just return the basic info from the token
      console.error('Error validating JWT token:', error);
      return { id: payload.sub, email: payload.email };
    }
  }
}
