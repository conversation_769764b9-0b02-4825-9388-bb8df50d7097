'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useNotification } from '@/contexts/NotificationContext';

// Sample affiliate data for future implementation
const affiliateStats = {
  referralCode: 'AHMED2023',
  totalReferrals: 12,
  activeReferrals: 8,
  totalEarnings: 89.94,
  pendingEarnings: 24.99,
  paidEarnings: 64.95,
};

// Feature component
const FeatureItem = ({ icon, title, description }: { icon: string, title: string, description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex items-start p-5 rounded-xl bg-white dark:bg-slate-800 shadow-md hover:shadow-lg transition-shadow duration-300"
    >
      <div className="flex-shrink-0 w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-500 dark:text-yellow-400 mr-4">
        <span className="text-2xl">{icon}</span>
      </div>
      <div>
        <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">{title}</h3>
        <p className="text-gray-600 dark:text-gray-300">{description}</p>
      </div>
    </motion.div>
  );
};

export default function Affiliate() {
  const { showNotification } = useNotification();
  const [email, setEmail] = useState('');

  const handleNotifyClick = () => {
    if (email && email.includes('@')) {
      showNotification('success', 'Thank you! We\'ll notify you when the affiliate program launches.', { autoClose: true });
      setEmail('');
    } else {
      showNotification('error', 'Please enter a valid email address.', { autoClose: true });
    }
  };

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header with gradient background */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-12"
      >
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-8 text-white shadow-lg text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-6xl mb-6 inline-block"
          >
            🚀
          </motion.div>
          <h2 className="text-3xl font-bold mb-4">Affiliate Program Coming Soon!</h2>
          <p className="text-xl max-w-3xl mx-auto">
            We're working hard to launch our affiliate program. Soon you'll be able to earn money by referring friends and family to Partagily.
          </p>
        </div>
      </motion.div>

      {/* Features section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mb-12"
      >
        <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">What to expect:</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FeatureItem
            icon="💰"
            title="Generous Commissions"
            description="Earn up to 20% commission on every subscription purchased through your referral link."
          />

          <FeatureItem
            icon="🔄"
            title="Recurring Revenue"
            description="Earn commissions not just on the first purchase, but on every renewal."
          />

          <FeatureItem
            icon="💳"
            title="Easy Payouts"
            description="Get paid via your preferred local payment method, including bank transfers and mobile payments."
          />

          <FeatureItem
            icon="📊"
            title="Detailed Analytics"
            description="Track your referrals, conversions, and earnings with our comprehensive dashboard."
          />
        </div>
      </motion.div>

      {/* How it will work section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="mb-12 bg-white dark:bg-slate-800 rounded-xl p-8 shadow-md"
      >
        <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">How it will work:</h3>

        <div className="space-y-8">
          <div className="flex items-start">
            <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <span className="font-bold">1</span>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Sign up for the affiliate program</h4>
              <p className="text-gray-600 dark:text-gray-300">
                Once launched, you'll be able to join our affiliate program directly from your dashboard.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <span className="font-bold">2</span>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Share your unique referral link</h4>
              <p className="text-gray-600 dark:text-gray-300">
                Get your personalized referral link and share it with friends, family, or on social media.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <span className="font-bold">3</span>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Earn commissions</h4>
              <p className="text-gray-600 dark:text-gray-300">
                When someone signs up using your link and makes a purchase, you'll earn a commission.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
              <span className="font-bold">4</span>
            </div>
            <div>
              <h4 className="text-lg font-bold mb-2 text-gray-900 dark:text-white">Get paid</h4>
              <p className="text-gray-600 dark:text-gray-300">
                Receive your earnings through your preferred payment method once you reach the minimum payout threshold.
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Notification section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border border-yellow-200 dark:border-yellow-800/30 rounded-xl p-6 shadow-md"
      >
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-400 mr-4">
            <span className="text-2xl">✉️</span>
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">We'll notify you when we launch</h3>
        </div>

        <p className="text-gray-600 dark:text-gray-300 mb-6">
          As a Partagily user, you're already on our list! We'll notify you as soon as our affiliate program launches so you can start earning commissions right away.
        </p>

        <div className="flex flex-col sm:flex-row items-center justify-center">
          <button
            onClick={() => showNotification('success', 'Great! We\'ll notify you when the affiliate program launches.', { autoClose: true })}
            className="w-full sm:w-auto bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            <span className="mr-2">🔔</span>
            Remind Me When It Launches
          </button>
        </div>
      </motion.div>
    </div>
  );
}
