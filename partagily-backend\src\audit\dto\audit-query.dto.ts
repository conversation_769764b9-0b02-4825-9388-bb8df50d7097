import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsDateString, IsInt, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class AuditQueryDto {
  @ApiProperty({
    description: 'Filter by event type',
    example: 'auth.login.success',
    required: false,
  })
  @IsString()
  @IsOptional()
  eventType?: string;

  @ApiProperty({
    description: 'Filter by user ID',
    example: '1',
    required: false,
  })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    description: 'Filter by IP address',
    example: '***********',
    required: false,
  })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiProperty({
    description: 'Filter by severity',
    enum: ['info', 'warning', 'error'],
    example: 'warning',
    required: false,
  })
  @IsEnum(['info', 'warning', 'error'])
  @IsOptional()
  severity?: string;

  @ApiProperty({
    description: 'Filter by start date (ISO format)',
    example: '2023-01-01T00:00:00Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'Filter by end date (ISO format)',
    example: '2023-12-31T23:59:59Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    default: 10,
    required: false,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit?: number = 10;
}
