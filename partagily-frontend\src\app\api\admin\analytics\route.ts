import { NextRequest, NextResponse } from 'next/server';

// Generate mock analytics data
const generateMockAnalyticsData = (timeframe: string) => {
  // Get tools from localStorage (this won't work server-side, but we'll handle that)
  let toolCount = 0;
  
  // Generate random user stats
  const userStats = {
    totalUsers: Math.floor(Math.random() * 1000) + 100,
    newUsers: Math.floor(Math.random() * 100) + 10,
    activeUsers: Math.floor(Math.random() * 500) + 50,
    growthRate: Math.random() * 10 + 2,
  };
  
  // Generate random revenue stats
  const revenueStats = {
    totalRevenue: Math.floor(Math.random() * 10000) + 1000,
    monthlyRevenue: Math.floor(Math.random() * 5000) + 500,
    averageOrderValue: Math.floor(Math.random() * 100) + 20,
    growthRate: Math.random() * 15 + 5,
  };
  
  // Generate random subscription stats
  const subscriptionStats = {
    totalSubscriptions: Math.floor(Math.random() * 800) + 100,
    totalActiveSubscriptions: Math.floor(Math.random() * 600) + 80,
    churnRate: Math.random() * 5 + 1,
    growthRate: Math.random() * 8 + 3,
  };
  
  // Generate random popular tools
  const popularTools = [
    {
      id: '1',
      name: 'Netflix',
      subscriptions: Math.floor(Math.random() * 200) + 50,
      revenue: Math.floor(Math.random() * 2000) + 500,
    },
    {
      id: '2',
      name: 'Spotify',
      subscriptions: Math.floor(Math.random() * 150) + 40,
      revenue: Math.floor(Math.random() * 1500) + 400,
    },
    {
      id: '3',
      name: 'ChatGPT',
      subscriptions: Math.floor(Math.random() * 100) + 30,
      revenue: Math.floor(Math.random() * 1000) + 300,
    },
    {
      id: '4',
      name: 'Adobe Creative Cloud',
      subscriptions: Math.floor(Math.random() * 80) + 20,
      revenue: Math.floor(Math.random() * 800) + 200,
    },
    {
      id: '5',
      name: 'Microsoft 365',
      subscriptions: Math.floor(Math.random() * 60) + 10,
      revenue: Math.floor(Math.random() * 600) + 100,
    },
  ];
  
  // Generate random user growth data
  const userGrowthData = [];
  let baseUsers = userStats.totalUsers - (userStats.newUsers * 6);
  
  if (timeframe === 'day') {
    // Last 24 hours in 4-hour increments
    for (let i = 0; i < 6; i++) {
      const hour = i * 4;
      userGrowthData.push({
        label: `${hour}:00`,
        users: Math.floor(baseUsers + (userStats.newUsers / 6) * i),
      });
    }
  } else if (timeframe === 'week') {
    // Last 7 days
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    for (let i = 0; i < 7; i++) {
      userGrowthData.push({
        label: days[i],
        users: Math.floor(baseUsers + (userStats.newUsers / 7) * i),
      });
    }
  } else if (timeframe === 'month') {
    // Last 30 days in weekly increments
    for (let i = 0; i < 4; i++) {
      const week = i + 1;
      userGrowthData.push({
        label: `Week ${week}`,
        users: Math.floor(baseUsers + (userStats.newUsers / 4) * i),
      });
    }
  } else if (timeframe === 'quarter') {
    // Last 3 months
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();
    for (let i = 0; i < 3; i++) {
      const monthIndex = (currentMonth - 2 + i) % 12;
      userGrowthData.push({
        label: months[monthIndex],
        users: Math.floor(baseUsers + (userStats.newUsers / 3) * i),
      });
    }
  } else if (timeframe === 'year') {
    // Last 12 months
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();
    for (let i = 0; i < 12; i++) {
      const monthIndex = (currentMonth - 11 + i) % 12;
      userGrowthData.push({
        label: months[monthIndex],
        users: Math.floor(baseUsers + (userStats.newUsers / 12) * i),
      });
    }
  }
  
  // Generate random revenue data
  const revenueData = userGrowthData.map(item => ({
    label: item.label,
    revenue: Math.floor(Math.random() * 1000) + 500,
  }));
  
  // Generate random conversion rate data
  const conversionRateData = userGrowthData.map(item => ({
    label: item.label,
    rate: Math.random() * 5 + 1,
  }));
  
  return {
    userStats,
    revenueStats,
    subscriptionStats,
    popularTools,
    userGrowthData,
    revenueData,
    conversionRateData,
    toolCount,
  };
};

export async function GET(request: NextRequest) {
  try {
    // Get timeframe from query parameters
    const url = new URL(request.url);
    const timeframe = url.searchParams.get('timeframe') || 'month';
    
    // Generate mock analytics data
    const data = generateMockAnalyticsData(timeframe);
    
    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Error generating analytics data:', error);
    return NextResponse.json({ 
      error: 'Failed to generate analytics data',
      message: error.message 
    }, { status: 500 });
  }
}
