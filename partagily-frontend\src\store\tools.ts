import userService from "@/services/userService";
import { create } from "zustand";
import { ToolsStoreType } from "./types";

const useTools = create<ToolsStoreType>((set, get) => ({
  tools: [],
  initTools: async () => {
    try {
      // Fetch real data from the database
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/tools`);
      if (!response.ok) {
        throw new Error("Failed to fetch Tools");
      }
      const { tools } = await response.json();
      set((state: any) => ({ tools }));
    } catch (err: any) {
      console.error("Error fetching tools:", err);
      throw err;
    }
  },
}));

export default useTools;
