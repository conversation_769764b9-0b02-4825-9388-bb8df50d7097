import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the backend URL from environment variables or use default
    const backendUrl = process.env.NEXT_PUBLIC_API_URL;

    // Get the plan ID from the URL params
    const planId = params.id;

    console.log(
      `API route /api/plans/${planId} - Proxying GET request to backend:`,
      `${backendUrl}/plans/${planId}`
    );

    // Extract auth token from request headers
    const authToken = request.headers.get("authorization");

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/plans/${planId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        ...(authToken ? { Authorization: authToken } : {}),
      },
      // Add a timeout
      signal: AbortSignal.timeout(5000),
      cache: "no-store",
    });

    // If the response is not OK, try alternative endpoint
    if (!response.ok) {
      console.warn(
        `Backend API returned error for /plans/${planId}:`,
        response.status,
        response.statusText
      );
      console.log(
        `Trying alternative endpoint: /subscriptions/plans/${planId}`
      );

      // Try alternative endpoint
      const altResponse = await fetch(
        `${backendUrl}/subscriptions/plans/${planId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
            ...(authToken ? { Authorization: authToken } : {}),
          },
          // Add a timeout
          signal: AbortSignal.timeout(5000),
          cache: "no-store",
        }
      );

      // If alternative endpoint also fails, return error
      if (!altResponse.ok) {
        console.error(
          "Both backend API endpoints returned error:",
          altResponse.status,
          altResponse.statusText
        );

        // Return a more helpful error response
        return NextResponse.json(
          {
            error: "Backend API error",
            status: altResponse.status,
            message:
              altResponse.statusText || "Failed to fetch plan from backend",
          },
          { status: altResponse.status }
        );
      }

      // Parse the response as JSON
      const altData = await altResponse.json();

      console.log(
        "Alternative backend API response received, forwarding to client"
      );

      // Return the data
      return NextResponse.json(altData);
    }

    // Parse the response as JSON
    const data = await response.json();

    console.log("Backend API response received, forwarding to client");

    // Return the data
    return NextResponse.json(data);
  } catch (error: any) {
    console.error(`Error in /api/plans/[id] route:`, error.message);

    // Return a helpful error response
    return NextResponse.json(
      {
        error: "API proxy error",
        message: error.message || "Failed to proxy request to backend",
      },
      { status: 500 }
    );
  }
}
