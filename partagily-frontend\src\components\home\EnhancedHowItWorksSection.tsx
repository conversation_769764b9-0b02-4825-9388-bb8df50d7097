'use client';

import { motion } from 'framer-motion';

const EnhancedHowItWorksSection = () => {
  const steps = [
    {
      title: "Download",
      description: "Get our Chrome extension to access premium tools instantly.",
      icon: "📥"
    },
    {
      title: "Create Account",
      description: "Sign up and choose your perfect subscription plan.",
      icon: "👤"
    },
    {
      title: "Buy Local",
      description: "Pay with Tunisian methods - no international cards needed!",
      icon: "💳"
    },
    {
      title: "Receive Tools",
      description: "Instantly access premium global tools and services.",
      icon: "📦"
    }
  ];

  const keyFeatures = [
    {
      title: "120+ Premium Tools",
      description: "From design to productivity to AI tools.",
      icon: "🛠️"
    },
    {
      title: "Shared Accounts",
      description: "Access premium tools without individual subscriptions.",
      icon: "🔑"
    },
    {
      title: "Instant Access",
      description: "No waiting - get access immediately after payment.",
      icon: "⚡"
    },
    {
      title: "Tunisian Support",
      description: "Local support team that understands your needs.",
      icon: "🇹🇳"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <section className="py-20" id="how-it-works">
      <div className="container mx-auto px-4">
        {/* How It Works Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            How It <span className="text-[#FFAD00]">Works</span> 🧩
          </h2>
          <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            Getting started with Partagily is simple and straightforward.
            Follow these steps to access premium tools today!
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {steps.map((step, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white dark:bg-card-bg p-8 rounded-xl border border-transparent dark:border-gray-700/30 shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center text-center hover-bounce hover-card"
            >
              <div className="text-5xl mb-6 transform transition-transform hover:scale-110 cursor-pointer">{step.icon}</div>
              <h3 className="text-xl font-bold mb-3 terminal-text">{step.title}</h3>
              <p className="text-gray-700 dark:text-gray-300">{step.description}</p>
              <div className="mt-6 bg-[#FFAD00] text-gray-900 w-8 h-8 rounded-full flex items-center justify-center font-bold shadow-md">
                {index + 1}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Key Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4 terminal-text">
            Key <span className="text-[#FFAD00]">Features</span> ✨
          </h2>
          <p className="text-xl text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
            Partagily is packed with features designed specifically for Tunisian users.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {keyFeatures.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white dark:bg-card-bg p-6 rounded-xl border border-transparent dark:border-gray-700/30 shadow-md hover:shadow-xl transition-all duration-300 flex flex-col items-center text-center hover-card"
            >
              <div className="text-4xl mb-4 transform transition-transform hover:scale-110 cursor-pointer">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-2 terminal-text">{feature.title}</h3>
              <p className="text-gray-700 dark:text-gray-300">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="mt-16 text-center"
        >
          <a
            href="#payment-methods"
            className="btn btn-primary px-8 py-3 text-lg inline-flex items-center gap-2 hover-glow"
          >
            Learn about payment methods 💳
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default EnhancedHowItWorksSection;
