'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, User, Mail, Key, Shield, Package } from 'lucide-react';
import adminService from '@/services/adminService';

export default function CreateUserPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'user',
    isActive: true,
    assignTools: false,
    selectedTools: [] as { toolId: string, planId: string }[],
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [tools, setTools] = useState<any[]>([]);
  const [isLoadingTools, setIsLoadingTools] = useState<boolean>(true);
  const router = useRouter();

  // Fetch tools for the dropdown
  useEffect(() => {
    const fetchTools = async () => {
      try {
        setIsLoadingTools(true);

        console.log('Fetching tools from backend API');
        const response = await adminService.getTools();
        setTools(response.tools);
        console.log('Tools fetched:', response.tools || []);
      } catch (err: any) {
        console.error('Failed to fetch tools:', err);
      } finally {
        setIsLoadingTools(false);
      }
    };

    fetchTools();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({
        ...formData,
        [name]: checkbox.checked,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle adding a tool to the user
  const handleAddTool = () => {
    setFormData({
      ...formData,
      selectedTools: [
        ...formData.selectedTools,
        { toolId: '', planId: '' }
      ]
    });
  };

  // Handle removing a tool from the user
  const handleRemoveTool = (index: number) => {
    const updatedTools = [...formData.selectedTools];
    updatedTools.splice(index, 1);
    setFormData({
      ...formData,
      selectedTools: updatedTools
    });
  };

  // Handle changing a tool or plan selection
  const handleToolChange = (index: number, field: 'toolId' | 'planId', value: string) => {
    const updatedTools = [...formData.selectedTools];
    updatedTools[index][field] = value;

    // If changing the tool, reset the plan
    if (field === 'toolId') {
      updatedTools[index].planId = '';
    }

    setFormData({
      ...formData,
      selectedTools: updatedTools
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError(null);

      // Validate tool selections if assignTools is enabled
      if (formData.assignTools && formData.selectedTools.length > 0) {
        const invalidSelections = formData.selectedTools.some(
          tool => !tool.toolId || !tool.planId
        );

        if (invalidSelections) {
          setError('Please select both tool and plan for all tool assignments');
          setIsLoading(false);
          return;
        }
      }

      // Create user data without the tool assignments
      const { assignTools, selectedTools, ...userData } = formData;

      console.log('Creating user with backend API');
      const response = await adminService.createUser(userData);
      console.log('User created successfully:', response);

      // If tool assignments are enabled and we have tools selected
      if (assignTools && selectedTools.length > 0) {
        try {
          // For each selected tool, create a subscription
          for (const toolSelection of selectedTools) {
            // Get the tool to find its details
            const tool = tools.find(t => t.id === toolSelection.toolId);
            if (!tool) continue;

            // Get the plan to find its details
            const plan = tool.plans.find((p: any) => p.id === toolSelection.planId);
            if (!plan) continue;

            // Calculate end date (1 month from now)
            const startDate = new Date();
            const endDate = new Date();
            endDate.setMonth(endDate.getMonth() + 1);

            // Create subscription data
            const subscriptionData = {
              userId: response.id,
              toolId: toolSelection.toolId,
              planId: toolSelection.planId,
              status: 'active',
              startDate: startDate.toISOString(),
              endDate: endDate.toISOString(),
              notes: 'Created during user creation'
            };

            // Create the subscription
            console.log('Creating subscription with backend API');
            const newSubscription = await adminService.createSubscription(subscriptionData);
            console.log('Created new subscription:', newSubscription);
          }
        } catch (subscriptionError: any) {
          console.error('Error creating subscriptions:', subscriptionError);
          // We don't want to block user creation if subscription creation fails
          // Just log the error and continue
        }
      }

      // Show success message
      alert('User created successfully' +
        (assignTools && selectedTools.length > 0 ? ' with tool assignments' : ''));

      // Redirect to users page
      router.push('/admin/users');
    } catch (err: any) {
      console.error('Error creating user:', err);
      setError(err.message || 'Failed to create user');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Link
          href="/admin/users"
          className="mr-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
        </Link>
        <h1 className="text-2xl font-bold">Create New User</h1>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Key className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Shield className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    required
                    className="pl-10 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              </div>

              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active Account
                  </label>
                </div>
              </div>

              {/* Tool Assignment Section */}
              <div className="md:col-span-2 mt-6 border-t pt-6">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="assignTools"
                    name="assignTools"
                    checked={formData.assignTools}
                    onChange={(e) => setFormData({ ...formData, assignTools: e.target.checked })}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="assignTools" className="ml-2 block text-sm font-medium text-gray-700">
                    Assign Tools & Plans to User
                  </label>
                </div>

                {formData.assignTools && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-gray-900">Tool Assignments</h3>
                      <button
                        type="button"
                        onClick={handleAddTool}
                        className="inline-flex items-center px-3 py-1.5 bg-blue-100 hover:bg-blue-200 text-blue-700 text-sm font-medium rounded-md transition-colors"
                      >
                        <Package className="w-4 h-4 mr-1" />
                        Add Tool
                      </button>
                    </div>

                    {formData.selectedTools.length === 0 ? (
                      <div className="bg-gray-50 p-4 rounded-md text-center text-gray-500">
                        No tools assigned. Click "Add Tool" to assign tools to this user.
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {formData.selectedTools.map((toolSelection, index) => (
                          <div key={index} className="bg-gray-50 p-4 rounded-md">
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="font-medium text-gray-700">Tool Assignment #{index + 1}</h4>
                              <button
                                type="button"
                                onClick={() => handleRemoveTool(index)}
                                className="text-red-600 hover:text-red-800"
                              >
                                Remove
                              </button>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Select Tool
                                </label>
                                <select
                                  value={toolSelection.toolId}
                                  onChange={(e) => handleToolChange(index, 'toolId', e.target.value)}
                                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                                >
                                  <option value="">-- Select a Tool --</option>
                                  {tools.map((tool) => (
                                    <option key={tool.id} value={tool.id}>
                                      {tool.name}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                  Select Plan
                                </label>
                                <select
                                  value={toolSelection.planId}
                                  onChange={(e) => handleToolChange(index, 'planId', e.target.value)}
                                  disabled={!toolSelection.toolId}
                                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500 disabled:bg-gray-100 disabled:text-gray-500"
                                >
                                  <option value="">-- Select a Plan --</option>
                                  {toolSelection.toolId && tools.find(t => t.id === toolSelection.toolId)?.plans.map((plan: any) => (
                                    <option key={plan.id} value={plan.id}>
                                      {plan.name} - ${plan.price} / {plan.duration}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isLoading}
                className="inline-flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-5 h-5 border-2 border-gray-900 border-t-transparent rounded-full mr-2"
                    />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="w-5 h-5 mr-2" />
                    Create User
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
