'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import OrderHistory from '@/components/dashboard/OrderHistory';
import userService from '@/services/userService';
import { useNotification } from '@/contexts/NotificationContext';

export default function OrdersPage() {
  const [orders, setOrders] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { showNotification } = useNotification();

  useEffect(() => {
    fetchOrderHistory();
  }, []);

  const fetchOrderHistory = async () => {
    try {
      setIsLoading(true);
      const response = await userService.getOrderHistory();

      if (response?.success && response?.data) {
        setOrders(response.data);
      } else {
        setOrders([]);
      }

      setError(null);
    } catch (err: any) {
      console.error('Error fetching order history:', err);

      // Check if it's an authentication error
      if (err.message === 'Authentication required' ||
          err.message === 'No refresh token available') {
        setOrders([]);
        setError('Please log in to view your order history.');
      } else {
        setError('Failed to load order history. Please try again later.');
        showNotification('error', 'Failed to load order history', {
          autoClose: true,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-6 text-white shadow-lg">
          <h2 className="text-2xl font-bold mb-2">
            📦 Order History
          </h2>
          <p className="mb-4">
            View your past purchases and subscription details.
          </p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      <OrderHistory orders={orders} isLoading={isLoading} />
    </div>
  );
}
