'use client';

import { motion } from 'framer-motion';
import { AlertCircle, CheckCircle, CreditCard, DollarSign, Globe, Lock, XCircle } from 'lucide-react';
import CurvedShape from '../ui/CurvedShape';

const ModernProblemSection = () => {
  const problems = [
    {
      text: "International payment restrictions",
      icon: <XCircle size={20} className="text-red-500" />
    },
    {
      text: "Rejected credit cards",
      icon: <XCircle size={20} className="text-red-500" />
    },
    {
      text: "Currency conversion fees",
      icon: <XCircle size={20} className="text-red-500" />
    },
    {
      text: "Limited access to global tools",
      icon: <XCircle size={20} className="text-red-500" />
    }
  ];

  const solutions = [
    {
      text: "Pay with local Tunisian methods",
      icon: <CheckCircle size={20} className="text-green-500" />
    },
    {
      text: "Access premium global tools",
      icon: <CheckCircle size={20} className="text-green-500" />
    },
    {
      text: "No international cards needed",
      icon: <CheckCircle size={20} className="text-green-500" />
    },
    {
      text: "Instant access after payment",
      icon: <CheckCircle size={20} className="text-green-500" />
    }
  ];

  return (
    <section className="py-24 relative" id="problem">
      {/* Blob decorations */}
      <div className="blob-decoration blob-blue"></div>

      {/* Curved shapes */}
      <CurvedShape position="top" color="#4f8eff" />
      <CurvedShape position="bottom" color="#e94a9c" />

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-[#111111]">
            <span className="highlight">Solving</span> Payment Barriers
          </h2>
          <p className="text-xl text-[#333333] max-w-3xl mx-auto">
            We've built Partagily to help Tunisians overcome international payment restrictions
            while still accessing the best global tools and services.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Problem Card */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="gradient-card p-8 relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent"></div>

            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-full bg-red-500/10 flex items-center justify-center">
                <AlertCircle size={24} className="text-red-500" />
              </div>
              <h3 className="text-2xl font-bold text-[#111111]">The Problem</h3>
            </div>

            <p className="text-[#333333] mb-8">
              Tunisians face significant barriers when trying to access international
              subscription services and premium tools due to payment restrictions.
            </p>

            <div className="space-y-4">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.3 + (index * 0.1), duration: 0.5 }}
                  className="flex items-center gap-3 p-3 bg-[rgba(0,0,0,0.02)] rounded-lg"
                >
                  {problem.icon}
                  <span className="text-[#333333]">{problem.text}</span>
                </motion.div>
              ))}
            </div>

            <div className="absolute -bottom-20 -right-20 w-64 h-64 bg-red-500 opacity-5 rounded-full blur-3xl"></div>
          </motion.div>

          {/* Solution Card */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="gradient-card p-8 relative overflow-hidden"
          >
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-green-500 to-transparent"></div>

            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                <CheckCircle size={24} className="text-green-500" />
              </div>
              <h3 className="text-2xl font-bold text-[#111111]">Our Solution</h3>
            </div>

            <p className="text-[#333333] mb-8">
              Partagily bridges this gap by allowing you to use local payment methods
              while still gaining access to premium international tools.
            </p>

            <div className="space-y-4">
              {solutions.map((solution, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.5 + (index * 0.1), duration: 0.5 }}
                  className="flex items-center gap-3 p-3 bg-[rgba(0,0,0,0.02)] rounded-lg"
                >
                  {solution.icon}
                  <span className="text-[#333333]">{solution.text}</span>
                </motion.div>
              ))}
            </div>

            <div className="absolute -bottom-20 -left-20 w-64 h-64 bg-green-500 opacity-5 rounded-full blur-3xl"></div>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="mt-16 flex justify-center"
        >
          <div className="glass-card p-6 max-w-3xl">
            <div className="flex items-start gap-4">
              <div className="icon-badge icon-badge-yellow">
                <Lock size={20} />
              </div>
              <div>
                <h4 className="text-xl font-medium mb-2 text-[#111111]">How does it work?</h4>
                <p className="text-[#333333]">
                  Our platform uses secure cookie technology to provide you with access to premium tools.
                  You pay locally, and we handle the international payment barriers for you.
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernProblemSection;
