'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X } from 'lucide-react';

const WhatsAppWidget = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    // Show widget after 3 seconds
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleWhatsAppClick = () => {
    const message = encodeURIComponent(
      "Bonjour ! Je suis intéressé par Partagily et j'aimerais en savoir plus sur vos services."
    );
    window.open(`https://wa.me/21612345678?text=${message}`, '_blank');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            className="mb-4 bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-4 max-w-sm border border-gray-200 dark:border-gray-700"
          >
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 bg-[#25D366] rounded-full flex items-center justify-center">
                  <span className="text-white text-lg">💬</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Support Partagily</h4>
                  <p className="text-xs text-green-500">En ligne maintenant</p>
                </div>
              </div>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X size={16} />
              </button>
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
              Bonjour ! 👋 Comment pouvons-nous vous aider avec Partagily ?
            </p>
            
            <button
              onClick={handleWhatsAppClick}
              className="w-full bg-[#25D366] text-white py-2 px-4 rounded-lg font-medium hover:bg-[#20b358] transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <MessageCircle size={16} />
              Démarrer la conversation
            </button>
            
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
              Support en français et arabe 🇹🇳
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main WhatsApp Button */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{
          scale: 1,
          y: [0, -5, 0]
        }}
        transition={{
          scale: { delay: 0.5, type: "spring", stiffness: 200 },
          y: { repeat: Infinity, duration: 3, delay: 2 }
        }}
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-16 h-16 bg-[#25D366] rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center group hover:scale-110 ring-4 ring-white ring-opacity-30"
      >
        <AnimatePresence mode="wait">
          {isExpanded ? (
            <motion.div
              key="close"
              initial={{ rotate: -90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: 90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <X size={24} className="text-white" />
            </motion.div>
          ) : (
            <motion.div
              key="whatsapp"
              initial={{ rotate: 90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: -90, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="relative"
            >
              <MessageCircle size={28} className="text-white" />
              
              {/* Notification dot */}
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
                className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
              />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* Pulse animation for attention */}
      {!isExpanded && (
        <motion.div
          animate={{ scale: [1, 1.4, 1], opacity: [0.7, 0, 0.7] }}
          transition={{ repeat: Infinity, duration: 2, delay: 1 }}
          className="absolute inset-0 w-16 h-16 bg-[#25D366] rounded-full"
        />
      )}
    </div>
  );
};

export default WhatsAppWidget;
