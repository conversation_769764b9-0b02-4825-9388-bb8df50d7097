import { Controller, Get, Post, Delete, Param, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../guards/admin.guard';
import { RateLimitService } from '../../common/services/rate-limit.service';

/**
 * Admin Security Controller
 * 
 * This controller provides endpoints to manage security features like block lists.
 */
@ApiTags('admin-security')
@Controller('admin/security')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth()
export class AdminSecurityController {
  constructor(private readonly rateLimitService: RateLimitService) {}
  
  /**
   * Get the list of blocked IPs
   */
  @Get('blocked-ips')
  @ApiOperation({ summary: 'Get list of blocked IPs' })
  @ApiResponse({ status: 200, description: 'List of blocked IPs' })
  getBlockedIps() {
    return {
      blockedIps: this.rateLimitService.getBlockList(),
    };
  }
  
  /**
   * Block an IP address
   */
  @Post('block-ip')
  @ApiOperation({ summary: 'Block an IP address' })
  @ApiResponse({ status: 200, description: 'IP blocked successfully' })
  blockIp(@Body() body: { ip: string; duration?: number }) {
    const { ip, duration } = body;
    const success = this.rateLimitService.blockIp(ip, duration);
    
    return {
      success,
      message: success ? `IP ${ip} blocked successfully` : `Failed to block IP ${ip}`,
    };
  }
  
  /**
   * Unblock an IP address
   */
  @Delete('unblock-ip/:ip')
  @ApiOperation({ summary: 'Unblock an IP address' })
  @ApiResponse({ status: 200, description: 'IP unblocked successfully' })
  unblockIp(@Param('ip') ip: string) {
    const success = this.rateLimitService.unblockIp(ip);
    
    return {
      success,
      message: success ? `IP ${ip} unblocked successfully` : `IP ${ip} was not blocked`,
    };
  }
}
