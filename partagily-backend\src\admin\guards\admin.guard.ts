import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

/**
 * Admin Guard
 *
 * This guard ensures that only users with admin role can access protected routes.
 */
@Injectable()
export class AdminGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Check if user is already authenticated by JwtAuthGuard
    if (request.user) {
      console.log('User already authenticated:', request.user);

      // Check if user has admin role
      if (request.user.role === 'admin') {
        console.log('User has admin role, allowing access');
        return true;
      } else {
        console.log('User does not have admin role, denying access');
        throw new UnauthorizedException('You do not have permission to access this resource');
      }
    }

    // If no user in request, try to authenticate with token
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      console.log('No authentication token found');
      throw new UnauthorizedException('Authentication token is missing');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      console.log('Token payload:', payload);

      // Check if user has admin role
      if (!payload.role || payload.role.toLowerCase() !== 'admin') {
        console.log('User does not have admin role, denying access');
        throw new UnauthorizedException('You do not have permission to access this resource');
      }

      // Attach user to request
      request.user = payload;
      console.log('User authenticated as admin, allowing access');
      return true;
    } catch (error) {
      console.log('Token verification failed:', error.message);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
