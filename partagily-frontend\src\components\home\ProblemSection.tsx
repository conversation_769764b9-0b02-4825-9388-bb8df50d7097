'use client';

import { motion } from 'framer-motion';

const ProblemSection = () => {
  return (
    <section className="py-16 bg-white dark:bg-gray-900" id="problem">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-6 terminal-text">
            <span className="text-yellow-400">💸</span> Facing international payment issues in Tunisia?
          </h2>

          <p className="text-xl mb-8 text-gray-700 dark:text-gray-300">
            We've got you. Partagily lets you subscribe with local payments and still access top global tools.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="bg-pink-50 dark:bg-[rgba(233,74,156,0.08)] p-6 rounded-xl border border-transparent dark:border-[rgba(233,74,156,0.2)] shadow-md hover-card"
            >
              <div className="text-4xl mb-4">😞</div>
              <h3 className="text-xl font-bold mb-3 terminal-text">The Problem</h3>
              <ul className="text-left space-y-2 text-gray-700 dark:text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-red-500">✖</span>
                  <span>International payment restrictions</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-500">✖</span>
                  <span>Limited access to premium tools</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-500">✖</span>
                  <span>High subscription costs</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-red-500">✖</span>
                  <span>Currency conversion fees</span>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="bg-blue-50 dark:bg-[rgba(79,142,255,0.08)] p-6 rounded-xl border border-transparent dark:border-[rgba(79,142,255,0.2)] shadow-md hover-card"
            >
              <div className="text-4xl mb-4">😃</div>
              <h3 className="text-xl font-bold mb-3 terminal-text">Our Solution</h3>
              <ul className="text-left space-y-2 text-gray-700 dark:text-gray-300">
                <li className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Pay with local Tunisian methods</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Access 120+ premium tools</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>Save up to 80% on subscriptions</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-green-500">✓</span>
                  <span>No international payment needed</span>
                </li>
              </ul>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProblemSection;
