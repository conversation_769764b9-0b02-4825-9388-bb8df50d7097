import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEmail,
  IsN<PERSON>ber,
  IsString,
} from "class-validator";

export class CreateKonnectDto {
  @IsString()
  receiverWalletId: string;

  @IsString()
  token: string;

  @IsNumber()
  amount: number;

  @IsString()
  type: string;

  @IsString()
  description: string;

  @IsArray()
  acceptedPaymentMethods: string[];

  @IsNumber()
  lifespan: number;

  @IsBoolean()
  checkoutForm: boolean;

  @IsBoolean()
  addPaymentFeesToAmount: boolean;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsString()
  phoneNumber: string;
  @IsEmail()
  email: string;

  @IsString()
  orderId: string;

  @IsString()
  webhook: string;

  @IsString()
  theme: string;
}
