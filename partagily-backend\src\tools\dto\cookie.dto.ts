import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, IsDateString, IsArray } from 'class-validator';

export class CookieDto {
  @ApiProperty({
    description: 'Cookie name',
    example: 'NetflixId',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Cookie value',
    example: 'ct%3DBgjHlOvcAxLJAzqif7OkpE3euD5ThAzE5aVbG6QWShqWParCLVjeZWCR1Nxi4bd75pmkNuAi_idh5mFaVe0rgwOkKdqxl0cIP3_s9GnK2iP7IazlDWASLDdQVe9qtod-JDETXuypJTPXSglH-p1Z3J9s-7kEmvkdZkewe6zbLIzs1DN3B4A6CT7d0ZPKtPF97_MKfVCtUZ-VhqZSummLvKZR7NQFI5KwGMNrhIvF_qGp6_RF9G4-k8ZgyKz2l64O1gDtWbSyB2fuvogGtgw3Bs2Ap8Ua80Fv8YXYdnKMByGc3mpkjZKnCzqufKi0WogDsGFx4a90wwWv-tIvDvk2CTRME1d_vsyqaSQ0ot1iNMGcgbTsBi-KjeKCtjPS4zN6284IGZMh0IO0FKqhj825liAxas3y1bhQrzC--wQBART33B5Ri_RPpIixZ6jeXZ211rnxZyl-9nRYhPzKFQyF6MTi0OxAN6a3VD31m4VHaJYreL10wytkBeLkAIempsUPI2st1mM4ejkAg_Ex58Rqh3RG9x8gkzAd9QI7Ztzi-rhIt4lTpoFYknimXNMk7E3MPI-xZWvl6iXbO3DXi_EMlgAyGNblCg0pZT1WrtpbrKigoJkYBiIOCgy5Bw27fiDgO5uLCuM.%26ch%3DAQEAEAABABSOEQIKuuTe_eQeXL7j6pyVuF4Fi7iISbs.%26v%3D3',
  })
  @IsString()
  value: string;

  @ApiProperty({
    description: 'Cookie domain',
    example: '.netflix.com',
  })
  @IsString()
  domain: string;

  @ApiProperty({
    description: 'Cookie path',
    example: '/',
    default: '/',
  })
  @IsString()
  @IsOptional()
  path?: string;

  @ApiProperty({
    description: 'Cookie expiration date',
    example: '2025-04-25T19:45:35.080Z',
  })
  @IsDateString()
  expirationDate: string;

  @ApiProperty({
    description: 'Whether the cookie is secure',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  secure?: boolean;

  @ApiProperty({
    description: 'Whether the cookie is HTTP only',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  httpOnly?: boolean;

  @ApiProperty({
    description: 'SameSite attribute',
    example: 'lax',
    default: 'lax',
  })
  @IsString()
  @IsOptional()
  sameSite?: string;
}

export class CreateCookiesDto {
  @ApiProperty({
    description: 'Tool ID',
    example: '1',
  })
  @IsString()
  toolId: string;

  @ApiProperty({
    description: 'Array of cookies',
    type: [CookieDto],
  })
  @IsArray()
  cookies: CookieDto[];
}

export class GetCookiesResponseDto {
  @ApiProperty({
    description: 'Array of cookies',
    type: [CookieDto],
  })
  @IsArray()
  cookies: CookieDto[];
}
