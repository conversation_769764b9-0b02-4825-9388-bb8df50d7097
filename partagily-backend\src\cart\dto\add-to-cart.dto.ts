import { <PERSON><PERSON>num, IsNotEmpty, IsN<PERSON>ber, <PERSON><PERSON>tring, IsUUI<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ItemType {
  TOOL = 'TOOL',
  PLAN = 'PLAN',
}

export class AddToCartDto {
  @ApiProperty({
    description: 'Type of item (TOOL or PLAN)',
    enum: ItemType,
    example: 'TOOL',
  })
  @IsNotEmpty()
  @IsEnum(ItemType)
  type: ItemType;

  @ApiProperty({
    description: 'ID of the item (tool or plan)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  itemId: string;
}
