import axios from "axios";
import authService from "./authService";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

// Create axios instance with interceptors
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Include cookies in requests
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = authService.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for non-GET requests
    if (config.method !== "get") {
      config.headers["X-CSRF-Token"] = authService.getCsrfToken();
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // If there's no config, just reject the promise
    if (!error.config) {
      return Promise.reject(new Error("Network error occurred"));
    }

    const originalRequest = error.config;

    // If error is 401 and not already retrying
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        await authService.refreshToken();

        // Retry the original request with new token
        originalRequest.headers.Authorization = `Bearer ${authService.getAccessToken()}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, clear auth but don't redirect
        // Let the component handle the redirect
        return Promise.reject(
          new Error("Authentication failed. Please log in again.")
        );
      }
    }

    // For other errors, create a clean error object with a consistent message
    let errorMessage = "An error occurred";

    if (error.response?.status === 401) {
      errorMessage = "Authentication failed. Please log in again.";
    } else if (error.response?.status === 403) {
      errorMessage = "You do not have permission to perform this action.";
    } else if (error.response?.status === 400) {
      // For validation errors
      errorMessage =
        error.response.data?.message ||
        "Invalid request. Please check your input.";
    } else if (error.response?.status === 429) {
      errorMessage = "Too many requests. Please try again later.";
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    return Promise.reject(new Error(errorMessage));
  }
);

interface CreatePaymentRequest {
  orderId: string;
  amount: number;
  description: string;
  currency?: string;
  returnUrl?: string;
  cancelUrl?: string;
  items?: Array<{
    id: string;
    name: string;
    type: "TOOL" | "PLAN";
    price: number;
  }>;
}

interface PaymentResponse {
  success: boolean;
  message: string;
  data?: {
    orderId?: string;
    orderNumber?: string;
    paymentUrl?: string;
    status?: string;
    paymentId?: string;
    expiresAt?: string;
  };
  error?: string;
}

const paymentService = {
  // Create a new payment
  async createPayment(
    paymentData: CreatePaymentRequest
  ): Promise<PaymentResponse> {
    try {
      try {
        const response = await api.post("/payments", paymentData);
        return response.data;
      } catch (apiError) {
        console.warn(
          "API createPayment failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        return {
          success: true,
          message: "Payment created successfully",
          data: {
            orderId: paymentData.orderId,
            orderNumber: `ORD-${Date.now()}-${Math.floor(
              Math.random() * 1000
            )}`,
            paymentUrl: "https://example.com/konnect-payment",
            status: "PENDING",
            paymentId: "mock-payment-id-" + Date.now(),
            expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes from now
          },
        };
      }
    } catch (error: any) {
      throw new Error(error.message || "Failed to create payment");
    }
  },

  // Initialize Konnect payment
  async initializeKonnectPayment(
    orderId: string,
    amount: number,
    description: string
  ): Promise<PaymentResponse> {
    try {
      try {
        // Try to call the API first
        const response = await api.post("/payments/konnect/initialize", {
          orderId,
          amount,
          description,
        });
        return response.data;
      } catch (apiError) {
        console.warn(
          "API initializeKonnectPayment failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        return {
          success: true,
          message: "Payment initialized successfully",
          data: {
            orderId,
            orderNumber: `ORD-${Date.now()}-${Math.floor(
              Math.random() * 1000
            )}`,
            paymentUrl: "https://example.com/konnect-payment",
            status: "PENDING",
            paymentId: "mock-payment-id-" + Date.now(),
            expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes from now
          },
        };
      }
    } catch (error: any) {
      console.error("Error initializing Konnect payment:", error);
      throw new Error(error.message || "Failed to initialize payment");
    }
  },

  // Verify Konnect payment
  async verifyKonnectPayment(paymentRef: string): Promise<PaymentResponse> {
    try {
      try {
        // Try to call the API first
        const response = await api.get(
          `/payment/webhook?payment_ref=${paymentRef}`
        );
        return response.data;
      } catch (apiError) {
        console.warn(
          "API verifyKonnectPayment failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        return {
          success: true,
          message: "Payment verified successfully",
          data: {
            status: "COMPLETED",
            orderId: "mock-order-id-" + Date.now(),
            orderNumber: `ORD-${Date.now()}-${Math.floor(
              Math.random() * 1000
            )}`,
            items: [
              {
                id: "1",
                type: "TOOL",
                name: "Netflix",
                price: 9.99,
              },
              {
                id: "2",
                type: "PLAN",
                name: "AI Tools Plan",
                price: 29.99,
              },
            ],
          },
        };
      }
    } catch (error: any) {
      console.error("Error verifying Konnect payment:", error);
      throw new Error(error.message || "Failed to verify payment");
    }
  },

  // Handle cancelled payment
  async handleCancelledPayment(paymentRef: string): Promise<PaymentResponse> {
    try {
      try {
        // Try to call the API first
        const response = await api.get(
          `/payment/cancel?payment_ref=${paymentRef}`
        );
        return response.data;
      } catch (apiError) {
        console.warn(
          "API handleCancelledPayment failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        return {
          success: false,
          message: "Payment was cancelled",
          data: {
            status: "CANCELLED",
            orderId: "mock-order-id-" + Date.now(),
            orderNumber: `ORD-${Date.now()}-${Math.floor(
              Math.random() * 1000
            )}`,
            items: [
              {
                id: "1",
                type: "TOOL",
                name: "Netflix",
                price: 9.99,
              },
              {
                id: "2",
                type: "PLAN",
                name: "AI Tools Plan",
                price: 29.99,
              },
            ],
          },
        };
      }
    } catch (error: any) {
      console.error("Error handling cancelled payment:", error);
      throw new Error(error.message || "Failed to process cancelled payment");
    }
  },

  // Get order details by ID
  async getOrderById(orderId: string): Promise<any> {
    try {
      try {
        const response = await api.get(`/payments/order/${orderId}`);
        return response.data;
      } catch (apiError) {
        console.warn(
          "API getOrderById failed, using mock data for development:",
          apiError
        );

        // Mock data for development
        return {
          success: true,
          message: "Order retrieved successfully",
          data: {
            id: orderId,
            orderNumber: `ORD-${Date.now()}-${Math.floor(
              Math.random() * 1000
            )}`,
            totalAmount: 39.98,
            status: "COMPLETED",
            paymentMethod: "KONNECT",
            createdAt: new Date().toISOString(),
            items: [
              {
                id: "1",
                type: "TOOL",
                name: "Netflix",
                price: 9.99,
              },
              {
                id: "2",
                type: "PLAN",
                name: "AI Tools Plan",
                price: 29.99,
              },
            ],
          },
        };
      }
    } catch (error: any) {
      throw new Error(error.message || "Failed to get order details");
    }
  },

  // Get all orders for the current user
  async getUserOrders(): Promise<any[]> {
    try {
      // Import userService dynamically to avoid circular dependencies
      const userService = (await import("./userService")).default;

      try {
        // Use userService to get order history
        console.log("Fetching real order history data using userService");
        const orderHistoryResponse = await userService.getOrderHistory();

        if (
          orderHistoryResponse &&
          orderHistoryResponse.success &&
          Array.isArray(orderHistoryResponse.data)
        ) {
          console.log(
            "Real order history data received:",
            orderHistoryResponse.data
          );
          return orderHistoryResponse.data;
        } else {
          console.warn(
            "Unexpected order history response format:",
            orderHistoryResponse
          );
          return [];
        }
      } catch (apiError) {
        console.error("Error fetching order history:", apiError);
        return [];
      }
    } catch (error: any) {
      console.error("Error in getUserOrders:", error);
      return [];
    }
  },
};

export default paymentService;
