"use client";

import React, { useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { ShoppingCart } from "lucide-react";
// import { useCart } from "@/contexts/CartContext";
import { useNotification } from "@/contexts/NotificationContext";
import useCart from "@/store/cart";
import { ToolType } from "@/store/types";

import nextSVG from "../../../public/next.svg";

interface ToolCardProps {
  tool: ToolType & {
    originalPrice?: number;
    isPurchased?: boolean;
  };
  isPurchased?: boolean;
}

const ToolCard: React.FC<ToolCardProps> = ({ tool, isPurchased = false }) => {
  // const { addToCart, isLoading } = useCart();
  const { addItemCart, handleToggleCart } = useCart();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { showNotification } = useNotification();
  const discount = tool.originalPrice
    ? Math.round(((tool.originalPrice - tool.price) / tool.originalPrice) * 100)
    : 0;
  const isToolPurchased = isPurchased || tool.isPurchased;

  const handleAddToCart = () => {
    setIsLoading(true);
    console.log("ToolCard - handleAddToCart clicked for tool:", tool.id);
    addItemCart([tool]);
    // showNotification(
    //   "success",
    //   `${tool.type === "TOOL" ? "Tool" : "Plan"} added to cart`,
    //   {
    //     autoClose: true,
    //   }
    // );
    handleToggleCart(true);
    setIsLoading(false);
  };

  // Only log in development mode and not on every render
  if (process.env.NODE_ENV === "development" && tool.id) {
    // Using a more specific condition to avoid excessive logging
    console.log(
      `ToolCard render: ${tool.name} (${tool.id.substring(0, 8)}...)`
    );
  }

  return (
    <motion.div
      className="bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col border border-gray-200 dark:border-gray-700"
      whileHover={{
        y: -5,
        boxShadow:
          "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      }}
      data-tool-id={tool.id}
    >
      {/* Tool Icon/Logo */}
      <div className="p-6 pb-0 flex justify-center">
        <div className="w-16 h-16 rounded-lg bg-gray-100 dark:bg-slate-700 flex items-center justify-center overflow-hidden">
          {tool.icon &&
          typeof tool.icon === "string" &&
          !tool.icon.includes("undefined") ? (
            <Image
              src={nextSVG}
              alt={tool.name}
              width={64}
              height={64}
              onError={(e) => {
                console.log("Image failed to load for tool:", tool.name);
                // If image fails to load, replace with fallback
                (e.target as HTMLImageElement).style.display = "none";
                const parent = (e.target as HTMLImageElement).parentElement;
                if (parent) {
                  const fallback = document.createElement("div");
                  fallback.className = "text-3xl";
                  fallback.textContent = "🔧";
                  parent.appendChild(fallback);
                }
              }}
            />
          ) : (
            <div className="text-3xl">🔧</div>
          )}
        </div>
      </div>

      <div className="p-6 flex flex-col flex-grow">
        {/* Tool Name */}
        <h3 className="text-xl font-bold text-gray-900 dark:text-white text-center mb-3 tool-name">
          {tool.name}
        </h3>

        {/* Tool Description */}
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-6 line-clamp-2 flex-grow text-center">
          {tool.description}
        </p>

        {/* Price and Action */}
        <div className="mt-auto">
          {/* Price */}
          <div className="flex justify-center items-center mb-4">
            <span className="text-xl font-bold text-gray-900 dark:text-white tool-price">
              ${typeof tool.price === "number" ? tool.price.toFixed(2) : "0.00"}
            </span>
            {tool.originalPrice && typeof tool.originalPrice === "number" && (
              <span className="ml-2 text-sm text-gray-500 dark:text-gray-400 line-through">
                ${tool.originalPrice.toFixed(2)}
              </span>
            )}
            {discount > 0 && (
              <span className="ml-2 text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-2 py-0.5 rounded-full">
                -{discount}%
              </span>
            )}
          </div>

          {/* Action Button */}
          {isToolPurchased ? (
            <div className="flex justify-center">
              <span className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Purchased
              </span>
            </div>
          ) : (
            <div className="flex justify-center">
              <button
                onClick={handleAddToCart}
                disabled={isLoading}
                className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-2 px-4 rounded-lg text-sm flex items-center transition-all duration-200 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black mr-2"></div>
                    Adding...
                  </>
                ) : (
                  <>
                    <ShoppingCart size={16} className="mr-2" />
                    Add to Cart
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Use React.memo to prevent unnecessary re-renders
export default React.memo(ToolCard, (prevProps, nextProps) => {
  // Only re-render if the tool data or isPurchased status has changed
  return (
    prevProps.tool.id === nextProps.tool.id &&
    prevProps.tool.price === nextProps.tool.price &&
    prevProps.isPurchased === nextProps.isPurchased
  );
});
