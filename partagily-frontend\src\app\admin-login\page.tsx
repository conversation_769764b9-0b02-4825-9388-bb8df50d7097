'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import './admin-auth-styles.css';
import authService from '@/services/authService';

export default function AdminLogin() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isAlreadyLoggedIn, setIsAlreadyLoggedIn] = useState(false);
  const router = useRouter();

  // Check if admin is already logged in
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // Check if user is authenticated
        if (authService.isAuthenticated()) {
          // Get current user from API
          const user = await authService.getCurrentUser();

          console.log('Current user from API:', user);

          // Check if user is an admin
          if (user && user.role === 'admin') {
            setIsAlreadyLoggedIn(true);
            // Update admin user in localStorage
            localStorage.setItem('adminUser', JSON.stringify(user));
          }
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      }
    };

    checkAdminStatus();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setErrorMessage('Please enter both email and password');
      return;
    }

    try {
      setIsSubmitting(true);
      setErrorMessage('');

      // Use the real authentication service
      const user = await authService.login(email, password, true);

      console.log('Login response:', user);

      // Check if the user is an admin
      if (user && user.role === 'admin') {
        // Store the user object in localStorage for admin-specific checks
        localStorage.setItem('adminUser', JSON.stringify(user));

        console.log('Admin login successful, stored user data:', user);

        // Redirect to admin dashboard
        router.push('/admin');
      } else {
        setErrorMessage('You do not have admin privileges');
        // Clear any tokens that might have been set
        authService.logout();
      }
    } catch (error: any) {
      console.error('Admin login error:', error);
      setErrorMessage(error.message || 'Login failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="auth-container">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="auth-card"
      >
        <div className="px-6 py-8 sm:p-10">
          <div className="auth-header">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="auth-title">
                Admin Login <span className="auth-highlight">!</span>
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Or{' '}
                <Link href="/signin" className="auth-link">
                  go to regular login
                </Link>
              </p>
            </motion.div>
          </div>

          {errorMessage && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg"
            >
              <div className="flex items-start">
                <svg className="w-5 h-5 mr-2 mt-0.5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>{errorMessage}</span>
              </div>
            </motion.div>
          )}

          {isAlreadyLoggedIn && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg"
            >
              <div className="flex items-start">
                <svg className="w-5 h-5 mr-2 mt-0.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>You are already logged in as admin!</span>
              </div>
              <div className="mt-2">
                <button
                  onClick={() => router.push('/admin')}
                  className="w-full py-2 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                >
                  Go to Admin Dashboard
                </button>
              </div>
            </motion.div>
          )}

          <form className="space-y-8" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 terminal-text">
                Admin Email
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="auth-input"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 terminal-text">
                Admin Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="auth-input"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="auth-button"
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </span>
                ) : (
                  'Admin Sign in'
                )}
              </button>
            </div>
          </form>
        </div>

        <div className="auth-footer"></div>
      </motion.div>
    </div>
  );
}
