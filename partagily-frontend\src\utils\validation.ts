/**
 * Validation utility functions for Partagily frontend
 */

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation
export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number or special character
  const passwordRegex = /((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/;
  return password.length >= 8 && passwordRegex.test(password);
};

export const isValidConfirmPassword = (password: string, confirmPassword: string): boolean => {
  // First check if confirmPassword exists and has a length
  if (!confirmPassword || confirmPassword.length === 0) {
    return false;
  }

  // Then check if it matches the password
  return password === confirmPassword;
}

// Name validation
export const isValidName = (name: string): boolean => {
  return name.trim().length >= 2;
};

// URL validation
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

// Credit card validation (Luhn algorithm)
export const isValidCreditCard = (cardNumber: string): boolean => {
  const sanitized = cardNumber.replace(/\D/g, '');
  if (sanitized.length < 13 || sanitized.length > 19) return false;

  let sum = 0;
  let double = false;

  // Loop from right to left
  for (let i = sanitized.length - 1; i >= 0; i--) {
    let digit = parseInt(sanitized.charAt(i));

    // Double every second digit
    if (double) {
      digit *= 2;
      if (digit > 9) digit -= 9;
    }

    sum += digit;
    double = !double;
  }

  return sum % 10 === 0;
};

// Phone number validation
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^\+?[0-9]{10,15}$/;
  return phoneRegex.test(phone.replace(/\D/g, ''));
};

// Date validation (YYYY-MM-DD)
export const isValidDate = (date: string): boolean => {
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(date)) return false;

  const d = new Date(date);
  return d instanceof Date && !isNaN(d.getTime());
};

// Form validation helper
export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Generic form validator
export const validateForm = <T extends Record<string, any>>(
  data: T,
  rules: Record<keyof T, (value: any) => boolean>,
  errorMessages: Record<keyof T, string>
): ValidationResult => {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const field in rules) {
    if (Object.prototype.hasOwnProperty.call(rules, field)) {
      const isFieldValid = rules[field](data[field]);
      if (!isFieldValid) {
        errors[field] = errorMessages[field];
        isValid = false;
      }
    }
  }

  return { isValid, errors };
};

// Sanitize input to prevent XSS
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

// Sanitize object values
export const sanitizeObject = <T extends Record<string, any>>(obj: T): T => {
  const result = { ...obj };

  for (const key in result) {
    if (typeof result[key] === 'string') {
      result[key] = sanitizeInput(result[key]) as T[Extract<keyof T, string>];
    }
  }

  return result;
};

// UUID validation
export const isValidUUID = (uuid: string): boolean => {
  // UUID v4 regex pattern
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidPattern.test(uuid);
};

// Convert any ID to a valid UUID format
export const convertToUUID = (id: string): string => {
  // If it's already a valid UUID, return it
  if (isValidUUID(id)) {
    return id;
  }

  // For numeric IDs, convert to a deterministic UUID
  if (/^\d+$/.test(id)) {
    // Create a deterministic UUID based on the numeric ID
    // This ensures the same numeric ID always maps to the same UUID
    const numericId = id.padStart(12, '0');
    return `00000000-0000-4000-a000-${numericId}`;
  }

  // For string IDs like 'netflix', 'adobe', etc.
  if (/^[a-z0-9-]+$/i.test(id)) {
    // Create a deterministic UUID based on the string ID
    // This ensures the same string ID always maps to the same UUID
    let hash = 0;
    for (let i = 0; i < id.length; i++) {
      const char = id.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Convert the hash to a hex string and pad it
    const hashHex = Math.abs(hash).toString(16).padStart(12, '0');
    return `00000000-0000-4000-a000-${hashHex}`;
  }

  // If all else fails, generate a random UUID
  return '00000000-0000-4000-a000-000000000000';
};
