// Content script for the Partagily platform
// This script runs on the Partagily website to enable communication with the extension

console.log('Partagily: Platform content script loaded');

// Store the JWT token when it's available in localStorage
let jwtToken = null;

// Function to extract JWT token from localStorage or sessionStorage
function extractJwtToken() {
  try {
    // Try to get the token from localStorage first
    let token = localStorage.getItem('accessToken');

    // If not in localStorage, try sessionStorage
    if (!token) {
      token = sessionStorage.getItem('accessToken');
    }

    // If we found a token, store it and return it
    if (token) {
      jwtToken = token;
      console.log('JWT token extracted successfully');
      return token;
    }

    // For mock data in development
    if (window.location.hostname.includes('localhost') ||
        window.location.hostname.includes('127.0.0.1')) {

      // Check if we have a mock user role
      const mockUserRole = localStorage.getItem('mockUserRole') || sessionStorage.getItem('mockUserRole');

      if (mockUserRole) {
        // Generate a mock token
        const mockToken = 'mock-access-token-' + Date.now();
        console.log('Generated mock token for development');
        jwtToken = mockToken;
        return mockToken;
      }
    }

    console.warn('No JWT token found in storage');
    return null;
  } catch (error) {
    console.error('Error extracting JWT token:', error);
    return null;
  }
}

// Check for token on page load
document.addEventListener('DOMContentLoaded', () => {
  extractJwtToken();
});

// Listen for localStorage changes to capture token updates
window.addEventListener('storage', (event) => {
  if (event.key === 'accessToken' && event.newValue) {
    jwtToken = event.newValue;
  }
});

// Listen for custom events from the platform
window.addEventListener('message', async (event) => {
  // Only accept messages from the same origin
  if (event.origin !== window.location.origin) return;

  const { action, data } = event.data || {};

  if (!action) return;

  console.log('Received message from platform:', action, data);

  switch (action) {
    case 'getAccess':
      handleGetAccess(data);
      break;

    case 'checkExtensionStatus':
      // Respond to the platform that the extension is installed
      window.postMessage(
        {
          action: 'extensionStatus',
          data: { installed: true }
        },
        window.location.origin
      );
      break;
  }
});

// Handle "Get Access" button clicks
async function handleGetAccess(data) {
  if (!data || !data.toolId || !data.toolUrl) {
    console.error('Invalid tool data for access');
    notifyPlatform('accessError', {
      message: 'Invalid tool data',
      toolId: data?.toolId
    });
    return;
  }

  try {
    // Extract token if not already available
    if (!jwtToken) {
      jwtToken = extractJwtToken();
    }

    // Check if chrome.runtime is available
    if (!chrome || !chrome.runtime) {
      // Extension context might be invalidated, try to reload the page
      console.warn('Extension context may be invalidated, suggesting page reload');
      notifyPlatform('accessError', {
        message: 'Extension needs to be reloaded. Please refresh the page and try again.',
        toolId: data.toolId,
        requiresReload: true
      });
      return;
    }

    // Log the token for debugging
    console.log('Using token for tool access:', jwtToken ? 'Token available' : 'No token');

    // Open the tool URL in a new tab
    const response = await chrome.runtime.sendMessage({
      action: 'openToolTab',
      data: {
        toolId: data.toolId,
        url: data.toolUrl,
        token: jwtToken
      }
    }).catch(err => {
      // Handle specific error for invalidated context
      if (err.message && err.message.includes('Extension context invalidated')) {
        console.warn('Extension context invalidated, suggesting page reload');
        notifyPlatform('accessError', {
          message: 'Extension context invalidated. Please refresh the page and try again.',
          toolId: data.toolId,
          requiresReload: true
        });
        return null;
      }
      throw err; // Re-throw other errors
    });

    // If response is null, we've already handled the error
    if (response === null) return;

    // Notify the platform that the process has started
    notifyPlatform('accessStarted', {
      toolId: data.toolId,
      tabId: response?.tabId,
      hasAccess: response?.hasAccess,
      message: response?.message
    });
  } catch (error) {
    console.error('Error handling get access:', error);

    // Check for specific error types
    let errorMessage = error.message || 'Failed to access tool';
    let requiresReload = false;

    if (errorMessage.includes('Extension context invalidated') ||
        !chrome || !chrome.runtime) {
      errorMessage = 'Extension needs to be reloaded. Please refresh the page and try again.';
      requiresReload = true;
    }

    notifyPlatform('accessError', {
      message: errorMessage,
      toolId: data.toolId,
      requiresReload
    });
  }
}

// Send notifications back to the platform
function notifyPlatform(action, data) {
  window.postMessage(
    { action, data },
    window.location.origin
  );
}

// Expose a function that the platform can call directly
window.partagilyExtension = {
  getAccess: (toolId, toolUrl) => {
    handleGetAccess({ toolId, toolUrl });
  },

  isInstalled: () => {
    return true;
  }
};

// Notify the platform that the extension is installed using a safer approach
function notifyExtensionInstalled() {
  // Create a custom element to communicate with the page
  const notifier = document.createElement('div');
  notifier.id = 'partagily-extension-notifier';
  notifier.style.display = 'none';
  document.body.appendChild(notifier);

  // Dispatch a custom event that the page can listen for
  const event = new CustomEvent('partagilyExtensionInstalled', {
    detail: { installed: true }
  });
  document.dispatchEvent(event);

  // Set a data attribute that the page can check
  notifier.dataset.extensionInstalled = 'true';

  console.log('Notified platform of extension installation');
}

// Run the installation notifier when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', notifyExtensionInstalled);
} else {
  notifyExtensionInstalled();
}

// Listen for clicks on elements with data-partagily-tool attribute
document.addEventListener('click', (event) => {
  // Find the closest element with the data attribute
  const toolElement = event.target.closest('[data-partagily-tool]');

  if (toolElement) {
    const toolId = toolElement.getAttribute('data-partagily-tool');
    const toolUrl = toolElement.getAttribute('data-partagily-url');

    if (toolId && toolUrl) {
      event.preventDefault();
      handleGetAccess({ toolId, toolUrl });
    }
  }
});
