# Partagily Tech Stack

## Frontend
- Next.js 15.0
- TypeScript
- Tailwind CSS 3.4.0
- React Query for data fetching
- Atomic Design principles
- Responsive design

## Backend
- NestJS 10.2.0
- TypeScript
- PostgreSQL 16.1
- Prisma ORM
- JWT authentication
- OpenAPI/Swagger for documentation
- Domain-driven design

## Chrome Extension
- Manifest V3
- TypeScript
- Service Workers
- Content Scripts
- Background Scripts
- declarativeNetRequest API
- Secure messaging

## DevOps
- Git for version control
- Docker for containerization
- CI/CD pipeline
- Automated testing
