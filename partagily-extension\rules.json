[{"id": 1, "priority": 1, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Content-Security-Policy", "operation": "remove"}, {"header": "Content-Security-Policy-Report-Only", "operation": "remove"}, {"header": "X-Content-Security-Policy", "operation": "remove"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "other"]}}, {"id": 2, "priority": 2, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Content-Security-Policy", "operation": "append", "value": "default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'self' 'unsafe-inline' 'unsafe-eval'; connect-src * 'self'; img-src * 'self' data: blob:; style-src * 'self' 'unsafe-inline'; frame-src * 'self'; worker-src * 'self' blob:; font-src * 'self' data:; media-src * 'self' data: blob:;"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 3, "priority": 3, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "X-Frame-Options", "operation": "remove"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 4, "priority": 4, "action": {"type": "modifyHeaders", "responseHeaders": [{"header": "Access-Control-Allow-Origin", "operation": "append", "value": "*"}]}, "condition": {"urlFilter": "*://*/*", "resourceTypes": ["main_frame", "sub_frame", "stylesheet", "script", "image", "font", "object", "xmlhttprequest", "ping", "csp_report", "media", "websocket", "other"]}}]