import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from "@nestjs/common";
import { KonnectService } from "./konnect.service";
import { CreateKonnectDto } from "./dto/create-konnect.dto";
import { UpdateKonnectDto } from "./dto/update-konnect.dto";
import { GetKonnectDto } from "./dto/get-konnect-dto";

@Controller("konnect")
export class KonnectController {
  constructor(private readonly konnectService: KonnectService) {}

  @Post()
  async create(@Body() createKonnectDto: any): Promise<GetKonnectDto> {
    return await this.konnectService.create(createKonnectDto);
  }

  @Get()
  findAll() {
    return this.konnectService.findAll();
  }

  @Get(":id")
  findOne(@Param("id") id: string) {
    return this.konnectService.findOne(+id);
  }

  @Patch(":id")
  update(@Param("id") id: string, @Body() updateKonnectDto: UpdateKonnectDto) {
    return this.konnectService.update(+id, updateKonnectDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.konnectService.remove(+id);
  }
}
