'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import paymentService from '@/services/paymentService';

interface Plan {
  id: string;
  name: string;
  price: number;
  duration: string;
  features: string[];
}

interface Tool {
  id: string;
  name: string;
  description: string;
  image: string;
  plans: Plan[];
}

interface CheckoutFormProps {
  tool: Tool;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ tool }) => {
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Set the first plan as default selected plan
    if (tool && tool.plans && tool.plans.length > 0) {
      setSelectedPlan(tool.plans[0]);
    }
  }, [tool]);

  const handlePlanSelect = (plan: Plan) => {
    setSelectedPlan(plan);
  };

  const handleCheckout = async () => {
    if (!isAuthenticated) {
      router.push('/signin?redirect=/checkout');
      return;
    }

    if (!selectedPlan) {
      setError('Please select a plan to continue');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await paymentService.createPayment({
        toolId: tool.id,
        toolName: tool.name,
        planId: selectedPlan.id,
        planName: selectedPlan.name,
        amount: selectedPlan.price,
      });

      // Redirect to the payment URL
      if (response.success && response.data?.paymentUrl) {
        window.location.href = response.data.paymentUrl;
      } else {
        setError('Failed to initiate payment. Please try again.');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to process payment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!tool || !selectedPlan) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6 sm:p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 terminal-text">
          Checkout
        </h2>

        {/* Tool Information */}
        <div className="flex flex-col sm:flex-row items-start gap-6 mb-8">
          <div className="w-full sm:w-1/3">
            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
              {tool.image ? (
                <img
                  src={tool.image}
                  alt={tool.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="text-4xl">🧰</div>
              )}
            </div>
          </div>
          <div className="w-full sm:w-2/3">
            <h3 className="text-xl font-bold text-gray-900 mb-2">{tool.name}</h3>
            <p className="text-gray-600 mb-4">{tool.description}</p>
          </div>
        </div>

        {/* Plan Selection */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 terminal-text">
            Select a Plan
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tool.plans.map((plan) => (
              <motion.div
                key={plan.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedPlan?.id === plan.id
                    ? 'border-yellow-400 bg-yellow-50'
                    : 'border-gray-200 hover:border-yellow-300'
                }`}
                onClick={() => handlePlanSelect(plan)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-gray-900">{plan.name}</h4>
                  {selectedPlan?.id === plan.id && (
                    <span className="text-yellow-500 text-sm font-medium">
                      Selected ✓
                    </span>
                  )}
                </div>
                <div className="text-xl font-bold text-gray-900 mb-2">
                  {plan.price} TND
                </div>
                <div className="text-sm text-gray-500 mb-3">{plan.duration}</div>
                <ul className="text-sm text-gray-600 space-y-1">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Price Breakdown */}
        <div className="bg-gray-50 rounded-lg p-4 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 terminal-text">
            Order Summary
          </h3>
          <div className="space-y-2 mb-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Plan</span>
              <span className="font-medium">{selectedPlan.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Duration</span>
              <span className="font-medium">{selectedPlan.duration}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal</span>
              <span className="font-medium">{selectedPlan.price} TND</span>
            </div>
          </div>
          <div className="border-t border-gray-200 pt-2">
            <div className="flex justify-between font-bold text-lg">
              <span>Total</span>
              <span>{selectedPlan.price} TND</span>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* Checkout Button */}
        <button
          onClick={handleCheckout}
          disabled={isLoading}
          className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center"
        >
          {isLoading ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-900"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </>
          ) : (
            'Proceed to Payment'
          )}
        </button>

        <div className="mt-4 text-sm text-gray-500 text-center">
          By proceeding, you agree to our Terms of Service and Privacy Policy.
        </div>
      </div>
    </div>
  );
};

export default CheckoutForm;
