import { NextRequest, NextResponse } from 'next/server';

// This is a shared reference to the tools array
let tools = [];

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Update the tools array with the data from localStorage
    if (data.tools && Array.isArray(data.tools)) {
      tools = data.tools;
      console.log(`Received ${tools.length} tools from client`);
    }
    
    return NextResponse.json({ 
      message: 'Tools synced successfully',
      count: tools.length
    });
  } catch (error) {
    console.error('Error syncing tools:', error);
    return NextResponse.json({ 
      message: 'Failed to sync tools' 
    }, { status: 500 });
  }
}

// Export the tools array so it can be imported by other routes
export { tools };
