import { <PERSON>, Get, Param, UseGuards, Req } from '@nestjs/common';
import { OrderHistoryService } from './order-history.service';
import { OrderHistoryResponseDto } from './dto/order-history-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('order-history')
@Controller('order-history')
export class OrderHistoryController {
  constructor(private readonly orderHistoryService: OrderHistoryService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user order history' })
  @ApiResponse({ status: 200, description: 'Order history retrieved successfully', type: OrderHistoryResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getOrderHistory(@Req() req): Promise<OrderHistoryResponseDto> {
    return this.orderHistoryService.getOrderHistory(req.user.id);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiResponse({ status: 200, description: 'Order retrieved successfully', type: OrderHistoryResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Order not found' })
  async getOrderById(
    @Req() req,
    @Param('id') orderId: string,
  ): Promise<OrderHistoryResponseDto> {
    return this.orderHistoryService.getOrderById(req.user.id, orderId);
  }
}
