"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { ArrowRight, CreditCard, Globe, Lock } from "lucide-react";
import CurvedShape from "../ui/CurvedShape";

const ModernHeroSection = () => {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Blob decorations */}
      <div className="blob-decoration blob-yellow"></div>
      <div className="blob-decoration blob-pink"></div>

      {/* Bottom curved shape */}
      <CurvedShape position="bottom" color="#FFAD00" />

      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left column - Text content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-left"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
              className="inline-flex items-center px-3 py-1 mb-6 rounded-full bg-[rgba(255,173,0,0.1)] border border-[rgba(255,173,0,0.2)]"
            >
              <span className="text-sm font-medium text-[#FFAD00]">
                Made for Tunisia 🇹🇳
              </span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-[#111111]"
            >
              Access global tools with{" "}
              <span className="highlight-bg">local payments</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="text-xl mb-8 text-[#333333] max-w-xl"
            >
              Partagily helps Tunisians bypass international payment
              restrictions. Subscribe with local payment methods and unlock
              premium global tools.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Link
                href="/signup"
                className="btn btn-primary btn-lg btn-icon hover-glow"
              >
                Try it now <ArrowRight size={20} />
              </Link>

              <Link href="#how-it-works" className="btn btn-outline btn-lg">
                How it works
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.7, duration: 0.6 }}
              className="mt-12 flex items-center gap-6"
            >
              <div className="flex items-center gap-2">
                <div className="icon-badge icon-badge-yellow">
                  <CreditCard size={18} />
                </div>
                <span className="text-sm text-[#333333]">Local payments</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="icon-badge icon-badge-blue">
                  <Globe size={18} />
                </div>
                <span className="text-sm text-[#333333]">Global tools</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="icon-badge icon-badge-green">
                  <Lock size={18} />
                </div>
                <span className="text-sm text-[#333333]">Secure access</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right column - Visual */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.7 }}
            className="relative"
          >
            <div className="relative z-10">
              <div className="glass-card p-6 rounded-2xl overflow-hidden">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-400"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                    <div className="w-3 h-3 rounded-full bg-green-400"></div>
                  </div>
                  <div className="text-xs text-gray-400 font-mono">
                    partagily.com
                  </div>
                </div>

                <div className="relative">
                  {/* Animated visual showing Tunisian card unlocking global tools */}
                  <div className="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg flex items-center justify-center p-8 overflow-hidden">
                    <div className="relative flex flex-col items-center">
                      {/* <motion.div
                        initial={{ y: 0 }}
                        animate={{ y: [0, -10, 0] }}
                        transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                        className="w-48 h-28 bg-gradient-to-r from-[#FFAD00] to-[#e94a9c] rounded-xl shadow-lg flex items-center justify-center mb-6 relative"
                      >
                        <div className="absolute inset-1 bg-gray-900 rounded-lg flex items-center justify-center">
                          <span className="text-[#FFAD00] font-bold">Tunisian Card</span>
                        </div>
                      </motion.div> */}
                      <h1>Welcome To Partagily</h1>

                      {/* <motion.div
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: [0.8, 1.1, 1], opacity: [0, 1, 1] }}
                        transition={{ repeat: Infinity, duration: 3, ease: "easeInOut" }}
                        className="absolute top-20"
                      >
                        <div className="w-8 h-8 rounded-full bg-[#FFAD00] flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="text-gray-900">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                          </svg>
                        </div>
                      </motion.div> */}

                      {/* <motion.div
                        initial={{ y: 50, opacity: 0 }}
                        animate={{ y: [50, 0], opacity: [0, 1] }}
                        transition={{ repeat: Infinity, duration: 3, delay: 1, ease: "easeOut" }}
                        className="flex gap-3"
                      >
                        <div className="w-12 h-12 rounded-lg bg-blue-500 flex items-center justify-center text-xl">A</div>
                        <div className="w-12 h-12 rounded-lg bg-green-500 flex items-center justify-center text-xl">B</div>
                        <div className="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center text-xl">C</div>
                      </motion.div> */}
                    </div>
                  </div>
                </div>

                <div className="mt-4 font-mono text-sm text-green-400">
                  <div className="flex items-center">
                    <span className="text-[#FFAD00] mr-2">$</span>
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{
                        repeat: Infinity,
                        duration: 0.8,
                        repeatType: "reverse",
                      }}
                    >
                      Access premium tools with local payment methods...
                    </motion.span>
                    <motion.span
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ repeat: Infinity, duration: 1 }}
                      className="ml-1"
                    >
                      _
                    </motion.span>
                  </div>
                </div>
              </div>
            </div>

            {/* Background decorative elements */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-[#FFAD00] opacity-10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-[#e94a9c] opacity-10 rounded-full blur-3xl"></div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ModernHeroSection;
