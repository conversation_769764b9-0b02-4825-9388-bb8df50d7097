// This file is used to access localStorage from the server component
// It's injected as a script tag in the response

export const clientScript = `
<script>
  // Function to get tools from localStorage
  function getToolsFromLocalStorage() {
    try {
      const storedTools = localStorage.getItem('partagily-tools');
      return storedTools ? JSON.parse(storedTools) : [];
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return [];
    }
  }

  // Send the tools to the server via fetch
  async function sendToolsToServer() {
    const tools = getToolsFromLocalStorage();
    
    try {
      await fetch('/api/admin/tools/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tools }),
      });
      console.log('Tools sent to server');
    } catch (error) {
      console.error('Error sending tools to server:', error);
    }
  }

  // Call the function when the page loads
  sendToolsToServer();
</script>
`;
