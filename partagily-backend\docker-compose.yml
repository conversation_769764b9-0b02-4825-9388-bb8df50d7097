# Use postgres/example user/password credentials

services:
  db:
    image: postgres
    restart: always
    ports:
      - "5432:5432"
    # set shared memory limit when using docker compose
    shm_size: 128mb
    # or set shared memory limit when deploy via swarm stack
    #volumes:
    #  - type: tmpfs
    #    target: /dev/shm
    #    tmpfs:
    #      size: 134217728 # 128*2^20 bytes = 128Mb
    environment:
      POSTGRES_USER: seifa
      POSTGRES_PASSWORD: 123456

  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
