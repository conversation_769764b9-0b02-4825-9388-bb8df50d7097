/*
  Warnings:

  - The values [Streaming,Stock,Publishing,Design,Video,AI,Music,Ecommerce,Writing,Networking] on the enum `ToolCategory` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `icon` on the `cart_items` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ToolCategory_new" AS ENUM ('streaming', 'stock', 'publishing', 'design', 'video', 'ai', 'music', 'ecommerce', 'writing', 'networking');
ALTER TABLE "tools" ALTER COLUMN "category" TYPE "ToolCategory_new" USING ("category"::text::"ToolCategory_new");
ALTER TYPE "ToolCategory" RENAME TO "ToolCategory_old";
ALTER TYPE "ToolCategory_new" RENAME TO "ToolCategory";
DROP TYPE "ToolCategory_old";
COMMIT;

-- AlterTable
ALTER TABLE "cart_items" DROP COLUMN "icon";

-- AlterTable
ALTER TABLE "order_items" ADD COLUMN     "icon" TEXT;
