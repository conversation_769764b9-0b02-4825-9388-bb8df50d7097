'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import CheckoutForm from '@/components/checkout/CheckoutForm';
import '@/styles/checkout.css';

// Mock data for tools - in a real app, this would come from an API
const mockTools = [
  {
    id: 'netflix',
    name: 'Netflix Premium',
    description: 'Access to Netflix Premium with 4K streaming and multiple profiles.',
    image: '/images/tools/netflix.png',
    plans: [
      {
        id: 'netflix-monthly',
        name: 'Monthly',
        price: 29.99,
        duration: '1 Month',
        features: [
          'Full HD & 4K streaming',
          '4 simultaneous screens',
          'Ad-free experience',
          'Download for offline viewing',
        ],
      },
      {
        id: 'netflix-quarterly',
        name: 'Quarterly',
        price: 79.99,
        duration: '3 Months',
        features: [
          'Full HD & 4K streaming',
          '4 simultaneous screens',
          'Ad-free experience',
          'Download for offline viewing',
          '10% discount',
        ],
      },
      {
        id: 'netflix-yearly',
        name: 'Yearly',
        price: 299.99,
        duration: '12 Months',
        features: [
          'Full HD & 4K streaming',
          '4 simultaneous screens',
          'Ad-free experience',
          'Download for offline viewing',
          '20% discount',
          'Priority support',
        ],
      },
    ],
  },
  {
    id: 'spotify',
    name: 'Spotify Premium',
    description: 'Ad-free music streaming with offline downloads and high-quality audio.',
    image: '/images/tools/spotify.png',
    plans: [
      {
        id: 'spotify-monthly',
        name: 'Monthly',
        price: 19.99,
        duration: '1 Month',
        features: [
          'Ad-free music',
          'Offline downloads',
          'High-quality audio',
          'Unlimited skips',
        ],
      },
      {
        id: 'spotify-quarterly',
        name: 'Quarterly',
        price: 54.99,
        duration: '3 Months',
        features: [
          'Ad-free music',
          'Offline downloads',
          'High-quality audio',
          'Unlimited skips',
          '10% discount',
        ],
      },
      {
        id: 'spotify-yearly',
        name: 'Yearly',
        price: 199.99,
        duration: '12 Months',
        features: [
          'Ad-free music',
          'Offline downloads',
          'High-quality audio',
          'Unlimited skips',
          '15% discount',
          'Priority support',
        ],
      },
    ],
  },
  {
    id: 'chatgpt',
    name: 'ChatGPT Plus',
    description: 'Premium access to ChatGPT with GPT-4, faster response times, and priority access.',
    image: '/images/tools/chatgpt.png',
    plans: [
      {
        id: 'chatgpt-monthly',
        name: 'Monthly',
        price: 39.99,
        duration: '1 Month',
        features: [
          'Access to GPT-4',
          'Faster response times',
          'Priority access during peak times',
          'Early access to new features',
        ],
      },
      {
        id: 'chatgpt-quarterly',
        name: 'Quarterly',
        price: 109.99,
        duration: '3 Months',
        features: [
          'Access to GPT-4',
          'Faster response times',
          'Priority access during peak times',
          'Early access to new features',
          '10% discount',
        ],
      },
      {
        id: 'chatgpt-yearly',
        name: 'Yearly',
        price: 399.99,
        duration: '12 Months',
        features: [
          'Access to GPT-4',
          'Faster response times',
          'Priority access during peak times',
          'Early access to new features',
          '15% discount',
          'Exclusive plugins',
        ],
      },
    ],
  },
];

export default function CheckoutPage() {
  const { toolId } = useParams();
  const [tool, setTool] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // In a real app, this would be an API call
    const fetchTool = () => {
      setLoading(true);
      try {
        const foundTool = mockTools.find((t) => t.id === toolId);
        if (foundTool) {
          setTool(foundTool);
        } else {
          setError('Tool not found');
        }
      } catch (err) {
        setError('Failed to load tool information');
      } finally {
        setLoading(false);
      }
    };

    if (toolId) {
      fetchTool();
    }
  }, [toolId]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-24 flex flex-col justify-center items-center min-h-[80vh] checkout-container">
        <motion.div
          animate={{
            rotate: 360,
          }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full mb-6"
        />
        <p className="text-gray-600 dark:text-gray-400 text-lg">Loading tool details...</p>
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
        <div className="checkout-header pt-8 mb-16">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white text-center mb-4">
            Tool Checkout
          </h1>
        </div>
        <div className="max-w-2xl mx-auto bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden checkout-content p-12">
          <div className="flex justify-center mb-8">
            <div className="w-24 h-24 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-red-500 dark:text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </div>
          </div>
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-6">
            Error Loading Tool
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-lg text-center mb-10">
            {error || 'Tool not found. Please try again or contact support.'}
          </p>
          <div className="flex justify-center">
            <a
              href="/dashboard"
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-4 px-8 rounded-lg flex items-center justify-center transition-colors duration-200 text-lg shadow-md hover:shadow-lg"
            >
              Return to Dashboard
            </a>
          </div>
        </div>
        <div className="checkout-footer"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-24 min-h-[80vh] checkout-container">
      <div className="checkout-header pt-8 mb-16">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white text-center mb-4">
          {tool.name} Checkout
        </h1>
        <p className="text-gray-600 dark:text-gray-400 text-lg text-center">
          Select a plan and complete your purchase
        </p>
      </div>
      <div className="max-w-4xl mx-auto checkout-content">
        <CheckoutForm tool={tool} />
      </div>
      <div className="checkout-footer"></div>
    </div>
  );
}
