import Image from 'next/image';
import Link from 'next/link';

const ToolsSection = () => {
  const tools = [
    {
      name: "Adobe Creative Cloud",
      description: "Access Photoshop, Illustrator, and more with a shared account.",
      status: "Available",
      icon: "/placeholder-tool.png"
    },
    {
      name: "Microsoft Office 365",
      description: "Use Word, Excel, PowerPoint and other Office applications.",
      status: "Available",
      icon: "/placeholder-tool.png"
    },
    {
      name: "Canva Pro",
      description: "Create professional designs with premium Canva features.",
      status: "Available",
      icon: "/placeholder-tool.png"
    },
    {
      name: "Grammarly Premium",
      description: "Advanced grammar and writing suggestions for your content.",
      status: "Available",
      icon: "/placeholder-tool.png"
    },
    {
      name: "ChatGPT Plus",
      description: "Priority access to OpenAI's advanced AI assistant.",
      status: "Available",
      icon: "/placeholder-tool.png"
    },
    {
      name: "Notion Premium",
      description: "Organize your work and life with advanced Notion features.",
      status: "Available",
      icon: "/placeholder-tool.png"
    }
  ];

  return (
    <section className="py-16" id="tools">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12">Available Tools</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {tools.map((tool, index) => (
            <div 
              key={index} 
              className="bg-white p-6 rounded-lg shadow-md border border-gray-200"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center mr-4">
                  {/* Replace with actual tool icons */}
                  <div className="text-2xl">{tool.icon ? "🔧" : "🔧"}</div>
                </div>
                <div>
                  <h3 className="text-xl font-semibold">{tool.name}</h3>
                  <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">
                    {tool.status}
                  </span>
                </div>
              </div>
              <p className="text-gray-600 mb-4">{tool.description}</p>
              <Link 
                href="/pricing" 
                className="text-[#1e88e5] font-medium hover:underline"
              >
                Get Access →
              </Link>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-10">
          <Link 
            href="/tools" 
            className="bg-[#1e88e5] text-white px-6 py-3 rounded-md font-medium hover:bg-blue-600 transition-colors inline-block"
          >
            View All Tools
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ToolsSection;
