'use client';

import { useState, useEffect } from 'react';
import { useNotification } from '@/contexts/NotificationContext';
import userStorageService from '@/services/userStorageService';

export default function DebugPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [role, setRole] = useState('user');
  const [registeredUsers, setRegisteredUsers] = useState<any[]>([]);
  const [userData, setUserData] = useState<any>(null);
  const { showNotification } = useNotification();

  // Function to load data
  const loadData = () => {
    // Load registered users
    const users = userStorageService.getRegisteredUsers();
    setRegisteredUsers(users);

    // Load user data
    const user = userStorageService.getCurrentUser();
    setUserData(user);
  };

  useEffect(() => {
    loadData();
  }, []);

  const handleResetPassword = () => {
    if (!email || !password) {
      showNotification('error', 'Please enter both email and password');
      return;
    }

    const success = userStorageService.updateUserPassword(email, password);
    if (success) {
      showNotification('success', `Password for ${email} reset to ${password}`);
      loadData(); // Reload data
    } else {
      showNotification('error', `Failed to reset password for ${email}`);
    }
  };

  const handleClearUserData = () => {
    userStorageService.clearAllUserData();
    showNotification('success', 'All user data cleared');
    setUserData(null);
    setRegisteredUsers([]);
  };

  const handleTestLogin = async () => {
    if (!email || !password) {
      showNotification('error', 'Please enter both email and password');
      return;
    }

    try {
      const user = userStorageService.verifyUserCredentials(email, password);
      if (user) {
        showNotification('success', `Login successful for ${email}`);
        console.log('Verified user:', user);
      } else {
        showNotification('error', `Invalid credentials for ${email}`);
      }
    } catch (error) {
      showNotification('error', `Error testing login: ${error}`);
    }
  };

  const handleCreateUser = () => {
    if (!email || !password || !name) {
      showNotification('error', 'Please enter email, password, and name');
      return;
    }

    try {
      const newUser = {
        id: Date.now().toString(),
        email,
        name,
        password,
        role
      };

      const success = userStorageService.addUser(newUser);

      if (success) {
        showNotification('success', `User ${email} created successfully`);
        setEmail('');
        setPassword('');
        setName('');
        loadData(); // Reload data
      } else {
        showNotification('error', `Failed to create user ${email}`);
      }
    } catch (error) {
      showNotification('error', `Error creating user: ${error}`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Debug Page</h1>

      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8 p-6">
        <h2 className="text-xl font-bold mb-4">User Management</h2>
        <div className="space-y-4 mb-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="text"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Name
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
          </div>
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
              Role
            </label>
            <select
              id="role"
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            >
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>
          </div>
        </div>
        <div className="flex flex-wrap gap-4 mb-4">
          <button
            onClick={handleCreateUser}
            className="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Create User
          </button>

          <button
            onClick={handleResetPassword}
            className="bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Reset Password
          </button>

          <button
            onClick={handleTestLogin}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Test Login
          </button>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8 p-6">
        <h2 className="text-xl font-bold mb-4">Clear User Data</h2>
        <p className="text-gray-600 mb-4">
          This will clear all user data from localStorage and sessionStorage.
        </p>
        <button
          onClick={handleClearUserData}
          className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg transition-colors duration-200"
        >
          Clear All User Data
        </button>
      </div>

      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8 p-6">
        <h2 className="text-xl font-bold mb-4">Current User</h2>
        {userData ? (
          <pre className="bg-gray-100 p-4 rounded-lg overflow-auto max-h-60">
            {JSON.stringify(userData, null, 2)}
          </pre>
        ) : (
          <p className="text-gray-600">No user data found</p>
        )}
      </div>

      <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8 p-6">
        <h2 className="text-xl font-bold mb-4">Registered Users</h2>
        {registeredUsers.length > 0 ? (
          <div className="overflow-auto max-h-96">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Password
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {registeredUsers.map((user, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.password}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.role || 'user'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-600">No registered users found</p>
        )}
      </div>
    </div>
  );
}
