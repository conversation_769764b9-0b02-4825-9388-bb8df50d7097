'use client';

import { motion } from 'framer-motion';
import { Shield, Users, CheckCircle } from 'lucide-react';

const TrustBanner = () => {
  return (
    <section className="py-16 bg-gradient-to-r from-[rgba(233,74,156,0.05)] to-[rgba(255,173,0,0.05)]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <div className="glass-card p-8 max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row items-center justify-center gap-8">
              {/* Trust Stats */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-[rgba(233,74,156,0.1)] flex items-center justify-center">
                  <Users size={24} className="text-[#e94a9c]" />
                </div>
                <div className="text-left">
                  <div className="text-2xl font-bold text-[#e94a9c]">10,000+</div>
                  <div className="text-sm text-gray-300">utilisateurs en Tunisie</div>
                </div>
              </div>

              {/* Security Badge */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-[rgba(255,173,0,0.1)] flex items-center justify-center">
                  <Shield size={24} className="text-[#FFAD00]" />
                </div>
                <div className="text-left">
                  <div className="text-lg font-bold">Paiement 100% sécurisé</div>
                  <div className="text-sm text-gray-300">via Konnect</div>
                </div>
              </div>

              {/* Verification Badge */}
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-full bg-[rgba(48,209,88,0.1)] flex items-center justify-center">
                  <CheckCircle size={24} className="text-[#30d158]" />
                </div>
                <div className="text-left">
                  <div className="text-lg font-bold">Plateforme vérifiée</div>
                  <div className="text-sm text-gray-300">depuis 2023</div>
                </div>
              </div>
            </div>

            {/* Main Trust Message */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700"
            >
              <p className="text-xl font-semibold text-center">
                <span className="text-[#e94a9c]">Plus de 10,000 utilisateurs</span> en Tunisie font confiance à Partagily.
                <br />
                <span className="text-[#FFAD00]">Paiement 100% sécurisé</span> via Konnect.
              </p>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TrustBanner;
