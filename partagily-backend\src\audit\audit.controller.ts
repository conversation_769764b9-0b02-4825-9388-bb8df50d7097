import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Get, 
  Query, 
  Param, 
  Ip,
  Request,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBearerAuth,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuditService, AuditEventType } from '../common/services/audit.service';
import { CreateCookieEventDto } from './dto/create-cookie-event.dto';
import { AuditQueryDto } from './dto/audit-query.dto';

@ApiTags('audit')
@Controller('audit')
export class AuditController {
  constructor(private readonly auditService: AuditService) {}

  @Post('cookie-events')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Log a cookie event' })
  @ApiResponse({ status: 201, description: 'Event logged successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createCookieEvent(
    @Body() createEventDto: CreateCookieEventDto,
    @Request() req,
    @Ip() ip: string,
  ) {
    // Determine the event type
    let eventType: AuditEventType;
    
    switch (createEventDto.eventType) {
      case 'COOKIE_INJECTION':
        eventType = AuditEventType.COOKIE_INJECTION;
        break;
      case 'COOKIE_ROTATION':
        eventType = AuditEventType.COOKIE_ROTATION;
        break;
      case 'COOKIE_CLEAR':
        eventType = AuditEventType.COOKIE_CLEAR;
        break;
      default:
        eventType = AuditEventType.COOKIE_INJECTION;
    }
    
    // Log the cookie event
    await this.auditService.logCookieEvent(
      eventType,
      createEventDto.userId || req.user.id,
      createEventDto.details || {}
    );
    
    return { success: true, message: 'Event logged successfully' };
  }
  
  @Get('logs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get audit logs with filtering' })
  @ApiResponse({ status: 200, description: 'Return filtered audit logs' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getAuditLogs(@Query() queryDto: AuditQueryDto, @Request() req) {
    // Only admins can see all logs, regular users can only see their own
    const isAdmin = req.user.role === 'ADMIN' || req.user.role === 'SUPER_ADMIN';
    const userId = isAdmin && queryDto.userId ? queryDto.userId : req.user.id;
    
    // Get logs from the database
    const logs = await this.auditService.getAuditLogs({
      ...queryDto,
      userId: isAdmin ? queryDto.userId : userId,
    });
    
    return logs;
  }
  
  @Get('logs/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get audit log by ID' })
  @ApiResponse({ status: 200, description: 'Return audit log details' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Log not found' })
  async getAuditLogById(@Param('id') id: string, @Request() req) {
    // Only admins can see all logs, regular users can only see their own
    const isAdmin = req.user.role === 'ADMIN' || req.user.role === 'SUPER_ADMIN';
    
    // Get log from the database
    const log = await this.auditService.getAuditLogById(id);
    
    // Check if user has permission to view this log
    if (!isAdmin && log.userId !== req.user.id.toString()) {
      return { error: 'You do not have permission to view this log' };
    }
    
    return log;
  }
}
