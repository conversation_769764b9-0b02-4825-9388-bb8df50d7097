'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
  isDefault?: boolean;
}

const PaymentMethods: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: 'edinar',
      name: 'E-Dinar',
      icon: '💳',
      description: 'Pay directly with your e-Dinar card',
      isDefault: true,
    },
    {
      id: 'postal',
      name: 'Postal Payment',
      icon: '📬',
      description: 'Use Tunisian postal payment services',
    },
    {
      id: 'bank_transfer',
      name: 'Local Bank Transfer',
      icon: '🏦',
      description: 'Transfer directly from your Tunisian bank',
    },
    {
      id: 'd17',
      name: 'D17',
      icon: '📱',
      description: 'Pay using the D17 mobile payment app',
    },
  ]);

  const [showAddForm, setShowAddForm] = useState(false);

  const handleSetDefault = (id: string) => {
    setPaymentMethods(
      paymentMethods.map((method) => ({
        ...method,
        isDefault: method.id === id,
      }))
    );
  };

  const handleRemoveMethod = (id: string) => {
    setPaymentMethods(paymentMethods.filter((method) => method.id !== id));
  };

  return (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold font-mono">Your Payment Methods</h2>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center"
          >
            {showAddForm ? 'Cancel' : '+ Add Method'}
          </button>
        </div>

        {showAddForm && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="mb-6 p-4 border border-gray-200 rounded-lg"
          >
            <h3 className="text-lg font-semibold mb-4">Add Payment Method</h3>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Type
                </label>
                <select className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500">
                  <option value="">Select payment type</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_account">Bank Account</option>
                  <option value="mobile_payment">Mobile Payment</option>
                </select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name on Account
                  </label>
                  <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Account Number
                  </label>
                  <input
                    type="text"
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                    placeholder="Account number"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  className="bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200"
                >
                  Save Payment Method
                </button>
              </div>
            </form>
          </motion.div>
        )}

        <div className="space-y-4">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              className="border border-gray-200 rounded-lg p-4 flex flex-col md:flex-row md:items-center md:justify-between"
            >
              <div className="flex items-center mb-4 md:mb-0">
                <div className="bg-gray-100 p-3 rounded-full mr-4 text-2xl">
                  {method.icon}
                </div>
                <div>
                  <div className="flex items-center">
                    <h3 className="font-semibold text-gray-900">{method.name}</h3>
                    {method.isDefault && (
                      <span className="ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded">
                        Default
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 text-sm">{method.description}</p>
                </div>
              </div>
              <div className="flex space-x-2">
                {!method.isDefault && (
                  <button
                    onClick={() => handleSetDefault(method.id)}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Set as Default
                  </button>
                )}
                <button
                  onClick={() => handleRemoveMethod(method.id)}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <h3 className="text-lg font-semibold mb-4">About Payment Methods</h3>
        <div className="space-y-4 text-sm text-gray-600">
          <p>
            Partagily supports various local Tunisian payment methods to make it easy for you to access international services.
          </p>
          <p>
            Your payment information is securely stored and processed in compliance with industry standards.
          </p>
          <p>
            For assistance with payments, please contact our support team at <span className="text-blue-600"><EMAIL></span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethods;
