import { Controller, Get, Query, Logger, HttpStatus, HttpCode } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';

@ApiTags('payment')
@Controller('payment/webhook')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly paymentService: PaymentService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Konnect payment webhook' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiQuery({ name: 'payment_ref', required: true, type: String })
  async handleWebhook(@Query('payment_ref') paymentRef: string): Promise<{ success: boolean; message: string; data?: any }> {
    this.logger.log(`Received webhook for payment reference: ${paymentRef}`);

    if (!paymentRef) {
      this.logger.warn('Webhook received without payment_ref');
      return { success: false, message: 'Missing payment reference' };
    }

    try {
      const result = await this.paymentService.verifyPayment(paymentRef);

      // Always return 200 to Konnect to acknowledge receipt, even if verification fails
      return result;
    } catch (error) {
      this.logger.error(`Error processing webhook: ${error.message}`, error.stack);
      // Always return 200 to Konnect to acknowledge receipt
      return { success: false, message: 'Error processing webhook' };
    }
  }
}
