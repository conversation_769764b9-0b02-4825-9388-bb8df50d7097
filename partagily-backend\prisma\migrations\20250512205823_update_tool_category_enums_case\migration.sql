/*
  Warnings:

  - The values [streaming,stock,publishing,design,video,ai,music,ecommerce,writing,networking] on the enum `ToolCategory` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ToolCategory_new" AS ENUM ('Streaming', 'Stock', 'Publishing', 'Design', 'Video', 'AI', 'Music', 'Ecommerce', 'Writing', 'Networking');
ALTER TABLE "tools" ALTER COLUMN "category" TYPE "ToolCategory_new" USING ("category"::text::"ToolCategory_new");
ALTER TYPE "ToolCategory" RENAME TO "ToolCategory_old";
ALTER TYPE "ToolCategory_new" RENAME TO "ToolCategory";
DROP TYPE "ToolCategory_old";
COMMIT;
