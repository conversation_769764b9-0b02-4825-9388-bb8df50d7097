-- Drop the existing enum type
DROP TYPE IF EXISTS "ToolCategory" CASCADE;

-- Create the new enum type with capitalized values
CREATE TYPE "ToolCategory" AS ENUM (
  'Streaming',
  'Stock',
  'Publishing',
  'Design',
  'Video',
  'AI',
  'Music',
  'Ecommerce',
  'Writing',
  'Networking'
);

-- Update any existing tools with lowercase categories to use the new capitalized values
UPDATE "tools" SET category = 'Streaming' WHERE category = 'streaming';
UPDATE "tools" SET category = 'Design' WHERE category = 'design';
UPDATE "tools" SET category = 'AI' WHERE category = 'ai';
UPDATE "tools" SET category = 'Writing' WHERE category = 'writing';
UPDATE "tools" SET category = 'Stock' WHERE category = 'stock';
UPDATE "tools" SET category = 'Publishing' WHERE category = 'publishing';
UPDATE "tools" SET category = 'Video' WHERE category = 'video';
UPDATE "tools" SET category = 'Music' WHERE category = 'music';
UPDATE "tools" SET category = 'Ecommerce' WHERE category = 'ecommerce';
UPDATE "tools" SET category = 'Networking' WHERE category = 'networking';
