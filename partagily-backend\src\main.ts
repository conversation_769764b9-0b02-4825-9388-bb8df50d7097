import { NestFactory } from "@nestjs/core";
import { ValidationPipe } from "@nestjs/common";
import { AppModule } from "./app.module";
import { corsConfig } from "./common/config/cors.config";
import * as cookieParser from "cookie-parser";
import * as session from "express-session";
import helmet from "helmet";

async function bootstrap() {
  try {
    console.log("Starting Partagily backend server...");
    const app = await NestFactory.create(AppModule);

    console.log("NestJS application created successfully");

    // Enable CORS with enhanced configuration
    app.enableCors(corsConfig(process.env));

    // Use Helmet for security headers
    if (process.env.NODE_ENV === "production") {
      // Full security in production
      app.use(
        helmet({
          contentSecurityPolicy: true,
          crossOriginEmbedderPolicy: true,
          crossOriginOpenerPolicy: true,
          crossOriginResourcePolicy: true,
          dnsPrefetchControl: true,
          // expectCt has been deprecated in newer versions of helmet
          frameguard: true,
          hidePoweredBy: true,
          hsts: true,
          ieNoOpen: true,
          noSniff: true,
          permittedCrossDomainPolicies: true,
          referrerPolicy: true,
          xssFilter: true,
        })
      );
    } else {
      // Reduced security in development for easier testing
      app.use(
        helmet({
          contentSecurityPolicy: false,
          crossOriginEmbedderPolicy: false,
          crossOriginOpenerPolicy: false,
          crossOriginResourcePolicy: false,
          dnsPrefetchControl: true,
          // expectCt has been deprecated in newer versions of helmet
          frameguard: false,
          hidePoweredBy: true,
          hsts: false,
          ieNoOpen: true,
          noSniff: true,
          permittedCrossDomainPolicies: false,
          referrerPolicy: false,
          xssFilter: true,
        })
      );
    }

    // Parse cookies
    app.use(cookieParser(process.env.COOKIE_SECRET || "partagily-secret"));

    // Use sessions
    app.use(
      session({
        secret: process.env.SESSION_SECRET || "partagily-session-secret",
        resave: false,
        saveUninitialized: false,
        cookie: {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          maxAge: 24 * 60 * 60 * 1000, // 1 day
        },
      })
    );

    // Enable validation
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
      })
    );

    // Setup Swagger - disable for now to avoid circular dependency issues
    // We'll re-enable it once the circular dependencies are fixed
    console.log(
      "Skipping Swagger documentation to avoid circular dependency issues"
    );

    // Get port from environment or use default
    const port = process.env.PORT; // Using 3005 as specified for the backend

    await app.listen(port, "0.0.0.0");
    console.log(`Application is running on: ${process.env.API_URL}`);
    console.log(
      "Database connection has been established. User registration will save to the database."
    );
  } catch (error) {
    console.error("Failed to start the application:", error);
    console.error("Please check your database connection and try again");
    process.exit(1);
  }
}

// Start the application
bootstrap().catch((err) => {
  console.error("Unhandled error during bootstrap:", err);
  process.exit(1);
});
