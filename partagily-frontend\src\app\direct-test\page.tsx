'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import './styles.css';

export default function DirectTestPage() {
  return (
    <div className="min-h-screen pt-20 flex flex-col items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full bg-white rounded-xl shadow-xl overflow-hidden mx-4"
      >
        <div className="px-6 py-8">
          <h1 className="text-3xl font-bold mb-6 terminal-text">
            Direct <span style={{ color: '#facc15' }}>Test</span> 🧪
          </h1>
          
          <p className="mb-6 text-gray-700">
            This is a test page with directly imported CSS to check if our styles are being applied correctly.
          </p>
          
          <div className="space-y-4">
            <button className="btn btn-primary w-full">Primary Button</button>
            <button className="btn btn-secondary w-full">Secondary Button</button>
            <button className="btn btn-outline w-full">Outline Button</button>
          </div>
          
          <div className="mt-8 flex justify-center">
            <Link href="/" className="text-yellow-500 hover:text-yellow-600 hover-bounce">
              Back to Home
            </Link>
          </div>
        </div>
        
        <div className="gradient-border h-2"></div>
      </motion.div>
    </div>
  );
}
