'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { motion, AnimatePresence } from 'framer-motion';
import '@/styles/dashboard.css';
import '@/styles/fix-layout-shift.css';
import {
  Grid,
  CreditCard,
  Briefcase,
  ShoppingBag,
  Users,
  Settings,
  LogOut,
  HelpCircle,
  Menu,
  X,
  ChevronRight
} from 'lucide-react';

// Dashboard sidebar navigation items
const navItems = [
  { name: 'Marketplace', href: '/dashboard', icon: <Grid size={20} /> },
  { name: 'Buy Plan', href: '/dashboard/plans', icon: <CreditCard size={20} /> },
  { name: 'My Tools', href: '/dashboard/subscriptions', icon: <Briefcase size={20} /> },
  { name: 'Order History', href: '/dashboard/orders', icon: <ShoppingBag size={20} /> },
  { name: 'Affiliate Program', href: '/dashboard/affiliate', icon: <Users size={20} /> },
  { name: 'Profile Settings', href: '/dashboard/profile', icon: <Settings size={20} /> },
];

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const { theme } = useTheme();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // This effect is now redundant with our middleware, but we'll keep it as a fallback
  // The middleware will prevent this component from rendering if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      console.log('Dashboard layout: User not authenticated, redirecting to signin');
      router.replace('/signin');
    }
  }, [isAuthenticated, isLoading, router]);

  // Remove any existing Navbar and Footer elements that might be rendered from the parent layout
  useEffect(() => {
    const navbar = document.querySelector('nav:not(.flex-1)') as HTMLElement | null;
    const footer = document.querySelector('footer') as HTMLElement | null;

    if (navbar) navbar.style.display = 'none';
    if (footer) footer.style.display = 'none';

    return () => {
      if (navbar) navbar.style.display = '';
      if (footer) footer.style.display = '';
    };
  }, []);

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-r from-pink-50 to-blue-50 dark:from-slate-900 dark:to-slate-800">
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.2, 1]
          }}
          transition={{
            rotate: { duration: 1, repeat: Infinity, ease: "linear" },
            scale: { duration: 1, repeat: Infinity, ease: "easeInOut" }
          }}
          className="w-16 h-16 rounded-full border-4 border-t-yellow-400 border-r-pink-400 border-b-blue-400 border-l-green-400"
        />
        <p className="mt-4 text-gray-700 dark:text-gray-300 font-medium">Verifying authentication...</p>
      </div>
    );
  }

  // If not authenticated, don't render anything (middleware will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-slate-900 w-full" id="dashboard-root" style={{ width: '100%', position: 'relative', left: 0, right: 0, overflow: 'hidden' }}>
      {/* Mobile sidebar toggle */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="p-2 rounded-lg bg-white dark:bg-slate-800 shadow-md text-gray-700 dark:text-gray-200"
          aria-label="Toggle menu"
        >
          {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>
      </div>

      {/* Sidebar - Desktop */}
      <div className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 bg-white dark:bg-slate-900 border-r border-gray-200 dark:border-gray-800 shadow-sm sidebar">
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="px-6 py-6 flex items-center">
            <Link href="/dashboard" className="flex items-center">
              <span className="text-2xl font-bold text-gray-900 dark:text-white">Partagily</span>
              <span className="ml-2 text-yellow-500">
                <Settings size={20} />
              </span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-4 py-3 text-sm rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-gray-100 dark:bg-slate-800 text-yellow-500 dark:text-yellow-400 font-medium border-l-4 border-yellow-500 dark:border-yellow-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-800/50 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <span className={`mr-3 ${isActive ? 'text-yellow-500 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>
                    {item.icon}
                  </span>
                  <span className={isActive ? 'font-medium' : ''}>{item.name}</span>
                  {isActive && (
                    <span className="ml-auto">
                      <ChevronRight size={16} className="text-yellow-500 dark:text-yellow-400" />
                    </span>
                  )}
                </Link>
              );
            })}
          </nav>

          {/* Bottom section */}
          <div className="p-4 mt-auto space-y-3">
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-all duration-200"
            >
              <LogOut size={20} className="mr-3 text-gray-500 dark:text-gray-400" />
              <span>Sign Out</span>
            </button>

            <a
              href="mailto:<EMAIL>"
              className="flex items-center w-full px-4 py-3 text-sm bg-yellow-400 hover:bg-yellow-500 text-black font-medium rounded-lg transition-all duration-200"
            >
              <HelpCircle size={20} className="mr-3" />
              <span>Need Help?</span>
            </a>
          </div>
        </div>
      </div>

      {/* Mobile sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            <motion.div
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className="fixed inset-y-0 left-0 z-50 w-full max-w-xs lg:hidden"
            >
              <div className="flex flex-col h-full bg-white dark:bg-slate-900 shadow-xl overflow-hidden rounded-r-xl">
                {/* Header with close button */}
                <div className="px-6 py-4 flex items-center justify-between border-b border-gray-200 dark:border-gray-800">
                  <Link href="/dashboard" className="flex items-center" onClick={() => setIsMobileMenuOpen(false)}>
                    <span className="text-xl font-bold text-gray-900 dark:text-white">Partagily</span>
                    <span className="ml-2 text-yellow-500">
                      <Settings size={18} />
                    </span>
                  </Link>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-2 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-800"
                    aria-label="Close menu"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Navigation */}
                <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                  {navItems.map((item) => {
                    const isActive = pathname === item.href;
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={`flex items-center px-4 py-3 text-sm rounded-lg transition-all duration-200 ${
                          isActive
                            ? 'bg-gray-100 dark:bg-slate-800 text-yellow-500 dark:text-yellow-400 font-medium border-l-4 border-yellow-500 dark:border-yellow-400'
                            : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-800/50 hover:text-gray-900 dark:hover:text-white'
                        }`}
                      >
                        <span className={`mr-3 ${isActive ? 'text-yellow-500 dark:text-yellow-400' : 'text-gray-500 dark:text-gray-400'}`}>
                          {item.icon}
                        </span>
                        <span className={isActive ? 'font-medium' : ''}>{item.name}</span>
                        {isActive && (
                          <span className="ml-auto">
                            <ChevronRight size={16} className="text-yellow-500 dark:text-yellow-400" />
                          </span>
                        )}
                      </Link>
                    );
                  })}
                </nav>

                {/* Bottom section */}
                <div className="p-4 mt-auto space-y-3 border-t border-gray-200 dark:border-gray-800">
                  <button
                    onClick={() => {
                      handleLogout();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-800/50 transition-all duration-200"
                  >
                    <LogOut size={20} className="mr-3 text-gray-500 dark:text-gray-400" />
                    <span>Sign Out</span>
                  </button>

                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center w-full px-4 py-3 text-sm bg-yellow-400 hover:bg-yellow-500 text-black font-medium rounded-lg transition-all duration-200"
                  >
                    <HelpCircle size={20} className="mr-3" />
                    <span>Need Help?</span>
                  </a>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Main content - with responsive padding */}
      <div className="flex flex-col flex-1 pl-0 lg:pl-64 w-full" id="dashboard-main-content" style={{ width: '100%', maxWidth: '100%', position: 'relative', left: 0, right: 0 }}>
        {/* Top bar */}
        <header className="sticky top-0 z-10 bg-white dark:bg-slate-900 border-b border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex items-center justify-between">
              <div className="pl-8 lg:pl-0"> {/* Add left padding on mobile for menu button */}
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                  <span className="mr-2">👋</span>
                  Welcome Back, {user?.name || 'User'}!
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  This is our Beta version – questions? Reach us anytime 💬
                </p>
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-slate-900 p-4 sm:p-6 lg:p-8 w-full">
          {children}
        </main>
      </div>
    </div>
  );
}
