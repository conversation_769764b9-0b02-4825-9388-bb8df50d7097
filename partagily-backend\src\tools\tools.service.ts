import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { ToolCookiesResponseDto } from './dto/tool-cookies.dto';

@Injectable()
export class ToolsService {
  constructor(private readonly prisma: PrismaService) {}

  // Mock tools for development
  private readonly mockTools = [
    {
      id: '520e0f4b-1273-475e-b2f8-2de932eebfd8',
      name: 'Netflix Premium',
      description: 'Access Netflix premium content with shared accounts.',
      status: 'Available',
      category: 'streaming',
      websiteUrl: 'https://www.netflix.com',
      logoUrl: '/images/tools/netflix.png',
      requiredPlan: 'Premium'
    },
    {
      id: '2',
      name: 'Microsoft Office 365',
      description: 'Use Word, Excel, PowerPoint and other Office applications.',
      status: 'Available',
      category: 'writing',
      websiteUrl: 'https://www.office.com',
      logoUrl: '/images/tools/office365.png',
      requiredPlan: 'Standard'
    },
    {
      id: '3',
      name: 'Canva Pro',
      description: 'Create professional designs with premium Canva features.',
      status: 'Available',
      category: 'design',
      websiteUrl: 'https://www.canva.com',
      logoUrl: '/images/tools/canva.png',
      requiredPlan: 'Standard'
    },
    {
      id: '4',
      name: 'Spotify Premium',
      description: 'Ad-free music streaming with offline downloads.',
      status: 'Available',
      category: 'streaming',
      websiteUrl: 'https://www.spotify.com',
      logoUrl: '/images/tools/spotify.png',
      requiredPlan: 'Standard'
    },
    {
      id: '5',
      name: 'ChatGPT Plus',
      description: 'Priority access to OpenAI\'s advanced AI assistant.',
      status: 'Available',
      category: 'ai',
      websiteUrl: 'https://chat.openai.com',
      logoUrl: '/images/tools/chatgpt.png',
      requiredPlan: 'Premium'
    },
    {
      id: '6',
      name: 'Disney+ Premium',
      description: 'Stream Disney, Marvel, Star Wars, and more.',
      status: 'Available',
      category: 'streaming',
      websiteUrl: 'https://www.disneyplus.com',
      logoUrl: '/images/tools/disney.png',
      requiredPlan: 'Standard'
    }
  ];

  async findAll() {
    try {
      // Get tools from the database using Prisma client
      // Use try-catch to handle potential enum mismatches
      try {
        const dbTools = await this.prisma.tool.findMany({
        include: {
          plans: {
            include: {
              plan: true
            }
          }
        }
      });

        // If we have tools in the database, format and return them
        if (dbTools && dbTools.length > 0) {
          return {
            tools: dbTools.map(tool => ({
              ...tool,
              plans: tool.plans.map(planTool => ({
                id: planTool.plan.id,
                name: planTool.plan.name,
                price: planTool.plan.price,
                description: planTool.plan.description,
                features: planTool.plan.features
              }))
            }))
          };
        }
      } catch (dbError) {
        console.error('Error querying database:', dbError);
        // Continue to fallback mock data
      }

      console.log('No tools found in database, using mock data');

      // Fallback to mock data if no tools in database
      return {
        tools: this.mockTools.map(tool => ({
          ...tool,
          plans: [
            {
              id: `${tool.id}-1`,
              name: 'Monthly',
              price: tool.requiredPlan === 'Premium' ? 19.99 : 9.99,
              duration: 'month',
              isPopular: true
            },
            {
              id: `${tool.id}-2`,
              name: 'Yearly',
              price: tool.requiredPlan === 'Premium' ? 199.99 : 99.99,
              duration: 'year',
              isPopular: false
            }
          ]
        }))
      };
    } catch (error) {
      console.error('Error fetching tools from database:', error);
      throw new Error(`Failed to fetch tools: ${error.message}`);
    }
  }

  async findOne(id: string) {
    try {
      // Get the tool from the database using Prisma client
      try {
        const dbTool = await this.prisma.tool.findUnique({
          where: { id },
          include: {
            plans: {
              include: {
                plan: true
              }
            },
            cookies: true
          }
        });

        // If we found the tool, format and return it
        if (dbTool) {
          return {
            ...dbTool,
            plans: dbTool.plans.map(planTool => ({
              id: planTool.plan.id,
              name: planTool.plan.name,
              price: planTool.plan.price,
              description: planTool.plan.description,
              features: planTool.plan.features
            }))
          };
        }
      } catch (dbError) {
        console.error(`Error querying database for tool ${id}:`, dbError);
        // Continue to fallback mock data
      }

      console.log(`Tool with ID ${id} not found in database, using mock data`);

      // Fallback to mock data if tool not found
      const mockTool = this.mockTools.find(tool => tool.id === id);
      if (mockTool) {
        return {
          ...mockTool,
          plans: [
            {
              id: `${mockTool.id}-1`,
              name: 'Monthly',
              price: mockTool.requiredPlan === 'Premium' ? 19.99 : 9.99,
              duration: 'month',
              isPopular: true
            },
            {
              id: `${mockTool.id}-2`,
              name: 'Yearly',
              price: mockTool.requiredPlan === 'Premium' ? 199.99 : 99.99,
              duration: 'year',
              isPopular: false
            }
          ]
        };
      }

      return null;
    } catch (error) {
      console.error(`Error fetching tool ${id} from database:`, error);
      throw new Error(`Failed to fetch tool: ${error.message}`);
    }
  }

  async findByCategory(category: string) {
    try {
      // Get tools by category from the database using Prisma client
      try {
        const dbTools = await this.prisma.tool.findMany({
          where: {
            category: category as any // Cast to any to match the enum
          },
          include: {
            plans: {
              include: {
                plan: true
              }
            }
          }
        });

        // If we found tools, format and return them
        if (dbTools && dbTools.length > 0) {
          return dbTools.map(tool => ({
            ...tool,
            plans: tool.plans.map(planTool => ({
              id: planTool.plan.id,
              name: planTool.plan.name,
              price: planTool.plan.price,
              description: planTool.plan.description,
              features: planTool.plan.features
            }))
          }));
        }
      } catch (dbError) {
        console.error(`Error querying database for category ${category}:`, dbError);
        // Continue to fallback mock data
      }

      console.log(`No tools found for category ${category}, using mock data`);

      // Fallback to mock data if no tools found
      const { tools } = await this.findAll();
      return (tools as any[]).filter(tool => tool.category === category);
    } catch (error) {
      console.error(`Error fetching tools for category ${category}:`, error);
      throw new Error(`Failed to fetch tools by category: ${error.message}`);
    }
  }

  async findByPlan(plan: string) {
    try {
      // For Gold plan, we want to include all tools
      // For Premium plan, we want to include Standard and Premium tools
      // For Standard plan, we want to include only Standard tools
      let requiredPlans = [];
      if (plan === 'GOLD') {
        requiredPlans = ['STANDARD', 'PREMIUM', 'GOLD'];
      } else if (plan === 'PREMIUM') {
        requiredPlans = ['STANDARD', 'PREMIUM'];
      } else if (plan === 'STANDARD') {
        requiredPlans = ['STANDARD'];
      }

      // Get tools by required plan from the database using Prisma client
      try {
        const dbTools = await this.prisma.tool.findMany({
          where: {
            requiredPlan: {
              in: requiredPlans as any[] // Cast to any[] to match the enum
            }
          },
          include: {
            plans: {
              include: {
                plan: true
              }
            }
          }
        });

        // If we found tools, format and return them
        if (dbTools && dbTools.length > 0) {
          return dbTools.map(tool => ({
            ...tool,
            plans: tool.plans.map(planTool => ({
              id: planTool.plan.id,
              name: planTool.plan.name,
              price: planTool.plan.price,
              description: planTool.plan.description,
              features: planTool.plan.features
            }))
          }));
        }
      } catch (dbError) {
        console.error(`Error querying database for plan ${plan}:`, dbError);
        // Continue to fallback mock data
      }

      console.log(`No tools found for plan ${plan}, using mock data`);

      // Fallback to mock data if no tools found
      const { tools } = await this.findAll();
      return (tools as any[]).filter(tool => {
        if (plan === 'Gold') return true;
        if (plan === 'Premium') return tool.requiredPlan !== 'Gold';
        if (plan === 'Standard') return tool.requiredPlan === 'Standard';
        return false;
      });
    } catch (error) {
      console.error(`Error fetching tools for plan ${plan}:`, error);
      throw new Error(`Failed to fetch tools by plan: ${error.message}`);
    }
  }

  /**
   * Check if a user has access to a specific tool
   * @param userId The user ID
   * @param toolId The tool ID
   * @returns Boolean indicating if the user has access
   */
  async userHasAccessToTool(userId: string, toolId: string): Promise<boolean> {
    try {
      // Check if the user has an active subscription that includes this tool
      const userSubscriptions = await this.prisma.subscription.findMany({
        where: {
          userId: userId,
          status: 'ACTIVE'
        },
        include: {
          plan: {
            include: {
              includedTools: {
                where: {
                  toolId: toolId
                }
              }
            }
          }
        }
      });

      // If the user has any active subscription that includes this tool, they have access
      return userSubscriptions.some(subscription =>
        subscription.plan.includedTools.length > 0
      );
    } catch (error) {
      console.error(`Error checking user ${userId} access to tool ${toolId}:`, error);
      throw new Error(`Failed to check tool access: ${error.message}`);
    }
  }

  /**
   * Get cookies for a specific tool
   * @param toolId The tool ID
   * @returns Cookies for the tool
   */
  async getToolCookies(toolId: string): Promise<ToolCookiesResponseDto> {
    try {
      // Get the tool with its cookies
      const tool = await this.prisma.tool.findUnique({
        where: { id: toolId },
        include: { cookies: true }
      });

      if (!tool) {
        throw new NotFoundException('Tool not found');
      }

      // Format the response
      return {
        toolId: tool.id,
        name: tool.name,
        url: tool.domain || `https://www.${tool.name.toLowerCase().replace(/\s+/g, '')}.com`,
        cookies: tool.cookies.map(cookie => ({
          name: cookie.name,
          value: cookie.value,
          domain: cookie.domain,
          path: cookie.path || '/',
          secure: cookie.isSecure,
          httpOnly: cookie.isHttpOnly,
          sameSite: cookie.sameSite as 'strict' | 'lax' | 'none',
          expiresAt: cookie.expiresAt?.toISOString()
        }))
      };
    } catch (error) {
      console.error(`Error getting cookies for tool ${toolId}:`, error);
      throw error;
    }
  }
}
