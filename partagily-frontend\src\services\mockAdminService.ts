// Mock Admin Service
// This service provides mock data for the admin dashboard when the backend is not available

// Mock data storage
const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    isActive: true,
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
  },
  {
    id: '3',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
  },
];

const mockTools = [
  {
    id: '1',
    name: 'ChatGPT Plus',
    description: 'Advanced AI chatbot with GPT-4 capabilities',
    websiteUrl: 'https://chat.openai.com',
    logoUrl: '/images/tools/chatgpt.png',
    category: 'AI',
    isActive: true,
    createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    plans: [
      {
        id: '1',
        name: 'Monthly',
        price: 19.99,
        duration: 'month',
        features: ['GPT-4 access', 'Faster response times', 'Priority access'],
        isPopular: true,
      }
    ]
  },
  {
    id: '2',
    name: 'Midjourney',
    description: 'AI image generation tool',
    websiteUrl: 'https://midjourney.com',
    logoUrl: '/images/tools/midjourney.png',
    category: 'AI',
    isActive: true,
    createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    plans: [
      {
        id: '2',
        name: 'Basic',
        price: 10,
        duration: 'month',
        features: ['200 images per month', 'Standard quality'],
        isPopular: false,
      },
      {
        id: '3',
        name: 'Standard',
        price: 30,
        duration: 'month',
        features: ['Unlimited images', 'High quality', 'Fast generation'],
        isPopular: true,
      }
    ]
  },
  {
    id: '3',
    name: 'Notion Premium',
    description: 'All-in-one workspace for notes, tasks, wikis, and databases',
    websiteUrl: 'https://notion.so',
    logoUrl: '/images/tools/notion.png',
    category: 'Productivity',
    isActive: true,
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    plans: [
      {
        id: '4',
        name: 'Plus',
        price: 8,
        duration: 'month',
        features: ['Unlimited blocks', 'File uploads up to 5GB', '100 guests'],
        isPopular: true,
      }
    ]
  },
];

const mockSubscriptions = [
  {
    id: '1',
    userId: '1',
    toolId: '1',
    planId: '1',
    status: 'active',
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    user: {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    tool: {
      id: '1',
      name: 'ChatGPT Plus',
      logoUrl: '/images/tools/chatgpt.png',
    },
    plan: {
      id: '1',
      name: 'Monthly',
      price: 19.99,
      duration: 'month',
    },
  },
  {
    id: '2',
    userId: '2',
    toolId: '2',
    planId: '3',
    status: 'active',
    startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    user: {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    tool: {
      id: '2',
      name: 'Midjourney',
      logoUrl: '/images/tools/midjourney.png',
    },
    plan: {
      id: '3',
      name: 'Standard',
      price: 30,
      duration: 'month',
    },
  },
];

const mockPayments = [
  {
    id: '1',
    orderId: 'ORD-001',
    userId: '1',
    toolId: '1',
    planId: '1',
    amount: 19.99,
    currency: 'USD',
    status: 'completed',
    paymentMethod: 'credit_card',
    transactionId: 'TXN-001',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    user: {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    tool: {
      id: '1',
      name: 'ChatGPT Plus',
    },
    plan: {
      id: '1',
      name: 'Monthly',
      price: 19.99,
      duration: 'month',
    },
  },
  {
    id: '2',
    orderId: 'ORD-002',
    userId: '2',
    toolId: '2',
    planId: '3',
    amount: 30,
    currency: 'USD',
    status: 'completed',
    paymentMethod: 'paypal',
    transactionId: 'TXN-002',
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
    user: {
      id: '2',
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    tool: {
      id: '2',
      name: 'Midjourney',
    },
    plan: {
      id: '3',
      name: 'Standard',
      price: 30,
      duration: 'month',
    },
  },
];

// Helper functions
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const findUserById = (id: string) => {
  return mockUsers.find(user => user.id === id);
};

const findToolById = (id: string) => {
  return mockTools.find(tool => tool.id === id);
};

const findSubscriptionById = (id: string) => {
  return mockSubscriptions.find(subscription => subscription.id === id);
};

const findPaymentById = (id: string) => {
  return mockPayments.find(payment => payment.id === id);
};

// Mock Admin Service
const mockAdminService = {
  // Dashboard
  async getDashboardOverview(): Promise<any> {
    await delay(500); // Simulate API delay
    return {
      userStats: {
        totalUsers: mockUsers.length,
        newUsers: 2,
        growthRate: 10,
      },
      revenueStats: {
        totalRevenue: 1000,
        periodRevenue: 100,
        growthRate: 10,
      },
      subscriptionStats: {
        totalActiveSubscriptions: mockSubscriptions.length,
        newSubscriptions: 1,
        growthRate: 5,
      },
      popularTools: [
        { name: 'ChatGPT Plus', subscriptionCount: 1 },
        { name: 'Midjourney', subscriptionCount: 1 },
      ],
      recentTransactions: mockPayments.map(payment => ({
        id: payment.id,
        orderNumber: payment.orderId,
        amount: payment.amount,
        status: payment.status,
        createdAt: payment.createdAt,
        user: { name: payment.user.name }
      })),
      userGrowthData: [
        { date: '2023-01', users: 1 },
        { date: '2023-02', users: 2 },
        { date: '2023-03', users: 3 },
      ],
    };
  },

  // User management
  async getUsers(filter: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    let filteredUsers = [...mockUsers];
    
    // Apply filters
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(searchLower) || 
        user.email.toLowerCase().includes(searchLower)
      );
    }
    
    if (filter.role) {
      filteredUsers = filteredUsers.filter(user => user.role === filter.role);
    }
    
    if (filter.isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isActive === filter.isActive);
    }
    
    return {
      users: filteredUsers,
      total: filteredUsers.length,
    };
  },

  async getUserById(id: string): Promise<any> {
    await delay(300); // Simulate API delay
    
    const user = findUserById(id);
    if (!user) {
      throw new Error(`User with ID ${id} not found`);
    }
    
    return user;
  },

  async createUser(userData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const newUser = {
      id: (mockUsers.length + 1).toString(),
      ...userData,
      createdAt: new Date().toISOString(),
    };
    
    mockUsers.push(newUser);
    
    return newUser;
  },

  async updateUser(id: string, userData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const userIndex = mockUsers.findIndex(user => user.id === id);
    if (userIndex === -1) {
      throw new Error(`User with ID ${id} not found`);
    }
    
    // Update user data
    const updatedUser = {
      ...mockUsers[userIndex],
      ...userData,
    };
    
    mockUsers[userIndex] = updatedUser;
    
    return updatedUser;
  },

  async deleteUser(id: string): Promise<void> {
    await delay(500); // Simulate API delay
    
    const userIndex = mockUsers.findIndex(user => user.id === id);
    if (userIndex === -1) {
      throw new Error(`User with ID ${id} not found`);
    }
    
    // Remove user
    mockUsers.splice(userIndex, 1);
    
    // Remove associated subscriptions
    const subscriptionIndexesToRemove: number[] = [];
    mockSubscriptions.forEach((subscription, index) => {
      if (subscription.userId === id) {
        subscriptionIndexesToRemove.push(index);
      }
    });
    
    // Remove from end to start to avoid index shifting
    subscriptionIndexesToRemove.reverse().forEach(index => {
      mockSubscriptions.splice(index, 1);
    });
  },

  // Tool management
  async getTools(filter: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    let filteredTools = [...mockTools];
    
    // Apply filters
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      filteredTools = filteredTools.filter(tool => 
        tool.name.toLowerCase().includes(searchLower) || 
        tool.description.toLowerCase().includes(searchLower)
      );
    }
    
    if (filter.category) {
      filteredTools = filteredTools.filter(tool => tool.category === filter.category);
    }
    
    if (filter.isActive !== undefined) {
      filteredTools = filteredTools.filter(tool => tool.isActive === filter.isActive);
    }
    
    return {
      tools: filteredTools,
      total: filteredTools.length,
    };
  },

  async getToolById(id: string): Promise<any> {
    await delay(300); // Simulate API delay
    
    const tool = findToolById(id);
    if (!tool) {
      throw new Error(`Tool with ID ${id} not found`);
    }
    
    return tool;
  },

  async createTool(toolData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const newTool = {
      id: (mockTools.length + 1).toString(),
      ...toolData,
      createdAt: new Date().toISOString(),
    };
    
    mockTools.push(newTool);
    
    return newTool;
  },

  async updateTool(id: string, toolData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const toolIndex = mockTools.findIndex(tool => tool.id === id);
    if (toolIndex === -1) {
      throw new Error(`Tool with ID ${id} not found`);
    }
    
    // Update tool data
    const updatedTool = {
      ...mockTools[toolIndex],
      ...toolData,
    };
    
    mockTools[toolIndex] = updatedTool;
    
    return updatedTool;
  },

  async deleteTool(id: string): Promise<void> {
    await delay(500); // Simulate API delay
    
    const toolIndex = mockTools.findIndex(tool => tool.id === id);
    if (toolIndex === -1) {
      throw new Error(`Tool with ID ${id} not found`);
    }
    
    // Remove tool
    mockTools.splice(toolIndex, 1);
    
    // Remove associated subscriptions
    const subscriptionIndexesToRemove: number[] = [];
    mockSubscriptions.forEach((subscription, index) => {
      if (subscription.toolId === id) {
        subscriptionIndexesToRemove.push(index);
      }
    });
    
    // Remove from end to start to avoid index shifting
    subscriptionIndexesToRemove.reverse().forEach(index => {
      mockSubscriptions.splice(index, 1);
    });
  },

  // Subscription management
  async getSubscriptions(filter: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    let filteredSubscriptions = [...mockSubscriptions];
    
    // Apply filters
    if (filter.userId) {
      filteredSubscriptions = filteredSubscriptions.filter(subscription => 
        subscription.userId === filter.userId
      );
    }
    
    if (filter.toolId) {
      filteredSubscriptions = filteredSubscriptions.filter(subscription => 
        subscription.toolId === filter.toolId
      );
    }
    
    if (filter.status) {
      filteredSubscriptions = filteredSubscriptions.filter(subscription => 
        subscription.status === filter.status
      );
    }
    
    return {
      subscriptions: filteredSubscriptions,
      total: filteredSubscriptions.length,
    };
  },

  async getSubscriptionById(id: string): Promise<any> {
    await delay(300); // Simulate API delay
    
    const subscription = findSubscriptionById(id);
    if (!subscription) {
      throw new Error(`Subscription with ID ${id} not found`);
    }
    
    return subscription;
  },

  async createSubscription(subscriptionData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    // Find user and tool to include in the subscription
    const user = findUserById(subscriptionData.userId);
    const tool = findToolById(subscriptionData.toolId);
    
    if (!user) {
      throw new Error(`User with ID ${subscriptionData.userId} not found`);
    }
    
    if (!tool) {
      throw new Error(`Tool with ID ${subscriptionData.toolId} not found`);
    }
    
    // Find plan
    const plan = tool.plans.find((p: any) => p.id === subscriptionData.planId);
    if (!plan) {
      throw new Error(`Plan with ID ${subscriptionData.planId} not found for tool ${subscriptionData.toolId}`);
    }
    
    const newSubscription = {
      id: (mockSubscriptions.length + 1).toString(),
      ...subscriptionData,
      status: subscriptionData.status || 'active',
      createdAt: new Date().toISOString(),
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
      tool: {
        id: tool.id,
        name: tool.name,
        logoUrl: tool.logoUrl,
      },
      plan: {
        id: plan.id,
        name: plan.name,
        price: plan.price,
        duration: plan.duration,
      },
    };
    
    mockSubscriptions.push(newSubscription);
    
    return newSubscription;
  },

  async updateSubscription(id: string, subscriptionData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const subscriptionIndex = mockSubscriptions.findIndex(subscription => subscription.id === id);
    if (subscriptionIndex === -1) {
      throw new Error(`Subscription with ID ${id} not found`);
    }
    
    // Update subscription data
    const updatedSubscription = {
      ...mockSubscriptions[subscriptionIndex],
      ...subscriptionData,
    };
    
    mockSubscriptions[subscriptionIndex] = updatedSubscription;
    
    return updatedSubscription;
  },

  async deleteSubscription(id: string): Promise<void> {
    await delay(500); // Simulate API delay
    
    const subscriptionIndex = mockSubscriptions.findIndex(subscription => subscription.id === id);
    if (subscriptionIndex === -1) {
      throw new Error(`Subscription with ID ${id} not found`);
    }
    
    // Remove subscription
    mockSubscriptions.splice(subscriptionIndex, 1);
  },

  // Payment management
  async getPayments(filter: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    let filteredPayments = [...mockPayments];
    
    // Apply filters
    if (filter.userId) {
      filteredPayments = filteredPayments.filter(payment => 
        payment.userId === filter.userId
      );
    }
    
    if (filter.toolId) {
      filteredPayments = filteredPayments.filter(payment => 
        payment.toolId === filter.toolId
      );
    }
    
    if (filter.status) {
      filteredPayments = filteredPayments.filter(payment => 
        payment.status === filter.status
      );
    }
    
    if (filter.paymentMethod) {
      filteredPayments = filteredPayments.filter(payment => 
        payment.paymentMethod === filter.paymentMethod
      );
    }
    
    return {
      payments: filteredPayments,
      total: filteredPayments.length,
    };
  },

  async getPaymentById(id: string): Promise<any> {
    await delay(300); // Simulate API delay
    
    const payment = findPaymentById(id);
    if (!payment) {
      throw new Error(`Payment with ID ${id} not found`);
    }
    
    return payment;
  },

  async updatePayment(id: string, paymentData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    const paymentIndex = mockPayments.findIndex(payment => payment.id === id);
    if (paymentIndex === -1) {
      throw new Error(`Payment with ID ${id} not found`);
    }
    
    // Update payment data
    const updatedPayment = {
      ...mockPayments[paymentIndex],
      ...paymentData,
    };
    
    mockPayments[paymentIndex] = updatedPayment;
    
    return updatedPayment;
  },

  // Analytics
  async getAnalytics(query: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      userStats: {
        totalUsers: mockUsers.length,
        newUsers: 2,
        growthRate: 10,
      },
      revenueStats: {
        totalRevenue: 1000,
        periodRevenue: 100,
        growthRate: 10,
      },
      subscriptionStats: {
        totalActiveSubscriptions: mockSubscriptions.length,
        newSubscriptions: 1,
        growthRate: 5,
      },
      popularTools: [
        { name: 'ChatGPT Plus', subscriptionCount: 1 },
        { name: 'Midjourney', subscriptionCount: 1 },
      ],
      recentTransactions: mockPayments.map(payment => ({
        id: payment.id,
        orderNumber: payment.orderId,
        amount: payment.amount,
        status: payment.status,
        createdAt: payment.createdAt,
        user: { name: payment.user.name }
      })),
      userGrowthData: [
        { date: '2023-01', users: 1 },
        { date: '2023-02', users: 2 },
        { date: '2023-03', users: 3 },
      ],
    };
  },

  async getUserGrowthAnalytics(query: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      totalUsers: mockUsers.length,
      newUsers: 2,
      growthRate: 10,
      userGrowthData: [
        { date: '2023-01', users: 1 },
        { date: '2023-02', users: 2 },
        { date: '2023-03', users: 3 },
      ],
      userDistribution: [
        { role: 'user', count: 2 },
        { role: 'admin', count: 1 },
      ],
    };
  },

  async getRevenueAnalytics(query: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      totalRevenue: 1000,
      periodRevenue: 100,
      revenueOverTime: [
        { date: '2023-01', revenue: 100 },
        { date: '2023-02', revenue: 200 },
        { date: '2023-03', revenue: 300 },
      ],
      revenueDistribution: [
        { paymentMethod: 'credit_card', revenue: 600 },
        { paymentMethod: 'paypal', revenue: 300 },
        { paymentMethod: 'bank_transfer', revenue: 100 },
      ],
    };
  },

  async getSubscriptionAnalytics(query: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      totalActiveSubscriptions: mockSubscriptions.length,
      newSubscriptions: 1,
      expiringSubscriptions: 0,
      subscriptionGrowthData: [
        { date: '2023-01', newSubscriptions: 1, activeSubscriptions: 1 },
        { date: '2023-02', newSubscriptions: 1, activeSubscriptions: 2 },
        { date: '2023-03', newSubscriptions: 0, activeSubscriptions: 2 },
      ],
      subscriptionDistribution: [
        { name: 'ChatGPT Plus', count: 1 },
        { name: 'Midjourney', count: 1 },
      ],
    };
  },

  async getToolUsageAnalytics(query: any = {}): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      totalTools: mockTools.length,
      activeTools: mockTools.filter(tool => tool.isActive).length,
      popularTools: [
        { name: 'ChatGPT Plus', subscriptionCount: 1 },
        { name: 'Midjourney', subscriptionCount: 1 },
        { name: 'Notion Premium', subscriptionCount: 0 },
      ],
      toolDistribution: [
        { category: 'AI', count: 2 },
        { category: 'Productivity', count: 1 },
      ],
    };
  },

  // Settings
  async getSettings(): Promise<any> {
    await delay(300); // Simulate API delay
    
    return {
      general: {
        siteName: 'Partagily',
        siteDescription: 'Access premium tools through shared accounts',
        contactEmail: '<EMAIL>',
        supportPhone: '+216 12 345 678',
      },
      appearance: {
        primaryColor: '#F59E0B',
        secondaryColor: '#3B82F6',
        darkMode: false,
        logoUrl: '/images/logo.png',
        faviconUrl: '/favicon.ico',
      },
      payment: {
        currency: 'USD',
        taxRate: 0,
        enableKonnect: true,
        enablePaypal: false,
        enableStripe: false,
      },
      email: {
        enableEmailNotifications: true,
        welcomeEmailTemplate: 'Welcome to Partagily!',
        orderConfirmationTemplate: 'Your order has been confirmed',
        subscriptionExpiryTemplate: 'Your subscription is expiring soon',
      },
      security: {
        enableTwoFactorAuth: false,
        passwordMinLength: 8,
        requireSpecialChars: true,
        sessionTimeout: 60,
      },
    };
  },

  async updateSettings(settingsData: any): Promise<any> {
    await delay(500); // Simulate API delay
    
    return {
      success: true,
      message: 'Settings updated successfully',
      settings: settingsData,
    };
  },
};

export default mockAdminService;
