'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useNotification } from '@/contexts/NotificationContext';
import { motion } from 'framer-motion';
import PasswordInput from '@/components/common/PasswordInput';

export default function SignIn() {
  // Check if there's a recently registered email to pre-fill
  const lastRegisteredEmail = typeof window !== 'undefined' ? localStorage.getItem('lastRegisteredEmail') : '';
  console.log('Last registered email:', lastRegisteredEmail);


  const [email, setEmail] = useState(lastRegisteredEmail || '');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isNewUser, setIsNewUser] = useState(!!lastRegisteredEmail);

  const { login, error, clearError } = useAuth();
  const { showNotification } = useNotification();

  // Clear the last registered email after using it
  useEffect(() => {
    if (lastRegisteredEmail) {
      localStorage.removeItem('lastRegisteredEmail');
      showNotification('info', 'Please login with your new account credentials', {
        autoClose: true
      });
    }
  }, [lastRegisteredEmail, showNotification]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
console.log('Form submitted:', { email, password, rememberMe });

    if (!email && !password) {
      // Only show notification, don't set form error
      showNotification('error', 'Please enter both email and password', {
        autoClose: false
      });
      return;
    }
    if (!email) {
      // Only show notification, don't set form error
      showNotification('error', 'Please enter your email address', {
        autoClose: false
      });
      return;
    }
    if (!password) {
      // Only show notification, don't set form error
      showNotification('error', 'Please enter your password', {
        autoClose: false
      });
      return;
    }

    setIsSubmitting(true);
    // Don't set error messages in the form
    clearError();

    try {
      // Attempt to login
      await login(email, password, rememberMe);

      // If we get here, login was successful
      showNotification('success', 'Login successful! Redirecting to dashboard...', {
        autoClose: true
      });
      // The login function will handle the redirect
    } catch (loginError: any) {
      // Suppress console error for expected login failures
      if (!loginError.message?.includes('mock backend')) {
        // Determine the appropriate error message
        let errorMsg = 'Invalid email or password. Please try again.';

        if (loginError.message?.includes('Network Error') ||
            loginError.message?.includes('timeout') ||
            loginError.message?.includes('unreachable')) {
          errorMsg = 'Unable to connect to the server. Please check if the backend server is running and try again.';
        } else if (loginError.response?.status === 429) {
          errorMsg = 'Too many login attempts. Please try again later.';
        }

        // Only show notification, don't set form error
        showNotification('error', errorMsg, {
          autoClose: false,
          action: {
            label: 'Try Again',
            onClick: () => {
              // Focus on the password field and select its content
            //   const passwordInput = document.getElementById('password') as HTMLInputElement;
            //   if (passwordInput) {
            //     passwordInput.focus();
            //     passwordInput.select();
            //   }
            // }
            window.location.reload(); // Reload the page to reset the form
          }
        }
        });

        // Clear the password field for security
        setPassword('');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="auth-container">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="auth-card"
      >
        <div className="px-6 py-8 sm:p-10">
          <div className="auth-header">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="auth-title">
                Welcome Back <span className="auth-highlight">!</span>
              </h2>
            </motion.div>
          </div>

          {/* Error messages are now shown only in notifications */}

          <form className="space-y-8" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  //required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="auth-input"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 terminal-text">
                Password
              </label>
              <div className="mt-1">
                <PasswordInput
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  autoComplete="current-password"
                  className="auth-input"
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 dark:border-gray-600 rounded dark:bg-slate-800 dark:checked:bg-yellow-500"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link href="/forgot-password" className="auth-link">
                  Lost Password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="auth-button"
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </span>
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="auth-divider">
              <div className="auth-divider-line"></div>
              <div className="auth-divider-text">
                <span>Not registered?</span>
              </div>
            </div>

            <div className="mt-6">
              <Link
                href="/signup"
                className="create-account-button"
              >
                Create account
              </Link>
            </div>
          </div>
        </div>

        <div className="auth-footer"></div>
      </motion.div>
    </div>
  );
}
