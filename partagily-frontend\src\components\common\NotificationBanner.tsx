'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>, ArrowRight, Zap } from 'lucide-react';
import LinkComponent from '../ui/LinkComponent';

const NotificationBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    // Check if banner was previously dismissed
    const dismissed = localStorage.getItem('ai-tools-banner-dismissed');
    const dismissedDate = dismissed ? new Date(dismissed) : null;
    const now = new Date();

    // Show banner if not dismissed or if dismissed more than 7 days ago
    if (!dismissed || (dismissedDate && (now.getTime() - dismissedDate.getTime()) > 7 * 24 * 60 * 60 * 1000)) {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('ai-tools-banner-dismissed', new Date().toISOString());

    // Dispatch custom event to notify navbar
    window.dispatchEvent(new CustomEvent('banner-dismissed'));
  };

  // Don't render anything until mounted (prevents hydration mismatch)
  if (!isMounted) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="fixed top-0 left-0 right-0 z-[100] bg-[#ff9800] text-white shadow-xl overflow-hidden"
        >
          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-20">
            <motion.div
              className="h-full w-[200%] bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{ x: ['-100%', '100%'] }}
              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
            />
          </div>

          {/* Floating particles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-white/40 rounded-full"
                style={{
                  left: `${10 + i * 15}%`,
                  top: `${20 + (i % 2) * 40}%`,
                }}
                animate={{
                  y: [0, -10, 0],
                  opacity: [0.4, 0.8, 0.4],
                }}
                transition={{
                  duration: 2 + i * 0.5,
                  repeat: Infinity,
                  delay: i * 0.3,
                }}
              />
            ))}
          </div>

          <div className="relative container mx-auto px-4 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Tunisia flag and animated icon */}
                <div className="flex items-center space-x-3">
                  <motion.div
                    className="w-8 h-8 rounded-full overflow-hidden border-2 border-white shadow-lg"
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <img src="/tunisia-flag.svg" alt="Tunisia" className="w-full h-full object-cover" />
                  </motion.div>
                  <motion.div
                    animate={{ scale: [1, 1.2, 1], rotate: [0, 180, 360] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Zap className="w-6 h-6 text-white" />
                  </motion.div>
                </div>

                {/* Scrolling text container */}
                <div className="flex-1 overflow-hidden">
                  <motion.div
                    className="flex items-center space-x-8 whitespace-nowrap"
                    animate={{ x: [0, -300] }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="bg-white text-[#FFAD00] px-3 py-1 rounded-full text-xs font-bold">
                        NOUVEAU
                      </span>
                      <span className="font-bold text-lg">
                        20+ Outils IA maintenant disponibles
                      </span>
                      <Sparkles className="w-5 h-5 text-white animate-pulse" />
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="font-semibold text-lg">
                        Made for Tunisia 🔥
                      </span>
                      <span className="text-white/90">•</span>
                      <span className="font-medium">
                        Accès premium sans blocage
                      </span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="bg-white text-[#FFAD00] px-3 py-1 rounded-full text-xs font-bold">
                        NOUVEAU
                      </span>
                      <span className="font-bold text-lg">
                        20+ Outils IA maintenant disponibles
                      </span>
                      <Sparkles className="w-5 h-5 text-white animate-pulse" />
                    </div>
                  </motion.div>
                </div>
              </div>

              {/* CTA and Close button */}
              <div className="flex items-center space-x-3">
                {/* CTA Button - Hidden on mobile */}
                <LinkComponent
                  href="/tools"
                  className="hidden sm:flex items-center space-x-2 bg-white text-[#FFAD00] px-6 py-2 rounded-full text-sm font-bold hover:bg-white/90 hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span>Explorer</span>
                  <ArrowRight className="w-4 h-4" />
                </LinkComponent>

                {/* Close button */}
                <motion.button
                  onClick={handleDismiss}
                  className="p-2 hover:bg-white/20 rounded-full transition-colors duration-200"
                  aria-label="Fermer la notification"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <X className="w-5 h-5" />
                </motion.button>
              </div>
            </div>

            {/* Mobile CTA */}
            <div className="sm:hidden mt-2">
              <LinkComponent
                href="/tools"
                className="flex items-center justify-center space-x-2 bg-white text-[#FFAD00] px-6 py-3 rounded-full text-sm font-bold hover:bg-white/90 transition-colors duration-200 w-full shadow-lg"
              >
                <span>Explorer les outils IA</span>
                <ArrowRight className="w-4 h-4" />
              </LinkComponent>
            </div>
          </div>

          {/* Animated bottom border */}
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-white/60 via-white to-white/60"
            animate={{
              background: [
                'linear-gradient(90deg, rgba(255,255,255,0.6) 0%, rgba(255,255,255,1) 50%, rgba(255,255,255,0.6) 100%)',
                'linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0.6) 50%, rgba(255,255,255,1) 100%)'
              ]
            }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NotificationBanner;
