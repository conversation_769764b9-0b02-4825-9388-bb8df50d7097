'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import LinkComponent from '../ui/LinkComponent';

const NotificationBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    
    // Check if banner was previously dismissed
    const dismissed = localStorage.getItem('ai-tools-banner-dismissed');
    const dismissedDate = dismissed ? new Date(dismissed) : null;
    const now = new Date();
    
    // Show banner if not dismissed or if dismissed more than 7 days ago
    if (!dismissed || (dismissedDate && (now.getTime() - dismissedDate.getTime()) > 7 * 24 * 60 * 60 * 1000)) {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('ai-tools-banner-dismissed', new Date().toISOString());
  };

  // Don't render anything until mounted (prevents hydration mismatch)
  if (!isMounted) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative bg-gradient-to-r from-red-600 via-red-500 to-green-600 text-white shadow-lg z-50"
        >
          {/* Tunisia flag pattern background */}
          <div className="absolute inset-0 opacity-10">
            <div className="h-full w-full bg-gradient-to-r from-red-600 via-white to-green-600"></div>
          </div>
          
          <div className="relative container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* Tunisia flag emoji and sparkles */}
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">🇹🇳</span>
                  <Sparkles className="w-5 h-5 text-yellow-300 animate-pulse" />
                </div>
                
                {/* Main message */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2">
                  <span className="font-bold text-sm sm:text-base">
                    <span className="bg-yellow-400 text-black px-2 py-1 rounded-full text-xs font-bold mr-2">
                      NOUVEAU
                    </span>
                    20+ Outils IA maintenant disponibles
                  </span>
                  <span className="text-yellow-300 font-semibold text-sm">
                    Made for Tunisia 🔥
                  </span>
                </div>
              </div>

              {/* CTA and Close button */}
              <div className="flex items-center space-x-3">
                {/* CTA Button - Hidden on mobile */}
                <LinkComponent
                  href="/tools"
                  className="hidden sm:flex items-center space-x-1 bg-white text-red-600 px-4 py-2 rounded-full text-sm font-semibold hover:bg-yellow-50 transition-colors duration-200"
                >
                  <span>Explorer les outils</span>
                  <ArrowRight className="w-4 h-4" />
                </LinkComponent>

                {/* Close button */}
                <button
                  onClick={handleDismiss}
                  className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200"
                  aria-label="Fermer la notification"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Mobile CTA */}
            <div className="sm:hidden mt-2">
              <LinkComponent
                href="/tools"
                className="flex items-center justify-center space-x-1 bg-white text-red-600 px-4 py-2 rounded-full text-sm font-semibold hover:bg-yellow-50 transition-colors duration-200 w-full"
              >
                <span>Explorer les outils IA</span>
                <ArrowRight className="w-4 h-4" />
              </LinkComponent>
            </div>
          </div>

          {/* Animated border */}
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 via-yellow-300 to-yellow-400 animate-pulse"></div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default NotificationBanner;
