'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Check, ShoppingCart } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { useNotification } from '@/contexts/NotificationContext';

interface PlanCardProps {
  plan: {
    id: string;
    name: string;
    description: string;
    price: number;
    features: string[];
    includedTools?: { id: string; name: string }[];
  };
}

const PlanCard: React.FC<PlanCardProps> = ({ plan }) => {
  const { addToCart, isLoading } = useCart();
  const { showNotification } = useNotification();

  const handleAddToCart = () => {
    console.log('PlanCard - handleAddToCart clicked for plan:', plan.id);

    // Ensure the plan ID exists
    if (!plan.id) {
      console.error('Missing plan ID');
      showNotification('error', 'Plan ID is missing. Please try again with a different plan.', {
        autoClose: true,
      });
      return;
    }

    // Add the plan to the cart
    addToCart(plan.id, 'PLAN');
  };
  return (
    <motion.div
      className="bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-700"
      whileHover={{ y: -5 }}
    >
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">{plan.description}</p>

        <div className="mb-6">
          <span className="text-3xl font-bold text-gray-900 dark:text-white">
            ${typeof plan.price === 'number' ? plan.price.toFixed(2) : '0.00'}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-2">/month</span>
        </div>

        <div className="space-y-3 mb-6">
          {Array.isArray(plan.features) && plan.features.map((feature, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mt-0.5 mr-3">
                <Check size={12} className="text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-300">{feature}</span>
            </div>
          ))}
        </div>

        {plan.includedTools && Array.isArray(plan.includedTools) && plan.includedTools.length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Included Tools:</h4>
            <div className="space-y-1">
              {plan.includedTools.map((tool) => (
                <div key={tool.id || Math.random().toString(36).substring(2, 9)} className="text-xs text-gray-600 dark:text-gray-400">
                  • {tool.name || 'Unknown Tool'}
                </div>
              ))}
            </div>
          </div>
        )}

        <button
          onClick={handleAddToCart}
          disabled={isLoading}
          className="w-full bg-yellow-400 hover:bg-yellow-500 text-black font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-black mr-2"></div>
              Adding...
            </>
          ) : (
            <>
              <ShoppingCart size={16} className="mr-2" />
              Add to Cart
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default PlanCard;
