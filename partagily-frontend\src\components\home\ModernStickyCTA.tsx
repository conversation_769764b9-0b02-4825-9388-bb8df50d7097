'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

const ModernStickyCTA = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show sticky CTA after scrolling down 500px
      const scrollY = window.scrollY;
      setIsVisible(scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div 
      className={`fixed bottom-0 left-0 right-0 glass-card p-4 border-t border-[rgba(255,255,255,0.1)] transition-transform duration-300 md:hidden z-50 ${
        isVisible ? 'translate-y-0' : 'translate-y-full'
      }`}
    >
      <div className="flex justify-between items-center">
        <div className="text-sm">
          <p className="text-gray-300">Ready to get started?</p>
          <p className="text-[#FFAD00] font-bold">Pay with local methods!</p>
        </div>
        <Link
          href="/signup"
          className="btn btn-primary btn-sm btn-icon hover-glow whitespace-nowrap"
        >
          Try it now <ArrowRight size={16} />
        </Link>
      </div>
    </div>
  );
};

export default ModernStickyCTA;
