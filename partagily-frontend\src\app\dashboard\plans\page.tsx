'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, Search, X } from 'lucide-react';
import userService from '@/services/userService';
import PlanCard from '@/components/dashboard/PlanCard';
import { useCart } from '@/contexts/CartContext';

// Define the plan type
type Plan = {
  id: string;
  name: string;
  description: string;
  price: number;
  billingCycle?: string;
  features: string[];
  popular?: boolean;
  includedTools?: { id: string; name: string }[];
};

// Payment method component
const PaymentMethod = ({
  method,
  icon,
  name,
  selected,
  onSelect
}: {
  method: string,
  icon: string,
  name: string,
  selected: boolean,
  onSelect: () => void
}) => {
  return (
    <div
      className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors duration-200 ${
        selected ? 'border-yellow-400 bg-yellow-50' : 'border-gray-200 hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-center flex-1">
        <span className="text-2xl mr-3">{icon}</span>
        <span className="font-medium">{name}</span>
      </div>
      <div className="flex items-center justify-center w-6 h-6 rounded-full border-2 border-gray-300">
        {selected && <div className="w-3 h-3 rounded-full bg-yellow-400"></div>}
      </div>
    </div>
  );
};

export default function Plans() {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const { cartItemCount, openCart } = useCart();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Fetching plans in plans page...');

      // Fetch plans from the backend
      const response = await userService.getPlans();

      console.log('Plans response:', response);

      // Check if response.plans exists
      if (!response || !response.plans) {
        console.error('Invalid response format:', response);
        throw new Error('Invalid response format');
      }

      // Transform the data to match our component's expected format
      const formattedPlans = response.plans.map((plan: any) => ({
        id: plan.id,
        name: plan.name,
        description: plan.description || `${plan.name} subscription plan`,
        price: plan.price || 0,
        features: plan.features || [
          `Access to ${plan.name} plan`,
          'Customer support',
          'Monthly billing',
          'Cancel anytime'
        ],
        includedTools: plan.includedTools || []
      }));

      // If no plans were returned, use default plans
      if (formattedPlans.length === 0) {
        console.log('No plans returned, using default plans');
        // Define mock plans directly
        const mockPlans = [
          {
            id: '1',
            name: 'Standard',
            tier: 'STANDARD',
            price: 9.99,
            description: 'Basic access to shared accounts',
            features: [
              'Access to 10+ premium tools',
              'Basic support',
              '1 concurrent login',
              'Standard availability'
            ],
            includedTools: [
              { id: '3', name: 'Microsoft Office 365' },
              { id: '4', name: 'Spotify Premium' },
              { id: '6', name: 'Disney+' }
            ]
          },
          {
            id: '2',
            name: 'Premium',
            tier: 'PREMIUM',
            price: 19.99,
            description: 'Enhanced access to premium accounts',
            features: [
              'Access to 50+ premium tools',
              'Priority support',
              '2 concurrent logins',
              'High availability',
              'Premium tools included'
            ],
            includedTools: [
              { id: '1', name: 'Netflix' },
              { id: '2', name: 'Adobe Creative Cloud' },
              { id: '3', name: 'Microsoft Office 365' },
              { id: '4', name: 'Spotify Premium' },
              { id: '6', name: 'Disney+' }
            ]
          },
          {
            id: '3',
            name: 'Gold',
            tier: 'GOLD',
            price: 29.99,
            description: 'Full access to all premium tools',
            features: [
              'Access to 100+ premium tools',
              '24/7 support',
              '3 concurrent logins',
              'Highest availability',
              'All premium tools included',
              'Early access to new tools'
            ],
            includedTools: [
              { id: '1', name: 'Netflix' },
              { id: '2', name: 'Adobe Creative Cloud' },
              { id: '3', name: 'Microsoft Office 365' },
              { id: '4', name: 'Spotify Premium' },
              { id: '5', name: 'ChatGPT Plus' },
              { id: '6', name: 'Disney+' }
            ]
          }
        ];

        console.log('Setting plans state with mock data:', mockPlans);
        setPlans(mockPlans);
      } else {
        setPlans(formattedPlans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      setError('Failed to load plans. Please try again later.');

      // Define mock plans directly
      const mockPlans = [
        {
          id: '1',
          name: 'Standard',
          tier: 'STANDARD',
          price: 9.99,
          description: 'Basic access to shared accounts',
          features: [
            'Access to 10+ premium tools',
            'Basic support',
            '1 concurrent login',
            'Standard availability'
          ],
          includedTools: [
            { id: '3', name: 'Microsoft Office 365' },
            { id: '4', name: 'Spotify Premium' },
            { id: '6', name: 'Disney+' }
          ]
        },
        {
          id: '2',
          name: 'Premium',
          tier: 'PREMIUM',
          price: 19.99,
          description: 'Enhanced access to premium accounts',
          features: [
            'Access to 50+ premium tools',
            'Priority support',
            '2 concurrent logins',
            'High availability',
            'Premium tools included'
          ],
          includedTools: [
            { id: '1', name: 'Netflix' },
            { id: '2', name: 'Adobe Creative Cloud' },
            { id: '3', name: 'Microsoft Office 365' },
            { id: '4', name: 'Spotify Premium' },
            { id: '6', name: 'Disney+' }
          ]
        },
        {
          id: '3',
          name: 'Gold',
          tier: 'GOLD',
          price: 29.99,
          description: 'Full access to all premium tools',
          features: [
            'Access to 100+ premium tools',
            '24/7 support',
            '3 concurrent logins',
            'Highest availability',
            'All premium tools included',
            'Early access to new tools'
          ],
          includedTools: [
            { id: '1', name: 'Netflix' },
            { id: '2', name: 'Adobe Creative Cloud' },
            { id: '3', name: 'Microsoft Office 365' },
            { id: '4', name: 'Spotify Premium' },
            { id: '5', name: 'ChatGPT Plus' },
            { id: '6', name: 'Disney+' }
          ]
        }
      ];

      setPlans(mockPlans);
    } finally {
      setIsLoading(false);
    }
  };

  // Filter plans based on search term
  const filteredPlans = plans.filter((plan) => {
    return searchTerm === '' ||
      plan.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.description.toLowerCase().includes(searchTerm.toLowerCase());
  });

  return (
    <div>
      <div className="mb-8">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-600 dark:to-purple-700 rounded-xl p-6 text-white shadow-lg">
          <h2 className="text-2xl font-bold mb-2">
            📦 Available Plans
          </h2>
          <p className="mb-4">
            Select a plan that works best for you and start accessing premium international tools with local payment methods.
          </p>
        </div>
      </div>

      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search plans..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="px-4 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-slate-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
            <button
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400"
              onClick={() => setSearchTerm('')}
            >
              {searchTerm ? <X size={16} /> : <Search size={16} />}
            </button>
          </div>
          <button
            onClick={openCart}
            className="relative bg-yellow-400 hover:bg-yellow-500 text-black font-medium p-2 rounded-lg transition-colors duration-200"
          >
            <ShoppingCart size={20} />
            {cartItemCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                {cartItemCount > 9 ? '9+' : cartItemCount}
              </span>
            )}
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300 rounded-lg flex items-center">
          <span className="text-xl mr-2">ℹ️</span>
          <p>{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <motion.div
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "linear",
            }}
            className="w-12 h-12 border-4 border-yellow-400 border-t-transparent rounded-full"
          />
        </div>
      ) : filteredPlans.length === 0 ? (
        <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md">
          <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
            No plans available
          </p>
          <p className="text-gray-500 dark:text-gray-400 mt-2">
            Check back later for subscription plans.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredPlans.map((plan) => (
            <PlanCard
              key={plan.id}
              plan={plan}
            />
          ))}
        </div>
      )}

      <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md">
        <h3 className="text-xl font-bold mb-4">❓ Frequently Asked Questions</h3>

        <div className="space-y-4">
          <div>
            <h4 className="font-bold mb-2">How does Partagily work?</h4>
            <p className="text-gray-600 dark:text-gray-300">
              Partagily allows you to access premium international tools and services using local Tunisian payment methods. We handle the international payments on your behalf.
            </p>
          </div>

          <div>
            <h4 className="font-bold mb-2">Can I cancel my subscription?</h4>
            <p className="text-gray-600 dark:text-gray-300">
              Yes, you can cancel your subscription at any time. Your access will remain active until the end of your billing period.
            </p>
          </div>

          <div>
            <h4 className="font-bold mb-2">How secure are my payments?</h4>
            <p className="text-gray-600 dark:text-gray-300">
              All payments are processed securely through our trusted local payment partners. We do not store your payment information.
            </p>
          </div>

          <div>
            <h4 className="font-bold mb-2">What happens if a tool increases its price?</h4>
            <p className="text-gray-600 dark:text-gray-300">
              We do our best to maintain stable pricing. If a tool increases its price significantly, we will notify you before making any changes to your subscription.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
