'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, RefreshCw, AlertTriangle } from 'lucide-react';
import adminService from '@/services/adminService';

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const defaultSettings = {
    general: {
      siteName: 'Partagily',
      siteDescription: 'Access premium tools through subscription plans',
      contactEmail: '<EMAIL>',
      enableMaintenance: false,
    },
    security: {
      sessionTimeout: 60,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requirePasswordReset: 90,
    },
    notifications: {
      enableEmailNotifications: true,
      enableAdminAlerts: true,
      notifyOnNewUser: true,
      notifyOnNewSubscription: true,
      notifyOnPaymentFailure: true,
    },
    payment: {
      currency: 'TND',
      enableKonnect: true,
      testMode: true,
      konnectApiKey: '********',
      konnectMerchantId: '********',
    },
  };

  const [settings, setSettings] = useState<any>(defaultSettings);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('Fetching settings from backend API');
        const response = await adminService.getSettings();

        // Make sure we have a valid response with all required sections
        if (response && typeof response === 'object') {
          // Merge the response with default settings to ensure all properties exist
          const mergedSettings = {
            general: { ...defaultSettings.general, ...(response.general || {}) },
            security: { ...defaultSettings.security, ...(response.security || {}) },
            notifications: { ...defaultSettings.notifications, ...(response.notifications || {}) },
            payment: { ...defaultSettings.payment, ...(response.payment || {}) },
          };
          setSettings(mergedSettings);
        } else {
          // If response is invalid, use default settings
          console.warn('Invalid settings response, using defaults');
          setSettings(defaultSettings);
        }
      } catch (err: any) {
        console.error('Error fetching settings:', err);
        setError(err.message || 'Failed to load settings');
        // Keep using default settings on error
        setSettings(defaultSettings);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleChange = (section: string, field: string, value: any) => {
    // Make sure the section exists before updating
    if (!settings[section]) {
      settings[section] = {};
    }

    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [field]: value,
      },
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      console.log('Updating settings with backend API');
      await adminService.updateSettings(settings);
      setSuccessMessage('Settings updated successfully');
    } catch (err: any) {
      console.error('Error updating settings:', err);
      setError(err.message || 'Failed to update settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Check if settings object is properly initialized
  const ensureSettingsStructure = () => {
    const sections = ['general', 'security', 'notifications', 'payment'];
    let needsUpdate = false;
    const updatedSettings = { ...settings };

    sections.forEach(section => {
      if (!updatedSettings[section]) {
        updatedSettings[section] = { ...defaultSettings[section] };
        needsUpdate = true;
      }
    });

    if (needsUpdate) {
      setSettings(updatedSettings);
    }
  };

  // Ensure settings structure is valid before rendering
  useEffect(() => {
    if (!isLoading) {
      ensureSettingsStructure();
    }
  }, [isLoading]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
          className="w-8 h-8 border-2 border-yellow-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  // Additional safety check before rendering the form
  if (!settings || !settings.general || !settings.security ||
      !settings.notifications || !settings.payment) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg">
        <p className="font-medium">Error loading settings</p>
        <p>Please refresh the page to try again.</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 rounded-md"
        >
          Refresh Page
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Platform Settings</h1>
        <button
          type="button"
          onClick={() => window.location.reload()}
          className="inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-1" />
          Refresh
        </button>
      </div>

      {error && (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          <div>
            <p className="font-medium">Warning</p>
            <p>{error}</p>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
          <p className="font-medium">{successMessage}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* General Settings */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">General Settings</h2>
          </div>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 mb-1">
                  Site Name
                </label>
                <input
                  type="text"
                  id="siteName"
                  value={settings.general.siteName}
                  onChange={(e) => handleChange('general', 'siteName', e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  Contact Email
                </label>
                <input
                  type="email"
                  id="contactEmail"
                  value={settings.general.contactEmail}
                  onChange={(e) => handleChange('general', 'contactEmail', e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div className="md:col-span-2">
                <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Site Description
                </label>
                <textarea
                  id="siteDescription"
                  value={settings.general.siteDescription}
                  onChange={(e) => handleChange('general', 'siteDescription', e.target.value)}
                  rows={3}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div className="md:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableMaintenance"
                    checked={settings.general.enableMaintenance}
                    onChange={(e) => handleChange('general', 'enableMaintenance', e.target.checked)}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableMaintenance" className="ml-2 block text-sm text-gray-700">
                    Enable Maintenance Mode
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Security Settings</h2>
          </div>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="sessionTimeout" className="block text-sm font-medium text-gray-700 mb-1">
                  Session Timeout (minutes)
                </label>
                <input
                  type="number"
                  id="sessionTimeout"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  min={1}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div>
                <label htmlFor="maxLoginAttempts" className="block text-sm font-medium text-gray-700 mb-1">
                  Max Login Attempts
                </label>
                <input
                  type="number"
                  id="maxLoginAttempts"
                  value={settings.security.maxLoginAttempts}
                  onChange={(e) => handleChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  min={1}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div>
                <label htmlFor="passwordMinLength" className="block text-sm font-medium text-gray-700 mb-1">
                  Password Minimum Length
                </label>
                <input
                  type="number"
                  id="passwordMinLength"
                  value={settings.security.passwordMinLength}
                  onChange={(e) => handleChange('security', 'passwordMinLength', parseInt(e.target.value))}
                  min={6}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>
              <div>
                <label htmlFor="requirePasswordReset" className="block text-sm font-medium text-gray-700 mb-1">
                  Require Password Reset (days)
                </label>
                <input
                  type="number"
                  id="requirePasswordReset"
                  value={settings.security.requirePasswordReset}
                  onChange={(e) => handleChange('security', 'requirePasswordReset', parseInt(e.target.value))}
                  min={0}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                />
                <p className="mt-1 text-sm text-gray-500">Set to 0 to disable</p>
              </div>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Notification Settings</h2>
          </div>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableEmailNotifications"
                  checked={settings.notifications.enableEmailNotifications}
                  onChange={(e) => handleChange('notifications', 'enableEmailNotifications', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="enableEmailNotifications" className="ml-2 block text-sm text-gray-700">
                  Enable Email Notifications
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableAdminAlerts"
                  checked={settings.notifications.enableAdminAlerts}
                  onChange={(e) => handleChange('notifications', 'enableAdminAlerts', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="enableAdminAlerts" className="ml-2 block text-sm text-gray-700">
                  Enable Admin Alerts
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifyOnNewUser"
                  checked={settings.notifications.notifyOnNewUser}
                  onChange={(e) => handleChange('notifications', 'notifyOnNewUser', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="notifyOnNewUser" className="ml-2 block text-sm text-gray-700">
                  Notify on New User Registration
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifyOnNewSubscription"
                  checked={settings.notifications.notifyOnNewSubscription}
                  onChange={(e) => handleChange('notifications', 'notifyOnNewSubscription', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="notifyOnNewSubscription" className="ml-2 block text-sm text-gray-700">
                  Notify on New Subscription
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifyOnPaymentFailure"
                  checked={settings.notifications.notifyOnPaymentFailure}
                  onChange={(e) => handleChange('notifications', 'notifyOnPaymentFailure', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="notifyOnPaymentFailure" className="ml-2 block text-sm text-gray-700">
                  Notify on Payment Failure
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Settings */}
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Payment Settings</h2>
          </div>
          <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <select
                  id="currency"
                  value={settings.payment.currency}
                  onChange={(e) => handleChange('payment', 'currency', e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                >
                  <option value="TND">Tunisian Dinar (TND)</option>
                  <option value="USD">US Dollar (USD)</option>
                  <option value="EUR">Euro (EUR)</option>
                </select>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="testMode"
                  checked={settings.payment.testMode}
                  onChange={(e) => handleChange('payment', 'testMode', e.target.checked)}
                  className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                />
                <label htmlFor="testMode" className="ml-2 block text-sm text-gray-700">
                  Enable Test Mode
                </label>
              </div>
              <div className="md:col-span-2">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="enableKonnect"
                    checked={settings.payment.enableKonnect}
                    onChange={(e) => handleChange('payment', 'enableKonnect', e.target.checked)}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                  />
                  <label htmlFor="enableKonnect" className="ml-2 block text-sm font-medium text-gray-700">
                    Enable Konnect Payment Gateway
                  </label>
                </div>
                {settings.payment.enableKonnect && (
                  <div className="pl-6 border-l-2 border-yellow-200 space-y-4">
                    <div>
                      <label htmlFor="konnectApiKey" className="block text-sm font-medium text-gray-700 mb-1">
                        Konnect API Key
                      </label>
                      <input
                        type="password"
                        id="konnectApiKey"
                        value={settings.payment.konnectApiKey}
                        onChange={(e) => handleChange('payment', 'konnectApiKey', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="konnectMerchantId" className="block text-sm font-medium text-gray-700 mb-1">
                        Konnect Merchant ID
                      </label>
                      <input
                        type="text"
                        id="konnectMerchantId"
                        value={settings.payment.konnectMerchantId}
                        onChange={(e) => handleChange('payment', 'konnectMerchantId', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  className="w-5 h-5 border-2 border-gray-900 border-t-transparent rounded-full mr-2"
                />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-5 h-5 mr-2" />
                Save Settings
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
