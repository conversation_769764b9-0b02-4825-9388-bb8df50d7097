'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight, Menu, X, ChevronDown, User, ShoppingCart } from 'lucide-react';
import './navbar-styles.css';
import LinkComponent from '../ui/LinkComponent';

const Navbar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const { cartItemCount, openCart } = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleLogout = async () => {
    await logout();
    setIsMenuOpen(false);
  };

  // Render a loading state until client-side hydration is complete
  if (!isMounted) {
    return (
      <div className="navbar">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-2xl font-bold">
              Parta<span className="text-[#FFAD00]">gily</span>
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`navbar ${scrolled ? 'navbar-solid' : 'navbar-transparent'}`}>
      <div className="container mx-auto px-4 flex justify-between items-center">
        <LinkComponent href="/" className="flex items-center">
          <span className="text-2xl font-bold">
            Parta<span className="text-[#FFAD00]">gily</span>
          </span>
        </LinkComponent>

        {/* Desktop Navigation */}
        <div className="hidden md:flex space-x-8 items-center">
          <LinkComponent href="/" className="hover-bounce">
            Home
          </LinkComponent>
          <LinkComponent href="#how-it-works" className="hover-bounce">
            How it Works
          </LinkComponent>
          <LinkComponent href="#features" className="hover-bounce">
            Features
          </LinkComponent>
          <LinkComponent href="#pricing" className="hover-bounce">
            Pricing
          </LinkComponent>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          onClick={toggleMobileMenu}
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Auth Buttons */}
        <div className="hidden md:flex items-center space-x-4 relative">
          {isAuthenticated ? (
            <div className="relative">
              <button
                onClick={toggleMenu}
                className="btn btn-primary btn-sm btn-icon"
              >
                <User size={16} />
                <span>{user?.name}</span>
                <ChevronDown size={16} />
              </button>

              <AnimatePresence>
                {isMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-900 rounded-xl shadow-xl py-2 z-10 border border-gray-100 dark:border-gray-800"
                  >
                    <LinkComponent
                      href="/dashboard"
                      className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Dashboard
                    </LinkComponent>

                    <LinkComponent
                      href="/dashboard/orders"
                      className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Order History
                    </LinkComponent>
                    <LinkComponent
                      href="/account"
                      className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      Account Settings
                    </LinkComponent>
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      Sign Out
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ) : (
            <div className="flex space-x-4">
              <LinkComponent
                href="/signin"
                className="btn btn-outline btn-sm"
              >
                Sign In
              </LinkComponent>
              <LinkComponent
                href="/signup"
                className="btn btn-primary btn-sm btn-icon"
              >
                Sign Up <ArrowRight size={16} />
              </LinkComponent>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
            className="md:hidden bg-white dark:bg-gray-900 shadow-lg border-b border-gray-100 dark:border-gray-800 backdrop-blur-md"
          >
            <div className="container mx-auto px-4 py-6 flex flex-col space-y-5">
              <LinkComponent
                href="/"
                className="py-3 px-2 hover:text-[#FFAD00] dark:text-gray-200 font-medium border-b border-gray-100 dark:border-gray-800 flex items-center"
                onClick={() => setMobileMenuOpen(false)}
              >
                Home
              </LinkComponent>
              <LinkComponent
                href="#how-it-works"
                className="py-3 px-2 hover:text-[#FFAD00] dark:text-gray-200 font-medium border-b border-gray-100 dark:border-gray-800 flex items-center"
                onClick={() => setMobileMenuOpen(false)}
              >
                How it Works
              </LinkComponent>
              <LinkComponent
                href="#features"
                className="py-3 px-2 hover:text-[#FFAD00] dark:text-gray-200 font-medium border-b border-gray-100 dark:border-gray-800 flex items-center"
                onClick={() => setMobileMenuOpen(false)}
              >
                Features
              </LinkComponent>
              <LinkComponent
                href="#pricing"
                className="py-3 px-2 hover:text-[#FFAD00] dark:text-gray-200 font-medium border-b border-gray-100 dark:border-gray-800 flex items-center"
                onClick={() => setMobileMenuOpen(false)}
              >
                Pricing
              </LinkComponent>

              <div className="pt-6 mt-2 border-t border-gray-200 dark:border-gray-700">
                {isAuthenticated ? (
                  <>
                    <LinkComponent
                      href="/dashboard"
                      className="btn btn-primary btn-sm w-full mb-3 justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Dashboard
                    </LinkComponent>

                    <LinkComponent
                      href="/dashboard/orders"
                      className="btn btn-outline btn-sm w-full mb-3 justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Order History
                    </LinkComponent>
                    <button
                      onClick={() => {
                        handleLogout();
                        setMobileMenuOpen(false);
                      }}
                      className="btn btn-outline btn-sm w-full justify-center"
                    >
                      Sign Out
                    </button>
                  </>
                ) : (
                  <div className="flex flex-col space-y-3">
                    <LinkComponent
                      href="/signin"
                      className="btn btn-outline btn-sm w-full justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Sign In
                    </LinkComponent>
                    <LinkComponent
                      href="/signup"
                      className="btn btn-primary btn-sm btn-icon w-full justify-center"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Sign Up <ArrowRight size={16} />
                    </LinkComponent>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Navbar;
