import { Injectable, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AddToCartDto, ItemType } from './dto/add-to-cart.dto';
import { CartResponseDto } from './dto/cart-response.dto';
import { PaymentService } from '../payment/payment.service';

@Injectable()
export class CartService {
  constructor(
    private readonly prisma: PrismaService,
    @Inject(forwardRef(() => PaymentService))
    private readonly paymentService: PaymentService,
  ) {}

  async getCart(userId: string): Promise<CartResponseDto> {
    try {
      // Find or create a cart for the user
      let cart = await this.prisma.cart.findFirst({
        where: {
          userId,
          status: 'OPEN',
        },
        include: {
          items: true,
        },
      });

      if (!cart) {
        cart = await this.prisma.cart.create({
          data: {
            userId,
            status: 'OPEN',
          },
          include: {
            items: true,
          },
        });
      }

      // Calculate total amount
      const totalAmount = cart.items.reduce((sum, item) => sum + item.price, 0);

      return {
        success: true,
        message: 'Cart retrieved successfully',
        data: {
          id: cart.id,
          userId: cart.userId,
          status: cart.status,
          items: cart.items,
          totalAmount,
        },
      };
    } catch (error) {
      console.error('Error getting cart:', error);
      return {
        success: false,
        message: 'Failed to retrieve cart',
      };
    }
  }

  async addToCart(userId: string, addToCartDto: AddToCartDto): Promise<CartResponseDto> {
    try {
      // Find or create a cart for the user
      let cart = await this.prisma.cart.findFirst({
        where: {
          userId,
          status: 'OPEN',
        },
      });

      if (!cart) {
        cart = await this.prisma.cart.create({
          data: {
            userId,
            status: 'OPEN',
          },
        });
      }

      // Check if the item exists
      let itemName = '';
      let itemPrice = 0;

      if (addToCartDto.type === ItemType.TOOL) {
        const tool = await this.prisma.tool.findUnique({
          where: { id: addToCartDto.itemId },
        });

        if (!tool) {
          throw new NotFoundException(`Tool with ID ${addToCartDto.itemId} not found`);
        }

        itemName = tool.name;
        itemPrice = tool.price;
      } else if (addToCartDto.type === ItemType.PLAN) {
        const plan = await this.prisma.plan.findUnique({
          where: { id: addToCartDto.itemId },
        });

        if (!plan) {
          throw new NotFoundException(`Plan with ID ${addToCartDto.itemId} not found`);
        }

        itemName = plan.name;
        itemPrice = plan.price;
      }

      // Add item to cart
      const cartItem = await this.prisma.cartItem.create({
        data: {
          cartId: cart.id,
          type: addToCartDto.type,
          itemId: addToCartDto.itemId,
          name: itemName,
          price: itemPrice,
        },
      });

      // Get updated cart with items
      const updatedCart = await this.prisma.cart.findUnique({
        where: { id: cart.id },
        include: {
          items: true,
        },
      });

      // Calculate total amount
      const totalAmount = updatedCart.items.reduce((sum, item) => sum + item.price, 0);

      return {
        success: true,
        message: 'Item added to cart successfully',
        data: {
          id: updatedCart.id,
          userId: updatedCart.userId,
          status: updatedCart.status,
          items: updatedCart.items,
          totalAmount,
        },
      };
    } catch (error) {
      console.error('Error adding to cart:', error);
      return {
        success: false,
        message: error.message || 'Failed to add item to cart',
      };
    }
  }

  async removeFromCart(userId: string, itemId: string): Promise<CartResponseDto> {
    try {
      // Find the user's cart
      const cart = await this.prisma.cart.findFirst({
        where: {
          userId,
          status: 'OPEN',
        },
      });

      if (!cart) {
        throw new NotFoundException('Cart not found');
      }

      // Check if the item exists before trying to delete it
      const cartItem = await this.prisma.cartItem.findUnique({
        where: { id: itemId },
      });

      if (!cartItem) {
        return {
          success: false,
          message: 'Cart item not found',
        };
      }

      // Remove the item from the cart
      await this.prisma.cartItem.delete({
        where: { id: itemId },
      });

      // Get updated cart with items
      const updatedCart = await this.prisma.cart.findUnique({
        where: { id: cart.id },
        include: {
          items: true,
        },
      });

      // Calculate total amount
      const totalAmount = updatedCart.items.reduce((sum, item) => sum + item.price, 0);

      return {
        success: true,
        message: 'Item removed from cart successfully',
        data: {
          id: updatedCart.id,
          userId: updatedCart.userId,
          status: updatedCart.status,
          items: updatedCart.items,
          totalAmount,
        },
      };
    } catch (error) {
      console.error('Error removing from cart:', error);
      return {
        success: false,
        message: error.message || 'Failed to remove item from cart',
      };
    }
  }

  async checkout(userId: string): Promise<CartResponseDto> {
    try {
      // Find the user's cart
      const cart = await this.prisma.cart.findFirst({
        where: {
          userId,
          status: 'OPEN',
        },
        include: {
          items: true,
        },
      });

      if (!cart || cart.items.length === 0) {
        throw new NotFoundException('Cart is empty or not found');
      }

      // Calculate total amount
      const totalAmount = cart.items.reduce((sum, item) => sum + item.price, 0);

      // Initiate payment with Konnect
      const paymentResponse = await this.paymentService.initiatePayment(userId, {});

      if (!paymentResponse.success) {
        throw new Error(paymentResponse.message || 'Failed to initiate payment');
      }

      return {
        success: true,
        message: 'Payment initiated successfully',
        data: {
          id: cart.id,
          userId: cart.userId,
          status: cart.status,
          items: cart.items,
          totalAmount,
          paymentUrl: paymentResponse.data?.paymentUrl,
        },
      };
    } catch (error) {
      console.error('Error during checkout:', error);
      return {
        success: false,
        message: error.message || 'Failed to complete checkout',
      };
    }
  }
}
